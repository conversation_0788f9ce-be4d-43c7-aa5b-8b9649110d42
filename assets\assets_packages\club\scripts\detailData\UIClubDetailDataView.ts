import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import SearchView from "../members/SearchView";
import UIClubDetailDataView_auto from "./UIClubDetailDataView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubDetailDataView extends UBaseSFixedDialog {

	protected ui: UIClubDetailDataView_auto = null;
	private searchContainer: SearchView = null;

	onUILoad(): void {
		this.ui = new UIClubDetailDataView_auto(this.node);
		this.onRegisterEvent(this.ui.btnBack, this.playExitAnim.bind(this));
		//id搜索
		this.searchContainer = this.ui.SearchLayer.getComponent(UPrefabContainer).getNodeComponent(SearchView);
		//缺省页
		this.initListEmpty("room.noTables");
	}

	onUpdateCell(i: number, node: cc.Node): cc.Node {
		let data = this._roomList[i];
		if (!data) return null;
		if (!node) {
			node = cc.instantiate(this.getItem());
			node.x = cc.winSize.width;
			// let view = node.getComponent(UIItemTable);
			// this.onRegisterEvent(node, () => {
			// 	this.onItemClick(view.getData());
			// });
			// view.setData(data);
			this._itemNodes.push(node);
		} else {
			// let view = node.getComponent(UIItemTable);
			// view.setData(data);
		}
		return node;
	}
}