export default class UIClubExportDataView_auto {
    node:cc.Node = null;   
	ClubExportDataView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	btnTime: cc.Node;
	sprTime: cc.Node;
	btnQuestion: cc.Node;
	sprQuestion: cc.Node;
	SearchLayer: cc.Node;
	labelTimeStart: cc.Node;
	labelTimeEnd: cc.Node;
	labelSeparator: cc.Node;
	SelectExportLayer: cc.Node;
	toggleExportData1: cc.Node;
	labelExportData1: cc.Node;
	BackExportData1: cc.Node;
	labelExportData2: cc.Node;
	toggleExportData2: cc.Node;
	labelExportData3: cc.Node;
	BackExportData3: cc.Node;
	labelExportData4: cc.Node;
	ListView: cc.Node;
	labelClubDataTips: cc.Node;
	labelTransactionDataTips: cc.Node;
	labelDiamondDataTips: cc.Node;
	labelClubData: cc.Node;
	labelTransactionData: cc.Node;
	labelDiamondData: cc.Node;
	diamond1: cc.Node;
	diamond2: cc.Node;
	diamond3: cc.Node;
	ToggleClubData: cc.Node;
	BackClubData: cc.Node;
	checkClubData: cc.Node;
	ToggleTransactionData: cc.Node;
	BackTransactionData: cc.Node;
	checkTransactionData: cc.Node;
	ToggleDiamondData: cc.Node;
	BackDiamondData: cc.Node;
	checkDiamondData: cc.Node;
	TotalLayout: cc.Node;
	labelTotalNum: cc.Node;
	diamond4: cc.Node;
	btnExport: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubExportDataView = this.node;
		this.Mask = this.ClubExportDataView.getChildByName("Mask");
		this.View = this.ClubExportDataView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.btnTime = this.View.getChildByName("btnTime");
		this.sprTime = this.btnTime.getChildByName("sprTime");
		this.btnQuestion = this.View.getChildByName("btnQuestion");
		this.sprQuestion = this.btnQuestion.getChildByName("sprQuestion");
		this.SearchLayer = this.View.getChildByName("SearchLayer");
		this.labelTimeStart = this.View.getChildByName("labelTimeStart");
		this.labelTimeEnd = this.View.getChildByName("labelTimeEnd");
		this.labelSeparator = this.View.getChildByName("labelSeparator");
		this.SelectExportLayer = this.View.getChildByName("SelectExportLayer");
		this.toggleExportData1 = this.SelectExportLayer.getChildByName("toggleExportData1");
		this.labelExportData1 = this.toggleExportData1.getChildByName("labelExportData1");
		this.BackExportData1 = this.toggleExportData1.getChildByName("BackExportData1");
		this.labelExportData2 = this.BackExportData1.getChildByName("labelExportData2");
		this.toggleExportData2 = this.SelectExportLayer.getChildByName("toggleExportData2");
		this.labelExportData3 = this.toggleExportData2.getChildByName("labelExportData3");
		this.BackExportData3 = this.toggleExportData2.getChildByName("BackExportData3");
		this.labelExportData4 = this.BackExportData3.getChildByName("labelExportData4");
		this.ListView = this.View.getChildByName("ListView");
		this.labelClubDataTips = this.ListView.getChildByName("labelClubDataTips");
		this.labelTransactionDataTips = this.ListView.getChildByName("labelTransactionDataTips");
		this.labelDiamondDataTips = this.ListView.getChildByName("labelDiamondDataTips");
		this.labelClubData = this.ListView.getChildByName("labelClubData");
		this.labelTransactionData = this.ListView.getChildByName("labelTransactionData");
		this.labelDiamondData = this.ListView.getChildByName("labelDiamondData");
		this.diamond1 = this.ListView.getChildByName("diamond1");
		this.diamond2 = this.ListView.getChildByName("diamond2");
		this.diamond3 = this.ListView.getChildByName("diamond3");
		this.ToggleClubData = this.ListView.getChildByName("ToggleClubData");
		this.BackClubData = this.ToggleClubData.getChildByName("BackClubData");
		this.checkClubData = this.ToggleClubData.getChildByName("checkClubData");
		this.ToggleTransactionData = this.ListView.getChildByName("ToggleTransactionData");
		this.BackTransactionData = this.ToggleTransactionData.getChildByName("BackTransactionData");
		this.checkTransactionData = this.ToggleTransactionData.getChildByName("checkTransactionData");
		this.ToggleDiamondData = this.ListView.getChildByName("ToggleDiamondData");
		this.BackDiamondData = this.ToggleDiamondData.getChildByName("BackDiamondData");
		this.checkDiamondData = this.ToggleDiamondData.getChildByName("checkDiamondData");
		this.TotalLayout = this.View.getChildByName("TotalLayout");
		this.labelTotalNum = this.TotalLayout.getChildByName("labelTotalNum");
		this.diamond4 = this.TotalLayout.getChildByName("diamond4");
		this.btnExport = this.View.getChildByName("btnExport");

    }
}
