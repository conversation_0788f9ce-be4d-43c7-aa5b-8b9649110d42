{"skeleton": {"hash": "5WoKTsSjc3zF7wQPfZ7IN+CrHcY", "spine": "3.6.38", "width": 150, "height": 527.35, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 312.46, "rotation": 90, "x": -0.58, "y": 50.18}, {"name": "bone2", "parent": "bone", "length": 96.26, "rotation": 179.7, "x": 0.9, "y": 0.35}, {"name": "bone3", "parent": "root", "x": 0.66, "y": 219.02}], "slots": [{"name": "baozha2", "bone": "bone3"}, {"name": "baozha1", "bone": "bone3"}, {"name": "huoyan", "bone": "bone2", "attachment": "huoyan"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "01_01", "bone": "bone3"}], "skins": {"default": {"01_01": {"01_01": {"x": -0.44, "width": 256, "height": 256}, "01_02": {"x": -0.66, "width": 256, "height": 256}, "01_04": {"x": -0.66, "width": 256, "height": 256}, "01_05": {"x": -0.66, "width": 256, "height": 256}, "01_06": {"x": -0.66, "width": 256, "height": 256}, "01_08": {"x": -0.66, "width": 256, "height": 256}, "01_09": {"x": -0.66, "width": 256, "height": 256}, "01_11": {"x": -0.66, "width": 256, "height": 256}, "01_13": {"x": -0.66, "width": 256, "height": 256}, "01_14": {"x": -0.66, "width": 256, "height": 256}, "01_16": {"x": -0.66, "width": 256, "height": 256}}, "baozha1": {"baozha1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [90.31, -70.24, -81.69, -70.24, -81.69, 79.76, 90.31, 79.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 172, "height": 150}, "baozha2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [161.31, -137.24, -192.69, -137.24, -192.69, 155.76, 161.31, 155.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 354, "height": 293}}, "huojiantong": {"huojiantong": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-51.14, -75.67, -51.14, 74.33, 333.86, 74.33, 333.86, -75.67], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 150, "height": 385}}, "huoyan": {"huoyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [194.19, 36.63, 194.55, -31.37, -20.45, -32.51, -20.81, 35.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 68, "height": 215}}}}, "animations": {"baozha": {"slots": {"01_01": {"attachment": [{"time": 0, "name": "01_01"}, {"time": 0.0333, "name": "01_02"}, {"time": 0.0667, "name": "01_04"}, {"time": 0.1, "name": "01_05"}, {"time": 0.1333, "name": "01_06"}, {"time": 0.1667, "name": "01_08"}, {"time": 0.2, "name": "01_09"}, {"time": 0.2333, "name": "01_11"}, {"time": 0.2667, "name": "01_13"}, {"time": 0.3, "name": "01_14"}, {"time": 0.3333, "name": "01_16"}, {"time": 0.3667, "name": null}]}, "huojiantong": {"attachment": [{"time": 0, "name": null}]}, "huoyan": {"attachment": [{"time": 0, "name": null}]}}}, "idle": {"bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0667, "x": 0.488, "y": 0.702}, {"time": 0.1, "x": 1, "y": 1}, {"time": 0.1667, "x": 0.488, "y": 0.702}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.068, "y": 0.068}, {"time": 0.1667, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "idle2": {"bones": {"bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0667, "x": 0.488, "y": 0.702}, {"time": 0.1, "x": 1, "y": 1}, {"time": 0.1667, "x": 0.488, "y": 0.702}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}}}