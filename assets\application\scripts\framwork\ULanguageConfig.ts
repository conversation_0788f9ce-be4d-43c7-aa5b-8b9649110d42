import AppInfo from "../AppInfo";
import PlatUtilsJS from "./platform/js/PlatUtilsJS";
import PlatUtils from "./platform/PlatUtils";

export interface ULanguageConfigObj {
    key: string,
    text: string,
    extKeys?: string[],
}

const LANGUAGE_KEY = "LanguageConfig.KEY"
const DevLanguage = 'en'
const Languages = [
    {
        key: 'cn',
        text: '中文(简体)',
    },
    {
        key: 'en',
        text: 'English',
    },
    {
        key: 'zh',
        extKeys: ['cn'],
        text: '中文(繁體)',
    },
    {
        key: 'vi',
        text: 'Tiếng Anh',
    }
]

/**
* 语言配置
*/
export default class ULanguageConfig {

    private static _instance = null;
    static getInstance(): ULanguageConfig {
        if (!this._instance) {
            this._instance = new ULanguageConfig();
        }
        return this._instance;
    }
    ////////////////////////////////////////////
    //配置数据
    private _languages: ULanguageConfigObj[] = [];
    private _current: ULanguageConfigObj = null;
    //---------
    /**
     * 获取支持的多语种数组
     */
    public get languages(): ULanguageConfigObj[] {
        return this._languages;
    }
    /**
     * 开发语言
     */
    public get devLanguage(): string {
        return DevLanguage;
    }

    /**
     * 获取当前语种
     */
    public get curLanguage(): ULanguageConfigObj {
        return this._current;
    }

    public init() {
        let languages = PlatUtils.getAppConfigValue("languages")
        if (languages) {
            this._languages = [];
            for (let i = 0; i < languages.length; i++) {
                for (let j = 0; j < Languages.length; j++) {
                    if (Languages[j].key == languages[i]) {
                        this._languages.push(Languages[j])
                        break;
                    }
                }
            }
        } else {
            this._languages = Languages;
        }

        //设置当前语言
        let lan = this._languages[0].key
        do {
            if (this._languages.length <= 1) {
                break
            }
            lan = PlatUtilsJS.getLocalStorage(LANGUAGE_KEY);
            if (lan && this.getConfig(lan)) {
                break;
            }
            if (AppInfo.isDev) {
                lan = 'cn';
                if (lan && this.getConfig(lan)) {
                    break;
                }
            }
            lan = PlatUtils.getSysLanguage()
            if (lan && this.getConfig(lan)) {
                break;
            }
            for (let i = 0; i < this._languages.length; i++) {
                if (this._languages[i].extKeys && this._languages[i].extKeys.indexOf(lan) >= 0) {
                    if (this.getConfig(this._languages[i].key)) {
                        lan = this._languages[i].key;
                        break;
                    }
                }
            }
            if (lan && this.getConfig(lan)) {
                break;
            }
            lan = this._languages[0].key;
        } while (false);
        this.setLanguage(lan);
    }

    /**
     * 获取配置
     * @param language 
     */
    private getConfig(language: string): ULanguageConfigObj {
        for (let i = 0; i < this._languages.length; i++) {
            if (this._languages[i].key == language) {
                return this._languages[i];
            }
        }
        return null;
    }

    /**
     * 开发语言
     */
    public getDevLanguages(): string {
        return DevLanguage;
    }

    /**
     * 获取支持的多语种数组
     */
    public getLanguages(): ULanguageConfigObj[] {
        return this._languages;
    }

    /**
     * 获取当前语种
     */
    public getCurLanguage(): ULanguageConfigObj {
        return this._current;
    }

    /**
     * 改变语种
     * @param language 
     */
    public setLanguage(language: string): boolean {
        language = language.toLowerCase();
        if (this._current && language === this._current.key) {
            return false;
        }
        let config = this.getConfig(language)
        if (!config) {
            console.warn("当前不支持该语种" + language);
            return false;
        }
        PlatUtilsJS.setLocalStorage(LANGUAGE_KEY, language);
        this._current = config
        console.log(`当前语言为【${language}】`);
        return true;
    }
}
