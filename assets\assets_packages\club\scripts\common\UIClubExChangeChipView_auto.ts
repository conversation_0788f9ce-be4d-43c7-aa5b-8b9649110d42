export default class UIClubExChangeChipView_auto {
    node:cc.Node = null;   
	ClubExChangeChipView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	btnOK: cc.Node;
	labeOK: cc.Node;
	currencyDiamn: cc.Node;
	arrow_y: cc.Node;
	ChangeDiamondNode: cc.Node;
	cdiamondBG: cc.Node;
	cdiamondIcon: cc.Node;
	EditBoxDiamond: cc.Node;
	TEXT_LABEL: cc.Node;
	PLACEHOLDER_LABEL: cc.Node;
	ChipNode: cc.Node;
	clubChipBg: cc.Node;
	clubChipIcon: cc.Node;
	labelChip: cc.Node;
	TextLayers: cc.Node;
	block1: cc.Node;
	block2: cc.Node;
	block3: cc.Node;
	labelTextTips1: cc.Node;
	labelTextTips2: cc.Node;
	labelTextTips3: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubExChangeChipView = this.node;
		this.Mask = this.ClubExChangeChipView.getChildByName("Mask");
		this.View = this.ClubExChangeChipView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.btnOK = this.View.getChildByName("btnOK");
		this.labeOK = this.btnOK.getChildByName("labeOK");
		this.currencyDiamn = this.View.getChildByName("currencyDiamn");
		this.arrow_y = this.View.getChildByName("arrow_y");
		this.ChangeDiamondNode = this.View.getChildByName("ChangeDiamondNode");
		this.cdiamondBG = this.ChangeDiamondNode.getChildByName("cdiamondBG");
		this.cdiamondIcon = this.ChangeDiamondNode.getChildByName("cdiamondIcon");
		this.EditBoxDiamond = this.ChangeDiamondNode.getChildByName("EditBoxDiamond");
		this.TEXT_LABEL = this.EditBoxDiamond.getChildByName("TEXT_LABEL");
		this.PLACEHOLDER_LABEL = this.EditBoxDiamond.getChildByName("PLACEHOLDER_LABEL");
		this.ChipNode = this.View.getChildByName("ChipNode");
		this.clubChipBg = this.ChipNode.getChildByName("clubChipBg");
		this.clubChipIcon = this.ChipNode.getChildByName("clubChipIcon");
		this.labelChip = this.ChipNode.getChildByName("labelChip");
		this.TextLayers = this.View.getChildByName("TextLayers");
		this.block1 = this.TextLayers.getChildByName("block1");
		this.block2 = this.TextLayers.getChildByName("block2");
		this.block3 = this.TextLayers.getChildByName("block3");
		this.labelTextTips1 = this.TextLayers.getChildByName("labelTextTips1");
		this.labelTextTips2 = this.TextLayers.getChildByName("labelTextTips2");
		this.labelTextTips3 = this.TextLayers.getChildByName("labelTextTips3");

    }
}
