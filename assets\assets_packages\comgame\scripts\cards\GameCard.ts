import BaseCard from "../../../okgame/common/scripts/cards/BaseCard";

const { ccclass, menu, property } = cc._decorator;

@ccclass
@menu("cards/GameCard")
export default class GameCard extends BaseCard {
    @property(cc.Node)
    view: cc.Node = null;
    @property(cc.Node)
    card: cc.Node = null;
    @property(cc.Node)
    cardBg: cc.Node = null;
    @property(cc.Node)
    mask: cc.Node = null;
    //---
    private _isFront: boolean = true;

    onLoad(): void {
    }

    setIsFront(val: boolean) {
        this._isFront = val;
        cc.Tween.stopAllByTarget(this.view);
        this.view.scaleX = 1;
        this.card.active = val;
        this.cardBg.active = !val;
    }

    getIsFront(): boolean {
        return this._isFront;
    }


    flipToFront(delay?: number, cb?: Function) {
        if (this._isFront) {
            cb && cb()
            return;
        }
        this._isFront = true;
        cc.Tween.stopAllByTarget(this.view);
        let anim = cc.tween(this.view).delay(delay).to(0.2, {
            scaleX: 0,
        }).call(() => {
            this.card.active = true;
            this.cardBg.active = false;
        }).to(0.2, {
            scaleX: 1,
        })
        if (cb) {
            anim = anim.call(cb)
        }
        anim.start()
    }

    flipToBack(delay?: number, cb?: Function) {
        if (!this._isFront) {
            return;
        }
        this._isFront = false;
        cc.Tween.stopAllByTarget(this.view);
        let anim = cc.tween(this.view).delay(delay).to(0.2, {
            scaleX: 0,
        }).call(() => {
            this.card.active = false;
            this.cardBg.active = true;
        }).to(0.2, {
            scaleX: 1,
        })
        if (cb) {
            anim = anim.call(cb)
        }
        anim.start()
    }

    setMask(val: boolean) {
        this.mask.active = val;
    }

}
