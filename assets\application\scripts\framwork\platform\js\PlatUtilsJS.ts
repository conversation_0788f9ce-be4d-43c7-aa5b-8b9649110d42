import SelectPhotoJS, { PhotoCallback } from "./SelectPhotoJS";

const DECRYPT_KEY = "okgame3456!@#abc123ABC123%^DDZZA";
declare var okgame;

export default class PlatUtilsJS {
    //-----
    private static _appConfig: any = null;
    private static _queryConfig = null;
    private static _uuid: string = null;
    //------
    public static decryptData(data: string): string {
        try {
            let CryptoJS = require("crypto-js");
            const keyHex = CryptoJS.enc.Utf8.parse(DECRYPT_KEY); // 秘钥
            const ivHex = CryptoJS.enc.Utf8.parse(DECRYPT_KEY.substring(0, 16)); // 偏移量

            let decrypt = CryptoJS.AES.decrypt(data, keyHex, {
                iv: ivHex,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7,
            });
            return decrypt.toString(CryptoJS.enc.Utf8);
        } catch (e) {
            console.error("decryp data error", e.toString());
        }
        return "";
    }

    public static encryptData(data: string): string {
        try {
            let CryptoJS = require("crypto-js");
            const keyHex = CryptoJS.enc.Utf8.parse(DECRYPT_KEY); // 秘钥
            const ivHex = CryptoJS.enc.Utf8.parse(DECRYPT_KEY.substring(0, 16)); // 偏移量

            let encrypt = CryptoJS.AES.encrypt(data, keyHex, {
                iv: ivHex,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7,
            });
            return encrypt.toString();
        } catch (e) {
            console.error("encrypt data error", e.toString());
        }
        return "";
    }

    public static decryptConfig(data: any): any {
        if (!data) {
            return {};
        }
        try {
            if (typeof data != "string") {
                return data;
            }
            var regexp = /^\s*{[\s\S]*}\s*$/;
            if (data.search(regexp) < 0) {
                data = this.decryptData(data);
            }
            return JSON.parse(data);
        } catch (e) { }
        return {};
    }

    public static decryptConfigBase64(data: string): any {
        if (!data || data == '') {
            return {};
        }
        let cryptoJs = require("crypto-js");
        let words = cryptoJs.enc.Base64.parse(data);
        return JSON.parse(words.toString(cryptoJs.enc.Utf8));
    }

    public static setLocalStorage(key: string, value: string) {
        try {
            value = value.toString();
            if (this._appConfig) {
                let md5 = require("md5");
                key = md5(key);
                if (value && value != "") {
                    value = this.encryptData(value);
                }
            }
            cc.sys.localStorage.setItem(key, value);
        } catch (e) {
            console.error(e);
        }
    }

    public static getLocalStorage(key: string): string {
        try {
            if (this._appConfig) {
                let md5 = require("md5");
                key = md5(key);
            }
            let value = cc.sys.localStorage.getItem(key);
            if (value && value != "" && this._appConfig) {
                value = this.decryptData(value);
            }
            return value;
        } catch (e) {
            console.error(e);
        }
        return null;
    }

    public static removeLocalStorage(key: string) {
        try {
            if (this._appConfig) {
                let md5 = require("md5");
                key = md5(key);
            }
            cc.sys.localStorage.removeItem(key);
        } catch (e) {
            console.error(e);
        }
    }

    public static getQueryConfig(): any {
        if (!this._queryConfig) {
            this._queryConfig = {};
            let search = window.location.search;
            if (search.startsWith("?")) {
                search = search.substring(1);
                console.log(search);
                let query = search.split("&");
                for (var i = 0; i < query.length; i++) {
                    let num = query[i].indexOf("=");
                    if (num > 0) {
                        try {
                            let name = query[i].substring(0, num);
                            let value = query[i].substring(num + 1);
                            this._queryConfig[name] = decodeURIComponent(value);
                        } catch (e) { }
                    }
                }
            }
            console.log("queryConfig", this._queryConfig);
        }
        return this._queryConfig;
    }

    /**
     * 系统
     */
    public static get osName(): string {
        if (cc.sys.os == cc.sys.OS_ANDROID) {
            return "Android";
        }
        if (cc.sys.os == cc.sys.OS_IOS) {
            return "IOS";
        }
        return "h5";
    }
    /**
     * 系统类型
     */
    public static get osType(): number {
        //1 h5 2 ios 3 安卓 0暂定为未知设备 10以上为壳包
        if (!cc.sys.isNative) {
            return 1;
        }
        if (cc.sys.os == cc.sys.OS_IOS) {
            return 2;
        }
        if (cc.sys.os == cc.sys.OS_ANDROID) {
            return 3;
        }
        return 0;
    }

    public static get packageName(): string {
        if (cc.sys.platform == cc.sys.WECHAT_GAME) {
            return "Wechat";
        }
        if (cc.sys.platform == cc.sys.QQ_PLAY) {
            return "QQ";
        }
        if (cc.sys.platform == cc.sys.OPPO_GAME) {
            return "Oppo";
        }
        if (cc.sys.platform == cc.sys.VIVO_GAME) {
            return "Vivo";
        }
        return "";
    }

    /**
     * 唯一id
     */
    public static get deviceId(): string {
        if (!this._uuid) {
            this._uuid = this.getLocalStorage("__DEVICEID__");
        }
        if (!this._uuid) {
            try {
                let fingerprintjs = new cc.js.fingerprintjs();
                let key = fingerprintjs.get();
                if (key) {
                    this._uuid = `WEB-FP-${key}`;
                    console.log(this._uuid);
                    this.setLocalStorage("__DEVICEID__", this._uuid);
                }
            } catch (e) {
                console.error(e);
            }
        }
        if (!this._uuid) {
            var d = new Date().getTime();
            if (window.performance && typeof window.performance.now === "function") {
                d += performance.now();
            }
            this._uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
            });
            this.setLocalStorage("__DEVICEID__", this._uuid);
        }
        return this._uuid;
    }

    public static get timeZone(): string {
        let formatter = new Intl.DateTimeFormat();
        return formatter.resolvedOptions().timeZone;
    }

    //*********************分平台接口********************/
    public static init() {
        console.log("PlatUtilsJS Init");
        // 初始化应用配置
        try {
            if (okgame && okgame.appConfig) {
                this._appConfig = this.decryptConfig(okgame.appConfig);
            }
        } catch (e) {
        }
    }

    /**
     * 应用配置
     */
    public static getAppConfig() {
        return this._appConfig;
    }

    /**
     * 设备信息
     */
    public static getDeviceInfo() {
        return {
            packageName: this.packageName,
            appName: "",
            osType: this.osType,
            osName: this.osName,
            osVersion: "1.0",
            mobileName: `${cc.sys.browserType}`,
            deviceId: this.deviceId,
            timeZone: this.timeZone,
        };
    }

    /**
     * 打开相册
     */
    public static openPhotoAlbum(limitSize: number, cb: PhotoCallback) {
        SelectPhotoJS.selectPhoto(limitSize, cb)
    }
    /**
     * 打开摄像机
     */
    public static openCamera(limitSize: number, cb: PhotoCallback) {
        SelectPhotoJS.selectPhoto(limitSize, cb)
    }

    /**
     * 复制文字
     * @param text 
     * @param cb 
     */
    static setSysClipboardText(text: string, cb?: Function) {
        var textArea = document.getElementById('clipBoard');
        if (textArea === null) {
            textArea = document.createElement('textarea');
            textArea.id = 'clipBoard';
            textArea.textContent = text;
            document.body.appendChild(textArea);
        }
        textArea['select']();
        try {
            let isSuccess = document.execCommand('copy');
            cb && cb(isSuccess ? 0 : -1);
            document.body.removeChild(textArea);
        } catch (err) {
            cb && cb(-1);
        }
    }


    /**
     * 获取剪贴板文字
     */
    public static getSysClipboardText(cb: Function) {
        if (CC_DEBUG) {
            cb && cb('[OKGAME]:/OpenApp...iv=178118')
        }
    }

    /**
     * 获取启动参数
     */
    public static getStartupParam(): any {
        return null
    }

    /**
     * 上报
     */
    public static reportByParam(param) {
        if (param.adKey) {
            //ReportByAdjust.report(param.adKey, param.adParam)
        }
    }
}
