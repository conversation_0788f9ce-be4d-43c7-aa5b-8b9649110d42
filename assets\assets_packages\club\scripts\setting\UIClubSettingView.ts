import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import Club<PERSON>anger, { CLUB_UIID } from "../ClubManger";
import UIClubSettingCareerCell, { ICareerCellData } from "./UIClubSettingCareerCell";
import UIClubSettingFuncCell, { IFuncCellData } from "./UIClubSettingFuncCell";
import UIClubSettingView_auto from "./UIClubSettingView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubSettingView]";

const CareerConfig: ICareerCellData[] = [
    {
        title: "club.careerServiceFee",
        today: 0,
        week: 0,
        lastWeek: 0,
        total: 0,
    },
    {
        title: "club.careerTotalGames",
        today: 0,
        week: 0,
        lastWeek: 0,
        total: 0,
    },
    {
        title: "club.careerPlayerProfitLoss",
        today: 0,
        week: 0,
        lastWeek: 0,
        total: 0,
    },
    {
        title: "club.careerInsuranceEV",
        today: 0,
        week: 0,
        lastWeek: 0,
        total: 0,
    },
];

const FuncConfig: IFuncCellData[] = [
    {
        title: "club.level",
        viewPath: CLUB_UIID.ClubLevelView,
    },
    {
        title: "club.career",
        viewPath: CLUB_UIID.ClubCareerView,
    },
    {
        title: "club.inform",
        viewPath: CLUB_UIID.ClubInformView,
    },
    {
        title: "club.message",
        viewPath: CLUB_UIID.ClubMessageView,
    },
    {
        title: "club.fee",
        viewPath: CLUB_UIID.ClubFeeView,
    },
    {
        title: "club.notice",
        viewPath: CLUB_UIID.ClubNoticeView,
    },
    {
        title: "club.poster",
        viewPath: CLUB_UIID.ClubPosterView,
    },
];

/**
 * 俱乐部设置
 *
 * @export
 * @class UIClubSettingView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubSettingView")
export default class UIClubSettingView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubSettingView_auto = null;

    @property({ type: cc.Prefab, tooltip: "生涯记录" })
    private careerCell: cc.Prefab = null;

    @property({ type: cc.Prefab, tooltip: "功能列表" })
    private funcCell: cc.Prefab = null;

    /**
     * 视图路径
     *
     * @private
     * @type {string}
     * @memberof UIClubSettingView
     */
    private viewPath: string = null;

    /**
     * 点击回调
     *
     * @private
     * @memberof UIClubSettingView
     */
    private onClickCb: (viewPath: string) => void = null;

    //#endregion

    //#region 生命周期
    onUILoad(): void {
        this.ui = new UIClubSettingView_auto(this.node);

        this.initCareerList();
        this.initFuncList();

        this.onRegisterEvent(this.ui.backButton, this.onClickBackBtn.bind(this));
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubSettingView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化生涯记录列表
     *
     * @private
     * @memberof UIClubSettingView
     */
    private initCareerList() {
        this.ui.careerDataNode.removeAllChildren();
        for (let i = 0; i < CareerConfig.length; i++) {
            const model = CareerConfig[i];
            const cellNode = cc.instantiate(this.careerCell);
            this.ui.careerDataNode.addChild(cellNode);
            const careerCell = cellNode.getComponent(UIClubSettingCareerCell);
            if (careerCell && careerCell.setCellData) {
                careerCell.setCellData(model);
            } else {
                cc.error("未找到 UIMoreSetFunc 组件");
            }
        }
    }

    private initFuncList() {
        this.ui.funcContainer.removeAllChildren();
        for (let i = 0; i < FuncConfig.length; i++) {
            const model = FuncConfig[i];
            const cellNode = cc.instantiate(this.funcCell);
            this.ui.funcContainer.addChild(cellNode);
            const funcCell = cellNode.getComponent(UIClubSettingFuncCell);
            if (funcCell && funcCell.setCellData) {
                funcCell.setCellData(i, model, this.onClickCell);
            } else {
                cc.error("未找到 UIClubSettingFuncCell 组件");
            }
        }
    }
    //#endregion

    //#region 事件处理
    /**
     * 点击返回按钮
     *
     * @private
     * @memberof UIClubSettingView
     */
    private onClickBackBtn() {
        this.closeDialog();
    }

    /**
     * 点击单元格
     *
     * @private
     * @memberof UIClubSettingView
     */
    private onClickCell(viewPath: string) {
        this.log(`点击单元格: ${viewPath}`);
        ClubManger.getInstance().showClubDialog(viewPath);
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
