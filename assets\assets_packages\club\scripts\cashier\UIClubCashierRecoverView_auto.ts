export default class UIClubCashierRecoverView_auto {
    node:cc.Node = null;   
	ClubCashierRecovrView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	maxNumberLayout: cc.Node;
	labelTips: cc.Node;
	labelMaxNum: cc.Node;
	EditBoxNum: cc.Node;
	BACKGROUND_SPRITE: cc.Node;
	TEXT_LABEL: cc.Node;
	PLACEHOLDER_LABEL: cc.Node;
	labelPeopleNum: cc.Node;
	EditBoxMask: cc.Node;
	ScrollView: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;
	totalLayout: cc.Node;
	labelTips2: cc.Node;
	labelTotal: cc.Node;
	recevorAllLayout: cc.Node;
	toggleSelect: cc.Node;
	Background: cc.Node;
	checkmark: cc.Node;
	labelTips3: cc.Node;
	btnOK: cc.Node;
	labeOK: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierRecovrView = this.node;
		this.Mask = this.ClubCashierRecovrView.getChildByName("Mask");
		this.View = this.ClubCashierRecovrView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.maxNumberLayout = this.View.getChildByName("maxNumberLayout");
		this.labelTips = this.maxNumberLayout.getChildByName("labelTips");
		this.labelMaxNum = this.maxNumberLayout.getChildByName("labelMaxNum");
		this.EditBoxNum = this.View.getChildByName("EditBoxNum");
		this.BACKGROUND_SPRITE = this.EditBoxNum.getChildByName("BACKGROUND_SPRITE");
		this.TEXT_LABEL = this.EditBoxNum.getChildByName("TEXT_LABEL");
		this.PLACEHOLDER_LABEL = this.EditBoxNum.getChildByName("PLACEHOLDER_LABEL");
		this.labelPeopleNum = this.EditBoxNum.getChildByName("labelPeopleNum");
		this.EditBoxMask = this.EditBoxNum.getChildByName("EditBoxMask");
		this.ScrollView = this.View.getChildByName("ScrollView");
		this.view = this.ScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.ScrollView.getChildByName("listEmpty");
		this.totalLayout = this.View.getChildByName("totalLayout");
		this.labelTips2 = this.totalLayout.getChildByName("labelTips2");
		this.labelTotal = this.totalLayout.getChildByName("labelTotal");
		this.recevorAllLayout = this.View.getChildByName("recevorAllLayout");
		this.toggleSelect = this.recevorAllLayout.getChildByName("toggleSelect");
		this.Background = this.toggleSelect.getChildByName("Background");
		this.checkmark = this.toggleSelect.getChildByName("checkmark");
		this.labelTips3 = this.recevorAllLayout.getChildByName("labelTips3");
		this.btnOK = this.View.getChildByName("btnOK");
		this.labeOK = this.btnOK.getChildByName("labeOK");

    }
}
