import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UISelectBox from "../../../okgame/common/scripts/SelectBox/UISelectBox";
import { HORIZONTAL_ALIGN, OptionsItemMode, SORT_TYPE, VERTICAL_ALIGN } from "../../../okgame/common/scripts/SelectBox/UISelectBoxOptions";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import ClubManger, { CLUB_EVENT, CLUB_UIID } from "../ClubManger";
import { SortTypeEnum } from "../members/MemberConst";
import MemberHelper from "../members/MemberHelper";
import { PermissionUtils } from "../members/PermissionUtils";
import SearchView from "../members/SearchView";
import UIClubCashierTransactionLayer_auto from "./UIClubCashierTransactionLayer_auto";

export interface ClubSelectUserItemData {
	data: okpb.ClubUserPO,
	isSelect: boolean,
}

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierTransactionLayer extends UBaseSFixedDialog {

	protected ui: UIClubCashierTransactionLayer_auto = null;
	private counterInfo: any = null;
	private searchContainer: SearchView = null;
	private sortField: SortTypeEnum = SortTypeEnum.ChipBalance;
	private sortDirection: SORT_TYPE = SORT_TYPE.ASC;
	private sortIdentity: boolean = false;
	private searchContent: string = "";

	onUILoad(): void {
		super.onUILoad();
		this.ui = new UIClubCashierTransactionLayer_auto(this.node);
		//id搜索
		this.searchContainer = this.ui.SearchLayer.getComponent(UPrefabContainer).getNodeComponent(SearchView);
		//缺省页
		this.initListEmpty("room.noTables");

		//打开筹码转换
		this.onRegisterEvent(this.ui.btnCashierBalance, this.onBtnCashierBalance.bind(this));
		this.onRegisterEvent(this.ui.btnChipAdd, this.onBtnCashierBalance.bind(this));
		this.onRegisterEvent(this.ui.btnCashierAgent, this.onBtnCashierAgent.bind(this));
		this.onRegisterEvent(this.ui.btnCashierChip, this.onBtnCashierChip.bind(this));
		this.onRegisterEvent(this.ui.btnRecevor, this.onBtnRecevor.bind(this));
		this.onRegisterEvent(this.ui.btnTicket, this.onBtnTicket.bind(this));
		this.onRegisterEvent(this.ui.btnSend, this.onBtnSend.bind(this));

		//柜台筹码相关数据更新
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO, this.updateClubCounterInfo.bind(this));
		//所有筹码更新变动，刷新列表信息
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_USER_CHIP_BALANCE, this.reqData.bind(this));
		//更新个人信息，当前只有个人筹码显示
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_USER_SELF_INFO, this.updateClubUserSelfInfo.bind(this));

		this.initSort();
		this.initMemberList();
		this.updateClubCounterInfo();
	}

	initData(data: any) {

	}

	onEnable(): void {
		ClubDataManager.getInstance().requestClubUserSelfInfo();
	}

	initSort() {
		let selectBox: UISelectBox = this.ui.selectSortBox.getComponent(UPrefabContainer).getNodeComponent(UISelectBox);
		let options = [
			ULanguage.getInstance().getLangByID("club.chips"),
			ULanguage.getInstance().getLangByID("club.joinTime"),
		];
		selectBox.init(options, options[0], (_sortFiled: number, value: number | string, _sortDirection: SORT_TYPE) => {
			this.sortField = _sortFiled == 0 ? SortTypeEnum.ChipBalance : SortTypeEnum.JoinClubTime;
			this.sortDirection = _sortDirection;
			this.refreshListView();
		}, OptionsItemMode.SORT, VERTICAL_ALIGN.DOWN, HORIZONTAL_ALIGN.RIGHT);
	}

	initMemberList() {
		let clubInfo = ClubDataManager.getInstance().getClubInfo();
		if (!clubInfo) return;
		//  回收权限
		let hasTakeChipsPermission = PermissionUtils.hasTakeChipsPermission(clubInfo.permissions);
		//  发放权限
		let hasSendChipsPermission = PermissionUtils.hasSendChipsPermission(clubInfo.permissions);
		//回收和发放的权限控制按钮是否显示
		this.ui.btnRecevor.active = hasTakeChipsPermission;
		this.ui.btnSend.active = hasSendChipsPermission;

		let memberList = ClubDataManager.getInstance().getMemberList();
		if (!memberList || memberList.length == 0) {
			ClubDataManager.getInstance().requestMembersAndAgetnAndMark(clubInfo.clubId, () => {
				this.requestClubUserList();
			});
			return;
		}
		this.requestClubUserList();
	}

	requestClubUserList() {
		let memberList = ClubDataManager.getInstance().getMemberList();
		if (!memberList || memberList.length == 0) {
			//缺省页显示
			this.updateListData({ errorCode: 0 }, []);
			return;
		}
		//更新列表里所有的筹码信息
		ClubDataManager.getInstance().requestClubAllUserChipBalance();
	}

	updateClubCounterInfo() {
		let data = ClubDataManager.getInstance().getClubCounterInfo();
		this.counterInfo = data.data;
		if (this.counterInfo) {
			//若是创建者或管理员，显示数据类型1
			if (data.identity == okpb.Identity.IDE_MANAGER || data.identity == okpb.Identity.IDE_MASTER) {
				this.ui.DataLayer.active = true;
				this.ui.btnCurChips.active = false;
				this.ui.labelAgent.getComponent(cc.Label).string = "" + data.data.agentCreditChips;
			} else if (data.identity == okpb.Identity.IDE_AGENT) {
				this.ui.DataLayer.active = true;
				this.ui.btnCashierAgent.active = false;
			}
			this.ui.labelCashierBalance.getComponent(cc.Label).string = "" + data.data.counterChips;
			this.ui.labelChips.getComponent(cc.Label).string = "" + data.data.memberChips;
		}
	}

	updateClubUserSelfInfo() {
		let data = ClubDataManager.getInstance().getClubUserSelfInfo();
		if (data && data.identity == okpb.Identity.IDE_AGENT) {
			this.ui.labelHoldChips.getComponent(cc.Label).string = "" + data.chipBalance;
		}
	}

	/**
	* 获取服务器成员列表
	* 重要: 0616决定不采用分页模式，请求接口全部返回后，本地做排序处理
	* 另外: 涉及到几千上万个俱乐部成员的话，后期再做处理，目前先不考虑
	*/
	async reqData() {
		if (!cc.isValid(this.node)) return;
		//没有玩家列表时，显示缺省页
		let memberList = ClubDataManager.getInstance().getMemberList();
		if (memberList.length == 0) {
			//显示缺省页
			this.updateListData({ errorCode: 0 }, []);
			return;
		}
		this.refreshListView();
		this.initSearch();
	}

	initSearch() {
		let placHolderTxt = ULanguage.getInstance().getLangByID("club.searchMember") + "  (" + this.listData.length + ")";
		this.searchContainer.initData(placHolderTxt, (content: string) => {
			this.searchContent = content;
			this.refreshListView();
		});
	}

	refreshListView() {
		let dataList = [];
		let cashierMemeberList = ClubDataManager.getInstance().getMemberList();
		let sortMemberList = MemberHelper.sortMemberList(cashierMemeberList, this.sortIdentity, this.sortField, this.sortDirection);
		for (let i = 0; i < sortMemberList.length; i++) {
			dataList.push({
				data: sortMemberList[i],
				isSelect: false,
			});
		}
		this.updateListData({ errorCode: 0 }, dataList);
	}

	updateSelectUserChips() {
		return new Promise(async (resolve, reject) => {
			let userIds = [];
			for (let i = 0; i < this.listData.length; i++) {
				if (this.listData[i].isSelect) {
					userIds.push(this.listData[i].data);
				}
			}
			if (userIds.length == 0) return resolve(false);

			let data: okpb.RspClubUserChipBalance = await ClubAPI.getInstance().ReqClubUserChipBalance(userIds);
			if (!cc.isValid(this.node)) return resolve(false);
			if (data && data.errorCode == okpb.ERET.OK) {
				//更新筹码后，再次重新获取最新数据
				ClubDataManager.getInstance().updateChipBalanceList(data.balances);
				for (let m = 0; m < data.balances.length; m++) {
					let curIndex = this.listData.findIndex((item: any) => { return data.balances[m].userId == item.data.userId });
					if (curIndex >= 0) {
						this.listData[curIndex].data.chipBalance = data.balances[m].balance;
					}
				}
				return resolve(true);
			}
			return resolve(false);
		});
	}

	//点击筹码余额，打开兑换筹码页面
	onBtnCashierBalance() {
		ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubExChangeChipView, this.counterInfo.diamondUnit, this.counterInfo.exchangeChips);
	}

	//点击代理授信，打开代理授信页面
	onBtnCashierAgent() {
		let clubInfo = ClubDataManager.getInstance().getClubInfo();
		if (clubInfo.identity > okpb.Identity.IDE_AGENT) {
			ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubCashierAgentCreditView);
		}
	}

	//当前持有筹码，暂无相关内容
	onBtnCashierChip() {
		// ClubManger.getInstance().showClubDialog("");
	}

	//按身份进行排序
	onToggleSortSelect(toggle: cc.Toggle) {
		this.sortIdentity = toggle.isChecked;
		this.refreshListView();
	}

	//收回
	async onBtnRecevor() {
		await this.updateSelectUserChips();
		let userList = [];
		let alllChips = 0;
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) {
				userList.push(this.listData[i].data);
				alllChips += this.listData[i].data.chipBalance;
			}
		}

		//当前没有选择人数，不触发回收弹窗
		if (userList.length <= 0) {
			return;
		}

		//当前回收筹码总额为0，不触发回收弹窗
		if (alllChips <= 0) {
			return;
		}
		ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubCashierRecoverView, userList);
	}

	//送票
	onBtnTicket() {

	}

	//发放
	async onBtnSend() {
		await this.updateSelectUserChips();
		let userList = [];
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) {
				userList.push(this.listData[i].data);
			}
		}
		//当前没有选择人数，不触发发放弹窗
		if (userList.length <= 0) {
			return;
		}
		ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubCashierSendView, userList);
	}

}