
export type PhotoCallback = (code: number, base64?: string) => void;

export default class SelectPhotoJS {
    private static inputEl: HTMLInputElement | null = null;
    /**
     * 清理输入框中的内容
     *
     * 如果输入框存在，则将其内容清空
     */
    private static cleanup() {
        if (this.inputEl) {
            this.inputEl.value = "";
            this.inputEl = null;
        }
    }

    /**
     * 处理图片的方法
     *
     * @param base64 图片的base64编码字符串
     * @param cb 处理完成后的回调函数，回调参数为(code: number, result: string)，其中code为处理结果码，result为处理后的图片base64编码字符串
     */
    private static processImage(base64: string, cb: PhotoCallback, limitSize: number) {
        if (!base64 || typeof base64 !== "string") {
            cb(-1);
            return;
        }
        if (limitSize <= 0) {
            cb(0, base64);
            return;
        }

        const img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = base64;

        const loadTimeout = setTimeout(() => {
            cb(-1);
            img.onload = img.onerror = null;
        }, 10000);

        img.onload = () => {
            clearTimeout(loadTimeout);

            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");
            if (!ctx) {
                cb(-1);
                return;
            }
            limitSize = Math.min(limitSize, img.width);
            limitSize = Math.min(limitSize, img.height);
            canvas.width = limitSize;
            canvas.height = limitSize;

            try {
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                cb(0, canvas.toDataURL("image/jpeg", 0.9));
            } catch (e) {
                cb(-1);
            }
        };

        img.onerror = () => {
            clearTimeout(loadTimeout);
            cb(-1);
        };
    }

    /**
     * 选择照片的方法
     *
     * @param cb 回调函数，用于处理选择的照片
     * @param limit 限制大小
     */
    public static selectPhoto(limitSize: number, cb: PhotoCallback) {
        if (!this.inputEl) {
            this.inputEl = document.createElement("input");
            this.inputEl.id = "file_input";
            this.inputEl.type = "file";
            this.inputEl.className = "fileToUpload";
            this.inputEl.style.opacity = "0";
            this.inputEl.style.position = "absolute";
            this.inputEl.style.left = "-999px";
            this.inputEl.accept = "image/*,.bnp,.png,.jpg,.jpeg,.webp";
            document.body.appendChild(this.inputEl);
        }

        let dialogClosed = false;
        const handleChange = (event: Event) => {
            dialogClosed = true;
            if (!this.inputEl?.files || this.inputEl.files.length === 0) {
                this.cleanup();
                return;
            }

            const file = this.inputEl.files[0];
            try {
                if (!file.type.match(/image\/(png|jpeg|jpg|webp)|\.bnp/i)) {
                    cb(100);
                    this.cleanup();
                    return;
                }

                // if (file.size > 5 * 1024 * 1024) {
                //     cb(101);
                //     this.cleanup();
                //     return;
                // }

                const fileReader = new FileReader();
                fileReader.onload = (e) => {
                    if (fileReader.readyState === FileReader.DONE) {
                        const originalBase64 = e.target?.result as string;
                        this.processImage(originalBase64, cb, limitSize);
                    }
                    this.cleanup();
                };
                fileReader.onerror = () => {
                    cb(-1);
                    this.cleanup();
                };
                fileReader.readAsDataURL(file);
            } catch (e) {
                cb(-1);
                this.cleanup();
            }
        };

        // PC 端：检测取消（focus + setTimeout）
        if (cc.sys.isBrowser) {
            window.addEventListener(
                "focus",
                () => {
                    setTimeout(() => {
                        if (!dialogClosed && this.inputEl?.files?.length === 0) {
                            cb(-1); // 用户取消
                            this.cleanup();
                            dialogClosed = true;
                        }
                    }, 500);
                },
                { once: true }
            );
        }

        this.inputEl.addEventListener("change", handleChange, { once: true });
        this.inputEl.click();
    }
}
