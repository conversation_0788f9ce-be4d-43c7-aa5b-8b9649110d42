import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import ItemClubAvator from "../../../okgame/club/scripts/ItemClubAvator";
import { okpb } from "../../../okgame/proto/proto_msg";

const { ccclass, property } = cc._decorator;

const TAG = "ProfilePreviewDialog";

@ccclass
export default class ProfilePreviewDialog extends UBaseDialog {
    @property(cc.Label)
    clubNameLabel: cc.Label = null;

    @property(cc.Label)
    clubIDLabel: cc.Label = null;

    @property(cc.Label)
    clubNoticeLabel: cc.Label = null;

    @property(cc.Node)
    clubAvatarContainer: cc.Node = null;

    @property(cc.Button)
    btnClose: cc.Button = null;

    @property(cc.Button)
    btnConfirm: cc.Button = null;

    onUILoad(): void {
        this.onRegisterEvent(this.btnClose.node, this.closeDialog.bind(this));
        this.onRegisterEvent(this.btnConfirm.node, this.closeDialog.bind(this));
    }

    start() {}

    onShow(clubInfo: okpb.ClubResp): void {
        console.log(TAG, "onShow 显示俱乐部信息预览弹窗");
        this.clubNameLabel.string = clubInfo.clubName;
        this.clubIDLabel.string = "ID: " + clubInfo.clubId;
        if (clubInfo.intro) {
            this.clubNoticeLabel.string = clubInfo.intro;
        } else {
            this.clubNoticeLabel.string = ULanguage.getInstance().getLangByID("club.welcomeToOKPoker");
        }

        // 显示头像
        const prefabContainer = this.clubAvatarContainer.getComponent(UPrefabContainer);
        if (!prefabContainer) {
            cc.warn(TAG, `缺少 UPrefabContainer 组件`);
            return;
        }
        const script: ItemClubAvator = prefabContainer.getNodeComponent("ItemClubAvator");
        if (!script) {
            cc.warn(TAG, `缺少 ItemClubAvator 组件`);
            return;
        }
        script.setAvatorId(clubInfo.icon, () => {});
        script.showNormalMask(true);
    }
}
