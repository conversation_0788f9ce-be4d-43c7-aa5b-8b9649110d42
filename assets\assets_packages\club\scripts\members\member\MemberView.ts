import { ULanguage } from "../../../../../framwork/language/ULanguage";
import UBaseView from "../../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../../framwork/widget/UPrefabContainer";
import UISelectBox from "../../../../okgame/common/scripts/SelectBox/UISelectBox";
import { HORIZONTAL_ALIGN, OptionsItemMode, SORT_TYPE, VERTICAL_ALIGN } from "../../../../okgame/common/scripts/SelectBox/UISelectBoxOptions";
import { UIAlertDialogOptions } from "../../../../okgame/common/scripts/UIAlertDialog";
import UIListEmpty from "../../../../okgame/common/scripts/UIListEmpty";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameData from "../../../../okgame/public/OKGameData";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import ClubAPI from "../../ClubAPI";
import ClubDataManager from "../../ClubDataManager";
import { SortTypeEnum } from "../MemberConst";
import MemberHelper from "../MemberHelper";
import { IClubCompleteInfo, IClubMemberFullInfo } from "../MemberTypes";
import SearchView from "../SearchView";
import ItemMemberView from "./ItemMemberView";

const { ccclass, property } = cc._decorator;

const TAG = "[MemberView]";

@ccclass
export default class MemberView extends UBaseView {
    @property({ type: cc.Node, tooltip: "搜索container" })
    searchContainerNode: cc.Node = null;

    @property({ type: cc.Node, tooltip: "排序menu container" })
    sortMenuContainerNode: cc.Node = null;

    @property({ type: cc.Toggle, tooltip: "按身份分组Toggle" })
    roleToggle: cc.Toggle = null;

    @property({ type: cc.Label, tooltip: "" })
    roleDescLabel: cc.Label = null;

    @property({ type: cc.ScrollView, tooltip: "成员列表" })
    ScrollView: cc.ScrollView = null;

    @property({ type: cc.Prefab, tooltip: "成员列表item" })
    itemMemberView: cc.Prefab = null;

    @property({ type: cc.Button, tooltip: "删除按钮" })
    deleteBtn: cc.Button = null;

    @property({ type: cc.Label, tooltip: "删除数量" })
    delCountLabel: cc.Label = null;

    @property({ type: cc.Node, tooltip: "空数据Node" })
    emptyDataNode: cc.Node = null;

    /**
     * 是否通过身份分组
     */
    private isSortRole: boolean = false;
    /**
     * 排序字段: 服务费、盈亏、手牌数、上次登录、上次玩牌
     */
    private sortField: SortTypeEnum = SortTypeEnum.ServiceFee;
    /**
     * 正序、倒叙
     */
    private sortDirection: SORT_TYPE = SORT_TYPE.DESC;
    /**
     * 成员列表
     */
    private membersList: IClubMemberFullInfo[] = [];
    /**
     * 搜索结果列表
     */
    private searchResultList: IClubMemberFullInfo[] = [];
    /**
     * 总人数
     */
    private membersCount = 0;
    /**
     * 俱乐部信息
     */
    private clubInfo: IClubCompleteInfo | null = null;
    private myUserId: number = 0;
    private myRole: okpb.Identity = okpb.Identity.IDE_GENERAL;
    /**
     * 用户当前身份，用于决定itemView是否展示复选框
     */
    private userRole: number = 0;
    /**
     * 选中的成员ids,用于删除功能
     */
    private selectedMemberIds: number[] = [];
    /**
     * 防抖相关的变量
     */
    private isRequesting: boolean = false;
    private debounceTimer: NodeJS.Timeout | null = null;
    private readonly DEBOUNCE_DELAY: number = 300;
    /**
     * 在线人数的回调
     */
    private onlineCallback: Function = null;

    onUILoad(): void {
        this.initView();
        this.initEvent();
    }

    onUIDestroy(): void {
        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
        super.onUIDestroy();
    }

    private initView(): void {
        this.deleteBtn.node.active = false;
        this.emptyDataNode.active = false;

        this.onRegisterEvent(this.roleDescLabel.node, this.onRoleDescLabelClick.bind(this));
    }

    private onRoleDescLabelClick(): void {
        console.log(TAG, "onRoleDescLabelClick");
        this.roleToggle.isChecked = !this.roleToggle.isChecked;
        this.roleToggleClick();
    }

    private initEvent(): void {
        this.onRegisterEvent(this.deleteBtn.node, this.onDeleteBtnClick.bind(this));
        this.roleToggle.isChecked = false;
        this.roleToggle.node.on("toggle", this.roleToggleClick.bind(this));
    }

    /**
     * 初始化分页数据
     */
    private initData(): void {
        this.sortDirection = SORT_TYPE.DESC;
        this.membersList = [];
        this.membersCount = 0;
    }

    /**
     * 设置俱乐部信息
     */
    public showMemberView(onlineCallback?: Function): void {
        this.onlineCallback = onlineCallback;
        this.clubInfo = ClubDataManager.getInstance().getClubInfo();
        this.myRole = this.clubInfo.identity;
        this.myUserId = OKGameData.getInstance().getUserId();
        this.initData();
        // 搜索UI
        this.searchContainerNode.active = true;
        const searchScript = this.getSearchScript();
        if (searchScript) {
            const placHolderTxt = ULanguage.getInstance().getLangByID("club.searchMember") + "  (" + this.membersCount + ")";
            searchScript.initData(placHolderTxt, (searchContent: string) => {
                console.log(TAG, "接收到搜索内容：", searchContent);
                this.runSearchMember(searchContent);
            });
        } else {
            console.error(TAG, "SearchView组件不存在");
        }
        // 初始化排序菜单
        this.initSortMenu();
        // 初始化数据
        this.initData();
        // 获取成员列表
        this.getMemberList();
    }

    /**
     * 获取搜索组件
     * @returns SearchView组件实例
     */
    private getSearchScript(): SearchView {
        return this.searchContainerNode.getChildByName("SearchView").getComponent(SearchView);
    }

    /**
     * 设置搜索框的placeholder
     */
    private setSearchPlaceholder(): void {
        const searchScript = this.getSearchScript();
        if (searchScript) {
            const placHolderTxt = ULanguage.getInstance().getLangByID("club.searchMember") + "  (" + this.membersCount + ")";
            searchScript.setPlaceholder(placHolderTxt);
        }
    }

    /**
     * 初始化排序菜单的UI
     */
    private initSortMenu() {
        this.sortMenuContainerNode.active = true;
        let options = [
            this.getLangByID("club.ServiceFee"),
            this.getLangByID("club.profitLoss"),
            this.getLangByID("club.handCount"),
            this.getLangByID("club.lastLoginTime"),
            this.getLangByID("club.lastPlay"),
        ];
        this.sortMenuContainerNode
            .getComponent(UPrefabContainer)
            .getNodeComponent(UISelectBox)
            .init(
                options,
                options[0],
                (_sortFiled: number, value: number | string, _sortDirection: SORT_TYPE) => {
                    console.log(TAG, "排序说明:", value, ", 升序or降序:", _sortDirection);
                    this.sortField = _sortFiled;
                    this.sortDirection = _sortDirection;
                    this.sortMemberList();
                },
                OptionsItemMode.SORT,
                VERTICAL_ALIGN.DOWN,
                HORIZONTAL_ALIGN.RIGHT
            );
    }

    /**
     * 对成员列表进行排序
     */
    private sortMemberList(): void {
        if (this.membersList.length === 0) {
            return;
        }
        let sourceList = this.membersList;
        if (this.searchResultList.length > 0) {
            sourceList = this.searchResultList;
        }
        console.log(TAG, "排序前的成员列表:", sourceList);
        sourceList = MemberHelper.sortMemberList(sourceList, this.isSortRole, this.sortField, this.sortDirection);
        console.log(TAG, "排序后的成员列表:", sourceList);
        this.refreshListView(sourceList);
    }

    getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id);
    }

    /**
     * 点击按身份分组Toggle
     */
    private roleToggleClick(): void {
        this.isSortRole = this.roleToggle.isChecked;
        console.log(TAG, "是否通过身份分组：", this.isSortRole);
        this.sortMemberList();
    }

    /**
     * 点击删除按钮
     */
    private onDeleteBtnClick(): void {
        console.log(TAG, "onDeleteBtnClick");
        const alertParams: UIAlertDialogOptions = {
            title: this.getLangByID("common.tip"),
            content: this.getLangByID("club.deleteMemberMsg").replace("xx", this.selectedMemberIds.length.toString()),
            mask: true,
            confirmText: this.getLangByID("common.confirm"),
            cancelText: this.getLangByID("common.cancel"),
            confirm: this.onDeleteMember.bind(this),
            cancel: () => {},
        };

        OKGameManager.getInstance().showAlert(alertParams);
    }

    /**
     * 删除成员
     */
    private onDeleteMember(): void {
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqDeleteClubMember(this.clubInfo.clubId, this.myUserId, this.selectedMemberIds)
            .then((res: okpb.RspRemoveClubUser) => {
                console.log(TAG, "删除成员成功:", res);
                if (res.errorCode === okpb.ERET.OK) {
                    if (cc.isValid(this.node)) {
                        // 通知删除的view
                        this.ScrollView.content.children.forEach((child) => {
                            const itemScript = child.getComponent(ItemMemberView);
                            if (itemScript) {
                                itemScript.setDeleteMember(this.selectedMemberIds.map(String));
                            }
                        });
                        // 清空选中的成员ids
                        this.selectedMemberIds = [];
                        // 更新删除按钮的显示状态
                        this.deleteBtn.node.active = false;
                    }
                    ClubDataManager.getInstance().deleteMember(this.clubInfo.clubId, this.selectedMemberIds);
                } else {
                    OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                }
            })
            .catch((err: any) => {
                console.error(TAG, "删除成员失败:", err);
            })
            .finally(() => {
                console.log(TAG, "删除成员完成");
                OKGameManager.getInstance().hideLoading();
            });
    }

    /**
     * 获取服务器成员列表
     * 重要: 0616决定不采用分页模式，请求接口全部返回后，本地做排序处理
     * 另外: 涉及到几千上万个俱乐部成员的话，后期再做处理，目前先不考虑
     */
    private getMemberList(): void {
        if (this.isRequesting) {
            console.log(TAG, "请求正在进行中，忽略本次请求");
            return;
        }

        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        this.debounceTimer = setTimeout(() => {
            OKGameManager.getInstance().showLoading();
            this.isRequesting = true;

            if (this.myRole === okpb.Identity.IDE_AGENT) {
                console.log(TAG, "我的角色是代理，开始请求我这个代理的所有下线成员");
                ClubDataManager.getInstance().getAgentSubMemberList(this.clubInfo.clubId, this.myUserId, () => {
                    console.log(TAG, "请求我这个代理的所有下线成员结束,开始获得缓存数据");
                    this.isRequesting = false;
                    OKGameManager.getInstance().hideLoading();
                    this.handleMemberList();
                });
                return;
            }
            console.log(TAG, "我的角色是管理员或者创建者，开始请求俱乐部所有成员");
            ClubDataManager.getInstance().requestMembersAndAgetnAndMark(this.clubInfo.clubId, () => {
                console.log(TAG, "请求俱乐部所有成员结束，开始获得缓存数据");
                this.isRequesting = false;
                OKGameManager.getInstance().hideLoading();
                this.handleMemberList();
            });
        }, this.DEBOUNCE_DELAY);
    }

    /**
     * 处理成员列表数据
     */
    private handleMemberList() {
        if (!cc.isValid(this.node)) {
            return;
        }
        const clubInfo = ClubDataManager.getInstance().getClubInfoById(this.clubInfo.clubId);
        this.userRole = clubInfo.identity;
        this.onlineCallback?.(clubInfo.onlineCount);

        this.membersList = ClubDataManager.getInstance().getAllMemberListByClubId(this.clubInfo.clubId);
        this.membersList = MemberHelper.sortMemberList(this.membersList, this.isSortRole, this.sortField, this.sortDirection);
        this.membersCount = this.membersList.length;
        console.log(TAG, "获得缓存数据完成，成员数量:", this.membersCount, ",成员列表:", this.membersList);

        this.setSearchPlaceholder();
        if (this.membersCount === 0) {
            this.showEmptyData(this.getLangByID("club.noMember"));
            return;
        }

        this.refreshListView(this.membersList);
    }

    /**
     * 更新成员列表View
     * @param sourceList 源数据列表
     */
    private refreshListView(sourceList: IClubMemberFullInfo[]): void {
        console.log(TAG, "refreshListView() 刷新listView View展示");
        this.ScrollView.content.removeAllChildren();
        this.emptyDataNode.active = sourceList?.length === 0;
        sourceList.forEach((member) => {
            const itemNode = cc.instantiate(this.itemMemberView);
            const itemScript = itemNode.getComponent(ItemMemberView);
            console.log(TAG, "单个成员信息:", JSON.stringify(member));
            itemScript.setData(member, this.userRole, this.onCheckMember.bind(this));
            itemScript.showDiffView(this.sortField);
            this.ScrollView.content.addChild(itemNode);
        });
    }

    /**
     * 选择成员回调处理
     * @param userId 成员ID
     * @param isChecked 是否选中
     */
    private onCheckMember(userId: string, isChecked: boolean) {
        // console.log(TAG, "userId:", userId, ",是否选中:", isChecked);
        if (isChecked) {
            this.selectedMemberIds.push(Number(userId));
        } else {
            this.selectedMemberIds = this.selectedMemberIds.filter((id) => id !== Number(userId));
        }
        console.log(TAG, "选中的成员数组:", this.selectedMemberIds);
        const newLength = this.selectedMemberIds.length;
        this.deleteBtn.node.active = newLength > 0;
        // 修改deleteBtn的文本
        this.delCountLabel.string = this.getLangByID("club.delete") + "(" + newLength + ")";
    }

    /**
     * 搜索成员
     * @param searchContent 搜索内容
     */
    private runSearchMember(searchContent: string) {
        console.log(TAG, "runSearchMember:", searchContent);
        // 如果searchContent内容为空，则展示所有数据
        if (!searchContent) {
            this.searchResultList = [];
            const sortResult = MemberHelper.sortMemberList(this.membersList, this.isSortRole, this.sortField, this.sortDirection);
            this.refreshListView(sortResult);
            return;
        }
        // IClubMemberInfo通过搜索nickname、userId、remarkName、agentId、upAgentId进行搜索
        this.searchResultList = this.membersList.filter((member) => {
            return (
                member.nickname.includes(searchContent) ||
                member.userId.toString().includes(searchContent) ||
                member.mark.includes(searchContent) ||
                member.agentId.toString().includes(searchContent) ||
                member.realname.includes(searchContent)
            );
        });

        if (this.searchResultList.length === 0) {
            this.showEmptyData(this.getLangByID("club.searchNoMember"));
            this.ScrollView.content.removeAllChildren();
        } else {
            const sortResult = MemberHelper.sortMemberList(this.searchResultList, this.isSortRole, this.sortField, this.sortDirection);
            console.log(TAG, "搜索后+排序后的成员列表:", sortResult);
            this.refreshListView(sortResult);
        }
    }

    /**
     * 显示无成员的默认node
     */
    private showEmptyData(defaultTxt: string = ""): void {
        this.emptyDataNode.active = true;
        const emptyScript = this.emptyDataNode.getComponent(UPrefabContainer).getNodeComponent(UIListEmpty);
        if (emptyScript) {
            emptyScript.setTitle(defaultTxt);
        } else {
            console.error(TAG, "UIListEmpty组件不存在");
        }
    }
}
