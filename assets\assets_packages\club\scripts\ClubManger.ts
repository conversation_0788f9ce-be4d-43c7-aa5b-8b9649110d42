import UAssetDispenser from "../../../framwork/utils/UAssetDispenser";
import { okpb } from "../../okgame/proto/proto_msg";
import { IClubCompleteInfo, IClubMemberFullInfo } from "./members/MemberTypes";
export enum CLUB_UIID {
    // 俱乐部编辑资料： 创建者or管理员or首次创建
    ClubEditProfileView = "view/editprofile/EditProfileDialog",
    // 俱乐部资料预览： 所有成员
    ClubProfilePreviewView = "view/editprofile/ProfilePreviewDialog",
    // 俱乐部编辑二级弹框： 创建者or管理员or首次创建
    ClubEditProfileSubView = "view/editprofile/EditSubView",
    // 成员--主页面：
    MemberMainView = "view/members/MembersMainView",
    // 成员-详情页面
    MemberDetailView = "view/members/detail/MemberDetailDialog",
    // 成员-编辑备注弹框
    MemberEditRemarksView = "view/members/detail/EditRemarksDialog",
    /**
     * 成员-权限限制弹框
     */
    PermissionLimitDialog = "view/members/detail/PermissionDialog",
    /**
     * 发放/收回筹码弹框
     */
    IssueAndCollectChipsDialog = "view/members/detail/IssueAndCollectChipsDialog",
    /**
     * 代理授信弹框
     */
    ChipsHistoryDialog = "view/members/detail/ChipsHistoryDialog",
    /**
     * 下线管理弹框
     */
    DownlineManageDialog = "view/members/detail/DownlineManagementDialog",
    /**
     * 成员详情-代理数据弹框
     */
    AgentDataDialog = "view/members/agent/AgentDataDialog",
    /**
     * 设置角色弹框
     */
    SetRoleDialog = "view/members/detail/SetRoleDialog",
    /**
     * 所属代理弹框
     */
    // 俱乐部设置
    ClubSettingView = "view/setting/ClubSettingView",
    // 俱乐部等级
    ClubLevelView = "view/setting/ClubLevelView",
    // 俱乐部生涯
    ClubCareerView = "view/setting/ClubCareerView",
    // 俱乐部手机通知栏消息
    ClubInformView = "view/setting/ClubInformView",
    // 俱乐部消息
    ClubMessageView = "view/setting/ClubMessageView",
    // 俱乐部服务费
    ClubFeeView = "view/setting/ClubFeeView",
    // 俱乐部滚动公告
    ClubNoticeView = "view/setting/ClubNoticeView",
    // 俱乐部海报
    ClubPosterView = "view/setting/ClubPosterView",
    // 俱乐部数据
    ClubDetailDataView = "view/detailData/ClubDetailDataView",
    // 俱乐部柜台
    ClubCashierView = "view/cashier/ClubCashierView",
    // 俱乐部柜台代理授信
    ClubCashierAgentCreditView = "view/cashier/ClubCashierAgentCreditView",
    // 俱乐部柜台回收
    ClubCashierRecoverView = "view/cashier/ClubCashierRecoverView",
    // 俱乐部柜台发放
    ClubCashierSendView = "view/cashier/ClubCashierSendView",

    // 俱乐部代币-兑换筹码
    ClubExChangeChipView = "view/common/ClubExChangeChipView",
    // 俱乐部代币-筹码申请
    ClubChipRequestView = "view/common/ClubChipRequestView",
    // 俱乐部代币-授信申请
    ClubCreditRequestView = "view/common/ClubCreditRequestView",
}

//俱乐部自定义事件
export enum CLUB_EVENT {
    /** 俱乐部信息变更 */
    UPDATE_CLUB_INFO = "CLUB_EVENT.UPDATE_CLUB_INFO",
    /** 俱乐部柜台信息变更 */
    UPDATE_CLUB_CASHIER_INFO = "CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO",
    /** 俱乐部柜台玩家列表筹码信息变更 */
    UPDATE_CLUB_USER_CHIP_BALANCE = "CLUB_EVENT.UPDATE_CLUB_USER_CHIP_BALANCE",
    /** 俱乐部个人信息更新 */
    UPDATE_CLUB_USER_SELF_INFO = "CLUB_EVENT.UPDATE_CLUB_USER_SELF_INFO",
}

/**
 * 俱乐部管理类
 * @description 负责俱乐部相关功能管理，采用单例模式
 */
export default class ClubManger extends UAssetDispenser {
    private static _instance: ClubManger;
    private constructor() {
        super("club");
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): ClubManger {
        if (!this._instance) {
            this._instance = new ClubManger();
        }
        return this._instance;
    }

    /**
     * 检查用户是否有指定的权限
     * @param clubInfo 俱乐部信息
     * @param requiredRoles 需要的权限
     * @returns 是否有权限
     */
    public checkAccess(clubInfo: okpb.ClubResp, requiredRoles: okpb.Identity[]): boolean {
        if (!clubInfo) return false;
        if (!Array.isArray(requiredRoles) || requiredRoles.length <= 0) return false;
        const identity = clubInfo.identity || okpb.Identity.IDE_GENERAL;
        return requiredRoles.includes(identity);
    }

    /**
     * 是否有编辑俱乐部信息权限
     * @param clubInfo
     * @returns
     */
    public hasEditClubProfileAccess(clubInfo: okpb.ClubResp) {
        return this.checkAccess(clubInfo, [okpb.Identity.IDE_MASTER, okpb.Identity.IDE_MANAGER]);
    }

    /**
     * 是否有查看成员的权限
     * @description 管理员、创建者、代理 可以查看成员列表
     * @param clubInfo
     * @returns
     */
    public hasMemberAccess(clubInfo: okpb.ClubResp): boolean {
        return this.checkAccess(clubInfo, [okpb.Identity.IDE_MASTER, okpb.Identity.IDE_MANAGER, okpb.Identity.IDE_AGENT]);
    }

    /**
     * 是否有查看柜台的权限
     * @param clubInfo
     * @returns
     */
    public hasCashierAccess(clubInfo: okpb.ClubResp): boolean {
        return this.checkAccess(clubInfo, [okpb.Identity.IDE_MASTER, okpb.Identity.IDE_MANAGER, okpb.Identity.IDE_AGENT]);
    }

    /**
     * 是否有查看数据的权限
     * @param clubInfo
     * @returns
     */
    public hasDataAccess(clubInfo: okpb.ClubResp): boolean {
        return this.checkAccess(clubInfo, [okpb.Identity.IDE_MASTER, okpb.Identity.IDE_MANAGER, okpb.Identity.IDE_AGENT]);
    }

    /**
     * 是否有设置权限
     * @param clubInfo
     * @returns
     */
    public hasSetingAccess(clubInfo: okpb.ClubResp): boolean {
        return this.checkAccess(clubInfo, [okpb.Identity.IDE_MASTER, okpb.Identity.IDE_MANAGER, okpb.Identity.IDE_AGENT]);
    }

    /**
     * 显示编辑俱乐部信息
     * @param clubInfo 俱乐部数据
     * @param updateCallback 编辑成功后的回调
     */
    public showClubEditProfileView(clubInfo: okpb.ClubResp) {
        this.showDialog(CLUB_UIID.ClubEditProfileView, clubInfo);
    }

    /**
     * 显示俱乐部资料预览
     * @param clubInfo 俱乐部数据
     */
    public showClubProfilePreviewView(clubInfo: okpb.ClubResp) {
        this.showDialog(CLUB_UIID.ClubProfilePreviewView, clubInfo);
    }

    /**
     * 显示俱乐部编辑二级弹框
     * @param clubInfo 俱乐部数据
     * @param editType 编辑类型
     */
    public showClubEditProfileSubView(clubInfo: okpb.ClubResp, editType: number, callbackFun: (clubInfo: okpb.ClubResp) => void) {
        this.showDialog(CLUB_UIID.ClubEditProfileSubView, clubInfo, editType, callbackFun);
    }

    /**
     * 显示俱乐部弹框
     * @param viewName 弹框名称
     */
    public showClubDialog(viewName: string, ...params: any[]) {
        this.showDialog(viewName, ...params);
    }

    /**
     * 显示俱乐部成员列表
     * @param clubInfo 俱乐部数据
     */
    public showClubMemberView() {
        this.showDialog(CLUB_UIID.MemberMainView);
    }

    /**
     * 显示成员详情
     * @param memberInfo 成员数据
     * @param agentUserInfo 代理数据
     */
    public showMemberDetailView(memberInfo: IClubMemberFullInfo) {
        this.showDialog(CLUB_UIID.MemberDetailView, memberInfo);
    }

    /**
     * 显示编辑备注弹框
     * @param mark 备注
     * @param markDetail 备注详情
     */
    public showMemberEditRemarksView(curUserId: number, mark: string, markDetail: string, refreshMemberInfo: () => void) {
        this.showDialog(CLUB_UIID.MemberEditRemarksView, curUserId, mark, markDetail, refreshMemberInfo);
    }

    /**
     * 显示下线管理弹框
     * @param targetIdentity 目标身份
     */
    public showDownlineManagementView(targetId: number, targetIdentity: okpb.Identity) {
        this.showDialog(CLUB_UIID.DownlineManageDialog, targetId, targetIdentity);
    }

    /**
     * 显示设置角色弹框
     * @param clubId 俱乐部ID
     * @param targetUserId 目标用户ID
     * @param sourceIdentity 源身份
     * @param operationCallback 操作回调
     */
    public showSetRoleDialog(clubId: number, targetUserId: number, sourceIdentity: okpb.Identity, operationCallback: Function) {
        this.showDialog(CLUB_UIID.SetRoleDialog, clubId, targetUserId, sourceIdentity, operationCallback);
    }

    /**
     * 显示权限限制弹框
     * @param clubId 俱乐部ID
     * @param targetUserId 目标用户ID
     * @param sourceIdentity 源身份
     * @param operationCallback 操作回调
     */
    public showPermissionLimitDialog(memberId: number, permissions: number) {
        this.showDialog(CLUB_UIID.PermissionLimitDialog, memberId, permissions);
    }

    /**
     * 显示筹码历史记录弹框： 成员详情-->代理授信弹框
     * @param myPermissions 权限位掩码
     * @param clubId 俱乐部ID
     * @param targetUserId 目标用户ID
     * @param balance 余额
     */
    public showChipsHistoryDialog(myPermissions: number, targetUserId: number, balance: number) {
        this.showDialog(CLUB_UIID.ChipsHistoryDialog, {
            myPermissions,
            targetUserId,
            balance,
        });
    }

    /**
     * 显示发放/收回筹码弹框
     * @param type 类型："issue" 发放，"collect" 收回
     * @param targetUserId 目标用户ID
     * @param balance 余额
     * @param onSuccess 成功回调函数（可选)
     */
    public showIssueAndCollectChipsDialog(type: string, targetUserId: number, balance: number, onSuccess?: () => void) {
        this.showDialog(CLUB_UIID.IssueAndCollectChipsDialog, {
            type,
            targetUserId,
            chipBalance: balance,
            onSuccess,
        });
    }

    /**
     * 显示成员详情-代理数据弹框
     * @param agentId 代理ID
     */
    public showAgentDataDialog(agentId: number) {
        this.showDialog(CLUB_UIID.AgentDataDialog, agentId);
    }
}
