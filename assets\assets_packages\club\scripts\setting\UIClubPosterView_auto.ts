export default class UIClubPosterView_auto {
    node:cc.Node = null;   
	ClubPosterView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	titleContainer: cc.Node;
	title: cc.Node;
	closedBtn: cc.Node;
	closedBtnBg: cc.Node;
	contentEditBox: cc.Node;
	contentEditBoxBg: cc.Node;
	contentEditBoxLabel: cc.Node;
	contentEditBoxPlaceholder: cc.Node;
	buttonContainer: cc.Node;
	addButton: cc.Node;
	addButtonBg: cc.Node;
	addButtonTitle: cc.Node;
	publishButton: cc.Node;
	publishButtonBg: cc.Node;
	publishButtonTitle: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubPosterView = this.node;
		this.mask = this.ClubPosterView.getChildByName("mask");
		this.mainContainer = this.ClubPosterView.getChildByName("mainContainer");
		this.titleContainer = this.mainContainer.getChildByName("titleContainer");
		this.title = this.titleContainer.getChildByName("title");
		this.closedBtn = this.titleContainer.getChildByName("closedBtn");
		this.closedBtnBg = this.closedBtn.getChildByName("closedBtnBg");
		this.contentEditBox = this.mainContainer.getChildByName("contentEditBox");
		this.contentEditBoxBg = this.contentEditBox.getChildByName("contentEditBoxBg");
		this.contentEditBoxLabel = this.contentEditBox.getChildByName("contentEditBoxLabel");
		this.contentEditBoxPlaceholder = this.contentEditBox.getChildByName("contentEditBoxPlaceholder");
		this.buttonContainer = this.mainContainer.getChildByName("buttonContainer");
		this.addButton = this.buttonContainer.getChildByName("addButton");
		this.addButtonBg = this.addButton.getChildByName("addButtonBg");
		this.addButtonTitle = this.addButtonBg.getChildByName("addButtonTitle");
		this.publishButton = this.buttonContainer.getChildByName("publishButton");
		this.publishButtonBg = this.publishButton.getChildByName("publishButtonBg");
		this.publishButtonTitle = this.publishButtonBg.getChildByName("publishButtonTitle");

    }
}
