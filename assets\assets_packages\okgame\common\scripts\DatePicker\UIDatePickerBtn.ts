import UBaseView from "../../../../../framwork/widget/UBaseView";
import OKGameManager from "../../../public/OKGameManager";
import OKGameUtils from "../../../public/OKGameUtils";
import { DatePickerParams } from "./UIDatePicker";
import UIDatePickerBtn_auto from "./UIDatePickerBtn_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIDatePickerBtn extends UBaseView {
    protected ui: UIDatePickerBtn_auto = null;
    private startTime: number = 0;
    private endTime: number = 0;
    private maxDay: number = 30;
    private maxSelectDay = 31;
    private includeToday: boolean = true;
    private defQueryDay: number = 0;
    private customStartTime: number = 0;
    private customEndTime: number = 0;
    private updateDateTimerFun: Function = null;
    private isOnlyOneday: boolean = false;
    /**
     * 显示日期选择器时的回调，例如处理父页面中的toggle选中
     */
    private showCallback: Function = null;
    onUILoad(): void {
        this.ui = new UIDatePickerBtn_auto(this.node);
        this.onRegisterEvent(this.node, this.showDatePicker.bind(this));
    }
    private updateDateTimer(data: { startTime: number; endTime: number; isAutoReq: boolean }) {
        if (data.startTime) {
            let date = new Date(data.startTime);
            this.ui.dateTimer.getComponent(cc.Label).string = `${date.getMonth() + 1}/${date.getDate()}`;
        }
        if (data.endTime) {
            let date = new Date(data.endTime);
            this.ui.dateTimer.getComponent(cc.Label).string += ` - ${date.getMonth() + 1}/${date.getDate()}`;
        }
        this.updateDateTimerFun && this.updateDateTimerFun(data);
    }

    public showDatePicker() {
        this.showCallback && this.showCallback();
        console.log("----- inclubToday ----", this.includeToday);
        let params: DatePickerParams = {
            maxDay: this.maxDay,
            includeToday: this.includeToday,
            startTime: this.startTime,
            endTime: this.endTime,
            updateDateFun: this.updateDateTimer.bind(this),
            maxSelectDay: this.maxSelectDay,
            isOnlyOneday: this.isOnlyOneday,
        };
        OKGameManager.getInstance().showDialog("common/view/DatePicker/DatePicker", params);
    }

    private initDefQueryDay() {
        this.endTime = this.customEndTime ? this.customEndTime : this.includeToday ? new Date().getTime() : new Date().getTime() - 1000 * 3600 * 24;
        if (this.customStartTime) {
            this.startTime = this.customStartTime;
        } else {
            this.startTime = this.endTime - 1000 * 3600 * 24 * this.defQueryDay;
        }
        if (this.isOnlyOneday) this.startTime = this.endTime;
        this.updateDateTimer({
            startTime: OKGameUtils.formatStartTime(this.startTime),
            endTime: OKGameUtils.formatEndTime(this.endTime),
            isAutoReq: false,
        });
    }

    public setSerachTimer(
        updateDateTimerFun: Function,
        inclubToday: boolean = true,
        serachTime?: { startTime: number; endTime: number },
        isOnlyOneday: boolean = false
    ) {
        if (serachTime) {
            this.customStartTime = serachTime.startTime;
            this.customEndTime = serachTime.endTime;
        }
        if (isOnlyOneday) this.isOnlyOneday = true;
        this.includeToday = inclubToday;
        this.updateDateTimerFun = updateDateTimerFun;
        this.initDefQueryDay();
    }

    public setDefQueryDay(day: number) {
        this.defQueryDay = day;
    }

    public setMaxSelectDay(day: number) {
        this.maxSelectDay = day;
    }

    public setDateLabelColor(color: cc.Color) {
        this.ui.dateTimer.color = color;
    }

    /**
     * 设置dateTimer的文本大小
     * @param size 文本大小
     */
    public setDateTimerFontSize(size: number) {
        this.ui.dateTimer.getComponent(cc.Label).fontSize = size;
    }

    /**
     * 设置默认的文本
     * @param txt 文本
     */
    public setDefaultTxt(txt: string) {
        this.ui.dateTimer.getComponent(cc.Label).string = txt;
    }

    /**
     * 设置显示日期选择器时的回调，例如处理父页面中的toggle选中
     * @param callback 回调
     */
    public setShowCallback(callback: Function) {
        this.showCallback = callback;
    }
}
