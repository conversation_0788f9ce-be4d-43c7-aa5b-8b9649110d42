export default class UICareerMainView_auto {
    node:cc.Node = null;   
	CareerMainView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	navContainer: cc.Node;
	titleLabel: cc.Node;
	backButton: cc.Node;
	backButtonBg: cc.Node;
	favButton: cc.Node;
	favButtonBg: cc.Node;
	dateToggleContainer: cc.Node;
	toggleYesterday: cc.Node;
	yesterdayBackground: cc.Node;
	yesterdayCheckMark: cc.Node;
	yesterday: cc.Node;
	toggleLastSeven: cc.Node;
	lastBackground: cc.Node;
	lastCheckMark: cc.Node;
	lastSevenDay: cc.Node;
	toggleSelect: cc.Node;
	selectBackground: cc.Node;
	selectCheckMark: cc.Node;
	selectDate: cc.Node;
	selectIcon: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.CareerMainView = this.node;
		this.mask = this.CareerMainView.getChildByName("mask");
		this.mainContainer = this.CareerMainView.getChildByName("mainContainer");
		this.navContainer = this.mainContainer.getChildByName("navContainer");
		this.titleLabel = this.navContainer.getChildByName("titleLabel");
		this.backButton = this.navContainer.getChildByName("backButton");
		this.backButtonBg = this.backButton.getChildByName("backButtonBg");
		this.favButton = this.navContainer.getChildByName("favButton");
		this.favButtonBg = this.favButton.getChildByName("favButtonBg");
		this.dateToggleContainer = this.mainContainer.getChildByName("dateToggleContainer");
		this.toggleYesterday = this.dateToggleContainer.getChildByName("toggleYesterday");
		this.yesterdayBackground = this.toggleYesterday.getChildByName("yesterdayBackground");
		this.yesterdayCheckMark = this.toggleYesterday.getChildByName("yesterdayCheckMark");
		this.yesterday = this.toggleYesterday.getChildByName("yesterday");
		this.toggleLastSeven = this.dateToggleContainer.getChildByName("toggleLastSeven");
		this.lastBackground = this.toggleLastSeven.getChildByName("lastBackground");
		this.lastCheckMark = this.toggleLastSeven.getChildByName("lastCheckMark");
		this.lastSevenDay = this.toggleLastSeven.getChildByName("lastSevenDay");
		this.toggleSelect = this.dateToggleContainer.getChildByName("toggleSelect");
		this.selectBackground = this.toggleSelect.getChildByName("selectBackground");
		this.selectCheckMark = this.toggleSelect.getChildByName("selectCheckMark");
		this.selectDate = this.toggleSelect.getChildByName("selectDate");
		this.selectIcon = this.toggleSelect.getChildByName("selectIcon");

    }
}
