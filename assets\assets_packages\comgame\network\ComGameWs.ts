import OKBaseWS from "../../okgame/network/OKBaseWS";
import { OKGameMsgType } from "../../okgame/network/OKGameMsgType";
import { wspb } from "../../okgame/proto/proto_ws_msg";
import { ImChatMsgType, ImEmojiType } from "../pubilc/ComGameConst";

export const WsOriginOpt = {
    anonymous: "anonymous",
    joinRoom: "joinRoom",   //加入房间时
    userSit: "userSit",     //用户坐下
    userStand: "userStand", //用户站起
    forceStand: "forceStand",//强制站起
    backSeat: "backSeat",
    sysOpt: "sysOpt",
    sngExit: "sngExit",
    reserveStand: "reserveStand",
}

export default class ComGameWs extends OKBaseWS {
    //------------
    protected static _instance = null;
    public static getInstance(): ComGameWs {
        if (this._instance == null) {
            this._instance = new ComGameWs();
        }
        return this._instance;
    }

    //------------
    constructor() {
        super();
    }

    /**
     * 发送站起
     * @param cb 回调
     */
    sendSandUp(cb?: Function) {
        this.sendGameMessage(OKGameMsgType.standUp, null, cb);
    }

    /**
     * 解散房间
     * @param cb 
     */
    sendTableClose(cb?: Function) {
        this.sendGameMessage(OKGameMsgType.tableClose, null, cb);
    }

    /**
     * 保座离桌
     * @param cb 
     */
    sendReserveStand(cb?: Function) {
        this.sendGameMessage(OKGameMsgType.reserveStand, null, cb);
    }

    /**
     * 检查坐下
     * @param seatNum 
     * @param lon // 经度
     * @param lat // 纬度
     * @param cb 
     */
    sendCheckSitOpt(seatNum: number, lon?: number, lat?: number, cb?: Function) {
        let obj = {} as wspb.BattleSitMsg;
        obj.seatNum = seatNum;
        obj.lon = lon ? lon : 0;
        obj.lat = lat ? lat : 0;
        this.sendGameMessage(OKGameMsgType.checkSitOpt, wspb.BattleSitMsg.encode(obj), cb);
    }

    /**
     * 请求坐下
     * @param seatNum 
     * @param lon // 经度
     * @param lat // 纬度
     * @param cb 
     */
    sendSitDown(seatNum: number, cb?: Function) {
        let obj = {} as wspb.BattleSitMsg;
        obj.seatNum = seatNum;
        this.sendGameMessage(OKGameMsgType.sitDown, wspb.BattleSitMsg.encode(obj), cb);
    }

    /**
     * 取消留座离桌
     */
    sendBackSeat() {
        this.sendGameMessage(OKGameMsgType.backSeat, null);
    }

    /**
     * 开始游戏
     * @param cb 
     */
    sendStartGame(cb?: Function) {
        this.sendGameMessage(OKGameMsgType.startGame, null, cb);
    }

    /**
     * 买分
     */
    sendAddScore(score: number, passwd: string) {
        console.log('sendAddScore');
        let obj = {} as wspb.BattleScorePO;
        obj.score = score;
        obj.passwd = passwd;
        this.sendGameMessage(OKGameMsgType.addScore, wspb.BattleScorePO.encode(obj));
    }
    /**
     * 发送表情
     * @param id 内容
     */
    sendEmoji(id: string) {
        console.log('sendEmoji');
        let obj = {} as wspb.EmojiMsgPO;
        obj.type = ImEmojiType.kEmoji;
        obj.content = id;
        this.sendGameMessage(OKGameMsgType.sendEmoji, wspb.EmojiMsgPO.encode(obj));
    }
    /**
     * 发送互动道具
     * @param id 内容
     */
    sendEachProp(id: string, receiverId: number) {
        console.log('sendEachProp');
        let obj = {} as wspb.EmojiMsgPO;
        obj.type = ImEmojiType.kEachProp;
        obj.content = id;
        obj.receiverId = receiverId;
        this.sendGameMessage(OKGameMsgType.sendEmoji, wspb.EmojiMsgPO.encode(obj));
    }

    /**
     * 发送聊天信息
     * @param id 内容
     */
    sendChatMsg(id: string, content: string) {
        console.log('sendEachProp');
        let obj = {} as wspb.MsgTypeMsgPO;
        const timestamp = new Date().getTime();
        obj.createTime = timestamp;
        obj.second = timestamp;
        obj.type = ImChatMsgType.text;
        obj.content = content;
        if (id) obj.id = id;
        this.sendGameMessage(OKGameMsgType.sendMsg, wspb.MsgTypeMsgPO.encode(obj));
    }
}
