import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UIClubPosterView_auto from "./UIClubPosterView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubPosterView]";

/**
 * 俱乐部海报设置
 *
 * @export
 * @class UIClubPosterView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubPosterView")
export default class UIClubPosterView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubPosterView_auto = null;
    private contentText: string = "";

    private readonly EVENT_HANDLERS = {
        "text-changed": {
            contentEditBox: this.onContentTextChanged.bind(this),
        },
        "editing-did-began": {
            contentEditBox: this.onContentEditBegan.bind(this),
        },
        "editing-did-ended": {
            contentEditBox: this.onContentEditEnded.bind(this),
        },
    };

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubPosterView_auto(this.node);
        this.initEvent();
    }

    start() {}

    onDestroy() {
        super.onDestroy();
        this.removeAllEventListeners();
    }

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubPosterView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIClubPosterView
     */
    private initEvent() {
        // 按钮事件
        this.onRegisterEvent(this.ui.closedBtn, this.onClickBackButton.bind(this));
        this.onRegisterEvent(this.ui.addButton, this.onClickAddButton.bind(this));
        this.onRegisterEvent(this.ui.publishButton, this.onClickPublishButton.bind(this));

        // 输入框事件
        this.initEditBoxEvents();
    }

    /**
     * 初始化输入框事件
     *
     * @private
     * @memberof UIClubPosterView
     */
    private initEditBoxEvents() {
        // 内容输入框事件
        const contentEditBox = this.ui.contentEditBox.getComponent(cc.EditBox);
        if (contentEditBox) {
            this.registerEditBoxEvents(contentEditBox, "contentEditBox");
        }
    }

    /**
     * 注册输入框事件
     *
     * @private
     * @param {cc.EditBox} editBox
     * @param {string} editBoxName
     * @memberof UIClubPosterView
     */
    private registerEditBoxEvents(editBox: cc.EditBox, editBoxName: string) {
        Object.keys(this.EVENT_HANDLERS).forEach((eventType) => {
            const handler = this.EVENT_HANDLERS[eventType][editBoxName];
            if (handler) {
                editBox.node.on(eventType, handler);
            }
        });
    }

    /**
     * 移除所有事件监听
     *
     * @private
     * @memberof UIClubPosterView
     */
    private removeAllEventListeners(): void {
        const { contentEditBox } = this.ui;

        if (!contentEditBox) {
            return;
        }

        Object.entries(this.EVENT_HANDLERS).forEach(([eventName, handlers]) => {
            contentEditBox.off(eventName, handlers.contentEditBox);
        });
    }

    //#endregion

    //#region 事件处理
    /**
     * 点击返回按钮
     *
     * @private
     * @memberof UIClubPosterView
     */
    private onClickBackButton() {
        this.log("点击返回按钮");
        this.closeDialog();
    }

    /**
     * 点击添加按钮
     *
     * @private
     * @memberof UIClubPosterView
     */
    private onClickAddButton() {
        this.log("点击添加按钮");
        // TODO: 实现添加海报逻辑
        this.addPoster();
    }

    /**
     * 点击发布按钮
     *
     * @private
     * @memberof UIClubPosterView
     */
    private onClickPublishButton() {
        this.log("点击发布按钮");
        this.log(`内容: ${this.contentText}`);

        // TODO: 调用API发布海报
        this.publishPoster();
    }

    /**
     * 内容输入框开始编辑
     *
     * @private
     * @memberof UIClubPosterView
     */
    private onContentEditBegan() {
        this.log("内容输入框开始编辑");
    }

    /**
     * 内容输入框结束编辑
     *
     * @private
     * @memberof UIClubPosterView
     */
    private onContentEditEnded() {
        this.log("内容输入框结束编辑");
    }

    /**
     * 内容输入框文本变化
     *
     * @private
     * @param {cc.EditBox} editBox
     * @param {string} text
     * @memberof UIClubPosterView
     */
    private onContentTextChanged(editBox: cc.EditBox, text: string) {
        this.contentText = text;
        this.log(`内容文本变化: ${text}`);
    }

    /**
     * 添加海报
     *
     * @private
     * @memberof UIClubPosterView
     */
    private addPoster() {
        // TODO: 实现添加海报逻辑
        this.log("添加海报");
    }

    /**
     * 发布海报
     *
     * @private
     * @memberof UIClubPosterView
     */
    private publishPoster() {
        // TODO: 实现发布逻辑
        this.log("发布海报");
        this.closeDialog();
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
