import OKGameManager from "../../../public/OKGameManager";
import BaseCard from "./BaseCard";

const { ccclass, menu, property } = cc._decorator;

/**
 * 牌的创建池
 */
@ccclass
@menu("cards/CardPool")
export default class CardPool extends cc.Component {
    @property(cc.Prefab)
    prefabs: cc.Prefab[] = [];
    //-------
    private _cards: BaseCard[] = []
    //样式
    private _cardStyle: number = -1;
    private _cardBgStyle: number = -1;
    private _cardSpriteAtlas: cc.SpriteAtlas = null
    private _cardBgSpriteFrame: cc.SpriteFrame = null;

    onLoad() {

    }

    onDestroy(): void {
        if(this._cardSpriteAtlas){
            this._cardSpriteAtlas.decRef();
        }
        if(this._cardBgSpriteFrame){
            this._cardBgSpriteFrame.decRef();
        }
    }

    setStyle(cardStyle: number, bgStyle: number) {
        this.setCardStyle(cardStyle)
        this.setCardBgStyle(bgStyle)
    }

    setCardStyle(cardStyle: number) {
        if (cardStyle < 0 || cardStyle > 1) {
            return
        }
        if (this._cardStyle == cardStyle) {
            return
        }
        this._cardStyle = cardStyle;
        OKGameManager.getInstance().loadBundle((bundle: cc.AssetManager.Bundle) => {
            bundle.load(`common/textures/cards/cards${cardStyle + 1}_img`, cc.SpriteAtlas, (err: Error, spriteAtlas: cc.SpriteAtlas) => {
                if (err) {
                    console.error(err)
                    return;
                }
                if (this._cardStyle != cardStyle) {
                    spriteAtlas.destroy();
                    return
                }
                for (let i = 0; i < this._cards.length; i++) {
                    this._cards[i].setCardStyle(spriteAtlas)
                }
                if (this._cardSpriteAtlas) {
                    this._cardSpriteAtlas.decRef();
                }
                this._cardSpriteAtlas = spriteAtlas;
                this._cardSpriteAtlas.addRef();

            });
        })

    }

    setCardBgStyle(bgStyle: number) {
        if (bgStyle < 0 || bgStyle > 5) {
            return
        }
        if (this._cardBgStyle == bgStyle) {
            return
        }
        this._cardBgStyle = bgStyle;
        OKGameManager.getInstance().loadBundle((bundle: cc.AssetManager.Bundle) => {
            bundle.load(`common/textures/cardsBg/ic_card_bg_${bgStyle}`, cc.SpriteFrame, (err: Error, spriteFrame: cc.SpriteFrame) => {
                if (err) {
                    console.error(err)
                    return;
                }
                if (this._cardStyle != bgStyle) {
                    spriteFrame.destroy();
                    return
                }
                for (let i = 0; i < this._cards.length; i++) {
                    this._cards[i].setCardBgStyle(spriteFrame);
                }
                if (this._cardBgSpriteFrame) {
                    this._cardBgSpriteFrame.decRef();
                }
                this._cardBgSpriteFrame = spriteFrame;
                this._cardBgSpriteFrame.addRef();

            });
        })
    }

    createCard(index?: number): BaseCard {
        if (!index) {
            index = 0
        }
        if (!this.prefabs[index]) {
            return null;
        }
        let node = cc.instantiate(this.prefabs[index])
        let view = node.getComponent(BaseCard)
        view.releaseCard = this.releaseCard.bind(this)
        view.setCardStyle(this._cardSpriteAtlas)
        view.setCardBgStyle(this._cardBgSpriteFrame);
        this._cards.push(view)
        return view
    }

    releaseCard(card: BaseCard) {
        for (let i = 0; i < this._cards.length; i++) {
            if (this._cards[i] == card) {
                this._cards.splice(i, 1)
                break
            }
        }
    }

}