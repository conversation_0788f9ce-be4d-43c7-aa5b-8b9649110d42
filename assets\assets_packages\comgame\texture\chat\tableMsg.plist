<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>coin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{50,50}</string>
                <key>spriteSourceSize</key>
                <string>{50,50}</string>
                <key>textureRect</key>
                <string>{{50,1},{50,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>diamond.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{37,36}</string>
                <key>spriteSourceSize</key>
                <string>{37,36}</string>
                <key>textureRect</key>
                <string>{{279,1},{37,36}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>mttticket.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{91,46}</string>
                <key>spriteSourceSize</key>
                <string>{91,46}</string>
                <key>textureRect</key>
                <string>{{154,1},{91,46}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pgcoin.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{50,50}</string>
                <key>spriteSourceSize</key>
                <string>{50,50}</string>
                <key>textureRect</key>
                <string>{{102,1},{50,50}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>pgticket.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{40,30}</string>
                <key>spriteSourceSize</key>
                <string>{40,30}</string>
                <key>textureRect</key>
                <string>{{247,1},{40,30}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>redpocket.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{47,51}</string>
                <key>spriteSourceSize</key>
                <string>{47,51}</string>
                <key>textureRect</key>
                <string>{{1,1},{47,51}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>tableMsg.png</string>
            <key>size</key>
            <string>{316,53}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:bd00ce058eb49187c2af4f1268029b33:e114a8a315373718a4c05968d9ca8a49:5cc9a1b26e182e1ec004d8c7f08c0aa7$</string>
            <key>textureFileName</key>
            <string>tableMsg.png</string>
        </dict>
    </dict>
</plist>
