import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import { CLUB_EVENT } from "../ClubManger";
import UIClubCreditRequestView_auto from "./UIClubCreditRequestView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCreditRequestView extends UBaseDialog {
	protected ui: UIClubCreditRequestView_auto = null;

	onUILoad(): void {
		this.ui = new UIClubCreditRequestView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.onRegisterEvent(this.ui.btnOK, this.onBtnOK.bind(this));

		//柜台筹码相关数据更新
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO, this.updateClubCounterInfo.bind(this));
	}

	onShow() {
		//	获取最新柜台筹码信息，不用及时使用，使用缓存数据显示
		// ClubDataManager.getInstance().requestClubCounterInfo();
		this.updateClubCounterInfo();
	}

	updateClubCounterInfo() {
		let data = ClubDataManager.getInstance().getClubCounterInfo();
		if (data) {
			this.ui.labelCurChip.getComponent(cc.Label).string = "" + data.data.counterChips;
		}
	}

	async onBtnOK() {
		let edit = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		let chips = Number(edit);
		let data: okpb.RspExchangeClubCounterChips = await ClubAPI.getInstance().ReqApplyClubChips(chips, okpb.ClubChipType.CCT_CREDIT);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode == okpb.ERET.OK) {
			OKGameManager.getInstance().showToast("申请授信成功");
			this.closeDialog();
		}
	}
}