import { ULanguage } from "../../../../../framwork/language/ULanguage";
import UBaseView from "../../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../../framwork/widget/UPrefabContainer";
import UIListEmpty from "../../../../okgame/common/scripts/UIListEmpty";
import { okpb } from "../../../../okgame/proto/proto_msg";
import ClubAPI from "../../ClubAPI";
import SearchView from "../SearchView";
import ItemAgentView from "./ItemAgentView";
import UISelectBox from "../../../../okgame/common/scripts/SelectBox/UISelectBox";
import { HORIZONTAL_ALIGN, OptionsItemMode, SORT_TYPE, VERTICAL_ALIGN } from "../../../../okgame/common/scripts/SelectBox/UISelectBoxOptions";
import MemberHelper from "../MemberHelper";
import ClubDataManager from "../../ClubDataManager";
import { IClubCompleteInfo, IClubMemberFullInfo } from "../MemberTypes";
import { mockClubMemberFullInfo } from "../../mockData";

const { ccclass, property } = cc._decorator;
const TAG = "[AgentView]";

/**
 * 代理View
 */
@ccclass
export default class AgentView extends UBaseView {
    @property({ type: cc.Node, tooltip: "搜索container" })
    searchContainerNode: cc.Node = null;

    @property({ type: cc.Node, tooltip: "排序menu container" })
    sortMenuContainerNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "排序描述" })
    sortDescTxt: cc.Label = null;

    @property({ type: cc.ScrollView, tooltip: "代理列表" })
    ScrollView: cc.ScrollView = null;

    @property({ type: cc.Prefab, tooltip: "代理列表item" })
    itemAgentView: cc.Node = null;

    @property({ type: cc.Node, tooltip: "空数据" })
    emptyNode: cc.Node = null;

    private clubInfo: IClubCompleteInfo | null = null;
    private isRequesting: boolean = false;
    private debounceTimer: NodeJS.Timeout | null = null;
    private readonly DEBOUNCE_DELAY: number = 300;

    private agentList: IClubMemberFullInfo[] = [];
    private agentCount: number = 0;
    private searchResultList: IClubMemberFullInfo[] = [];
    private sortField: number = 0;
    private sortDirection: SORT_TYPE = SORT_TYPE.DESC;

    onUILoad(): void {
        console.log(TAG, "onUILoad()");
        this.initView();
        this.initEvent();
    }

    private initView(): void {
        console.log(TAG, "initView()");
        this.initSortMenu();
    }

    private initEvent(): void {
        console.log(TAG, "initEvent()");
    }

    /**
     * 设置俱乐部信息
     */
    public showAgentView(): void {
        console.log(TAG, "setClubInfo()");
        this.clubInfo = ClubDataManager.getInstance().getClubInfo();
        // 搜索UI
        this.searchContainerNode.active = true;
        const searchScript = this.getSearchScript();
        if (searchScript) {
            const placHolderTxt = ULanguage.getInstance().getLangByID("club.searchMember") + "  (" + this.agentList.length + ")";
            searchScript.initData(placHolderTxt, (searchContent: string) => {
                console.log(TAG, "接收到搜索内容：", searchContent);
                this.runSearchAgent(searchContent);
            });
        } else {
            console.error(TAG, "SearchView组件不存在");
        }
        this.getAgentMemberList();
    }

    /**
     * 获取代理列表
     */
    private getAgentMemberList(): void {
        if (this.isRequesting) {
            console.log(TAG, "请求正在进行中，忽略本次请求");
            return;
        }
        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
        this.debounceTimer = setTimeout(() => {
            this.isRequesting = true;
            console.log(TAG, "getAgentMemberList()");

            ClubDataManager.getInstance().requestMembersAndAgetnAndMark(this.clubInfo.clubId, (memberList) => {
                console.log(TAG, "getAgentMemberList()代理页面接收到整合的信息 memberList:", memberList);
                this.isRequesting = false;
                if (memberList) {
                    this.setAgentList(memberList.filter((member) => member.identity === okpb.Identity.IDE_AGENT));
                } else {
                    this.showEmptyData("club.noAgent");
                }
                // TODO: 测试数据
                this.setAgentList(mockClubMemberFullInfo.filter((member) => member.identity === okpb.Identity.IDE_AGENT));
            });
        }, this.DEBOUNCE_DELAY);
    }

    /**
     * 设置代理列表并刷新
     */
    private setAgentList(list: IClubMemberFullInfo[]): void {
        this.agentList = list || [];
        this.agentCount = this.agentList.length;
        this.setSearchPlaceholder();
        this.refreshListView(this.agentList);
    }

    /**
     * 搜索代理
     * @param searchContent 搜索内容
     */
    private runSearchAgent(searchContent: string): void {
        console.log(TAG, "runSearchAgent()");
        if (!searchContent) {
            this.searchResultList = [];
            this.sortAgentList();
            return;
        }
        // 根据ClubAgentData的userId、mark、nickname字段模糊搜索
        this.searchResultList = this.agentList.filter((agent: IClubMemberFullInfo) => {
            return agent.userId.toString().includes(searchContent) || (agent.mark && agent.mark.includes(searchContent)) || (agent.nickname && agent.nickname.includes(searchContent));
        });
        if (this.searchResultList.length === 0) {
            this.showEmptyData("club.searchNoMember");
        } else {
            this.sortAgentList();
        }
    }

    /**
     * 排序代理列表
     */
    private sortAgentList(): void {
        if (this.agentList.length === 0) return;
        const listToSort = this.searchResultList.length > 0 ? this.searchResultList : this.agentList;
        const sortedList = MemberHelper.sortAgentList(listToSort, false, this.sortField, this.sortDirection);
        console.log(TAG, "排序后的代理列表:", JSON.stringify(sortedList));
        this.refreshListView(sortedList);
    }

    /**
     * 刷新代理列表
     * @param agentList 代理列表
     */
    private refreshListView(newAgentList: IClubMemberFullInfo[]): void {
        this.emptyNode.active = false;
        this.ScrollView.content.active = true;
        this.ScrollView.content.removeAllChildren();
        newAgentList.forEach((agent: IClubMemberFullInfo) => {
            const itemNode = cc.instantiate(this.itemAgentView);
            const itemScript = itemNode.getComponent(ItemAgentView);
            itemScript.setData(agent, this.clubInfo);
            this.ScrollView.content.addChild(itemNode);
        });
    }

    /**
     * 显示空数据
     * club.searchNoMember 搜索结果为空
     * club.noMember 暂无成员
     */
    private showEmptyData(langKey: string): void {
        console.log(TAG, "showEmptyData()");
        this.emptyNode.active = true;
        this.ScrollView.content.active = false;
        const emptyScript = this.emptyNode.getComponent(UPrefabContainer).getNodeComponent(UIListEmpty);
        if (emptyScript) {
            emptyScript.setTitle(this.getLangByID(langKey));
        } else {
            console.error(TAG, "UIListEmpty组件不存在");
        }
    }

    /**
     * 设置搜索框的placeholder
     */
    private setSearchPlaceholder(): void {
        const searchScript = this.getSearchScript();
        if (searchScript) {
            const placHolderTxt = ULanguage.getInstance().getLangByID("club.searchMember") + "  (" + this.agentCount + ")";
            searchScript.setPlaceholder(placHolderTxt);
        }
    }

    /**
     * 获取搜索组件
     * @returns SearchView组件实例
     */
    private getSearchScript(): SearchView {
        return this.searchContainerNode.getChildByName("SearchView").getComponent(SearchView);
    }

    /**
     * 排序菜单初始化
     */
    private initSortMenu() {
        console.log(TAG, "initSortMenu()");
        this.sortMenuContainerNode.active = true;
        let options = [
            this.getLangByID("club.thisWeekProfitLoss"),
            this.getLangByID("club.thisWeekServiceFee"),
            this.getLangByID("club.lastWeekProfitLoss"),
            this.getLangByID("club.lastWeekServiceFee"),
            this.getLangByID("club.totalProfitLoss"),
            this.getLangByID("club.totalServiceFee"),
        ];
        this.sortMenuContainerNode
            .getComponent(UPrefabContainer)
            .getNodeComponent(UISelectBox)
            .init(
                options,
                options[0],
                (_sortFiled: number, value: number | string, _sortDirection: SORT_TYPE) => {
                    console.log(TAG, "排序说明:", value, ", 升序or降序:", _sortDirection);
                    this.sortField = _sortFiled;
                    this.sortDirection = _sortDirection;
                    this.sortAgentList();
                },
                OptionsItemMode.SORT,
                VERTICAL_ALIGN.DOWN,
                HORIZONTAL_ALIGN.LEFT,
                SORT_TYPE.DESC
            );
    }

    /**
     * 多语言
     */
    private getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id);
    }
}
