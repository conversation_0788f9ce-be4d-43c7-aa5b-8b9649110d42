export default class UIGameUserOtherView_auto {
    node:cc.Node = null;   
	GameUserOtherView: cc.Node;
	mask: cc.Node;
	view: cc.Node;
	grayBg: cc.Node;
	userInfo: cc.Node;
	btnsNode: cc.Node;
	tableInfo: cc.Node;
	diamondNode: cc.Node;
	surplus: cc.Node;
	contentNode: cc.Node;
	propItem: cc.Node;
	propIcon: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.GameUserOtherView = this.node;
		this.mask = this.GameUserOtherView.getChildByName("mask");
		this.view = this.GameUserOtherView.getChildByName("view");
		this.grayBg = this.view.getChildByName("grayBg");
		this.userInfo = this.grayBg.getChildByName("userInfo-");
		this.btnsNode = this.grayBg.getChildByName("btnsNode-");
		this.tableInfo = this.grayBg.getChildByName("tableInfo-");
		this.diamondNode = this.grayBg.getChildByName("diamondNode-");
		this.surplus = this.grayBg.getChildByName("surplus-");
		this.contentNode = this.grayBg.getChildByName("contentNode");
		this.propItem = this.contentNode.getChildByName("propItem");
		this.propIcon = this.propItem.getChildByName("propIcon");

    }
}
