import OKClubAPI from "../../okgame/club/scripts/OKClubAPI";
import { OKGAME_ROUTE } from "../../okgame/network/OKGameMsgRoute";
import { okpb } from "../../okgame/proto/proto_msg";
import OKGameData from "../../okgame/public/OKGameData";
import ClubDataManager from "./ClubDataManager";

export default class ClubAPI extends OKClubAPI {
    protected static _instance = null;
    public static getInstance(): ClubAPI {
        if (this._instance == null) {
            this._instance = new ClubAPI();
        }
        return this._instance;
    }

    constructor() {
        super();
    }

    /**
     * 进行的牌局请求
     * @param clubId    //俱乐部ID
     * @param playType // 玩法类型(多个用 , 隔开) 0:普通, 1:6+短牌, 2:omaha
     * @param minGrade // 最小游戏等级的小盲或前注
     * @param maxGrade // 最大游戏等级的小盲或前注
     * @param excludeFull / 不看满桌 1:是, 0/null:不限制
     * @param includeStr // 包含Straddle 1, 0:不包含
     * @param timeSort // 创建时间排序 1:是, 0/null:不限制
     * @returns
     */
    public async ReqGetRoomLive(
        clubId: number,
        playType: string,
        minGrade: number,
        maxGrade: number,
        excludeFull: number,
        includeStr: number,
        timeSort: number,
        pageNum: number = 0
    ): Promise<okpb.RspGetRoomLive> {
        let req = {} as okpb.ReqGetRoomLive;
        req.userId = OKGameData.getInstance().getUserId();
        req.clubId = clubId;
        req.playType = playType;
        req.pageNum = pageNum;
        req.minGrade = minGrade;
        req.maxGrade = maxGrade;
        req.excludeFull = excludeFull;
        req.includeStr = includeStr;
        req.timeSort = timeSort;
        return this.requestPB(OKGAME_ROUTE.ReqGetRoomLive, okpb.ReqGetRoomLive.encode(req), okpb.RspGetRoomLive);
    }

    /**
     * 俱乐部编辑开场白请求
     * @param clubId 俱乐部ID
     * @param prologue 开场白文字
     * @param prologueStatus
     * @returns
     */
    // public async ReqClubEditPrologue(clubId: number, prologue: string, prologueStatus: number): Promise<okpb.RspCommonMsg> {
    //     let req = {} as okpb.ReqClubEditPrologue;
    //     req.userId = OKGameData.getInstance().getUserId();
    //     req.clubId = clubId;
    //     req.prologue = prologue;
    //     req.prologueStatus = prologueStatus;
    //     return this.requestPB(OKGAME_ROUTE.ReqClubEditPrologue, okpb.ReqClubEditPrologue.encode(req), okpb.RspCommonMsg);
    // }

    /**
     * 退出俱乐部请求
     * @param clubId 俱乐部ID
     * @returns Promise<okpb.RspCommonMsg> 通用返回消息  errorCode 0:成功
     */
    public async ReqQuitClub(clubId: number): Promise<okpb.RspCommonMsg> {
        let req = {} as okpb.ReqQuitClub;
        req.userId = OKGameData.getInstance().getUserId();
        req.clubId = clubId;
        return this.requestPB(OKGAME_ROUTE.ReqQuitClub, okpb.ReqQuitClub.encode(req), okpb.RspCommonMsg);
    }

    /**
     * 解散俱乐部请求
     * @param clubId 俱乐部ID
     * @param delType // 解散类型 0:不删除成员战绩, 1:删除成员战绩
     * @returns Promise<okpb.RspCommonMsg> 通用返回消息  errorCode 0:成功
     */
    public async ReqDelClub(clubId: number, delType: number): Promise<okpb.RspCommonMsg> {
        let req = {} as okpb.ReqDelClub;
        req.userId = OKGameData.getInstance().getUserId();
        req.clubId = clubId;
        req.delType = delType;
        return this.requestPB(OKGAME_ROUTE.ReqDelClub, okpb.ReqDelClub.encode(req), okpb.RspCommonMsg);
    }

    /**
     * 备注用户的请求
     * @param clubId 俱乐部ID
     * @param userId 操作员ID
     * @param clubUserId 备注用户ID
     * @param markName 备注名
     * @param markDetail 备注详情
     * @returns Promise<okpb.RspCommonMsg> 错误码 0:成功
     */
    public async ReqMarkClubUser(clubId: number, userId: number, clubUserId: number, markName: string, markDetail: string): Promise<okpb.RspCommonMsg> {
        let req = {} as okpb.ReqMarkClubUser;
        req.clubId = clubId;
        req.userId = userId;
        req.clubUserId = clubUserId;
        req.markName = markName;
        req.markDetail = markDetail;
        return this.requestPB(OKGAME_ROUTE.ReqMarkClubUser, okpb.ReqMarkClubUser.encode(req), okpb.RspCommonMsg);
    }

    /**
     * 获取俱乐部消息列表
     * @param clubId 俱乐部ID
     * @param page 页码
     */
    public async ReqGetClubMessages(clubId: number, page: number): Promise<okpb.RspJoinClubList> {
        let req: okpb.ReqJoinClubList = {
            clubId: clubId,
            pageNo: page,
        };
        return this.requestPB(OKGAME_ROUTE.ReqGetClubMessages, okpb.ReqJoinClubList.encode(req), okpb.RspJoinClubList);
    }

    /**
     * 处理俱乐部加入请求
     * @param mailId 邮件ID
     * @param opt 消息处理方式 0-拒绝 1-同意
     */
    public async ReqHandleClubJoinRequest(mailId: number, _opt: number): Promise<okpb.RspJoinClubAudit> {
        let req: okpb.ReqJoinClubAudit = {
            mailId: mailId,
            opt: _opt,
        };
        return this.requestPB(OKGAME_ROUTE.ReqHandleClubJoinRequest, okpb.ReqJoinClubAudit.encode(req), okpb.RspJoinClubAudit);
    }

    /**
     * 获得俱乐部成员列表接口
     * @param clubId 俱乐部ID
     */
    public async ReqGetClubMemberList(clubId: number): Promise<okpb.RspGetClubUserList> {
        const reqMemberList = new okpb.ReqGetClubUserList();
        reqMemberList.clubId = clubId;
        reqMemberList.sort = "desc"; //'desc':降序   'asc':升序  (活跃时间)
        reqMemberList.freeSort = "desc"; // 'desc':降序   'asc':升序(免审额度)
        return this.requestPB(OKGAME_ROUTE.ReqGetClubMemberList, okpb.ReqGetClubUserList.encode(reqMemberList), okpb.RspGetClubUserList);
    }

    /**
     * 删除成员接口，请求参数为多个string类型的userId,响应体暂时定位Object
     * @param clubId 俱乐部ID
     * @param userId 操作员id
     * @returns Promise<okpb.RspRemoveClubUser> 删除结果
     */
    public async ReqDeleteClubMember(clubId: number, myUserId: number, clubUserIds: number[]): Promise<okpb.RspRemoveClubUser> {
        let req = {} as okpb.ReqRemoveClubUser;
        req.clubId = clubId;
        req.userId = myUserId;
        req.clubUserIds = clubUserIds;
        return this.requestPB(OKGAME_ROUTE.ReqRemoveClubUser, okpb.ReqRemoveClubUser.encode(req), okpb.RspRemoveClubUser);
    }

    /**
     * 获得代理成员列表接口
     * @param clubId 俱乐部ID
     * @returns Promise<okpb.RspGetClubUserList> 代理成员列表信息
     */
    public async ReqGetAgentMemberList(clubId: number): Promise<okpb.RspGetClubAgentList> {
        const reqAgentList = new okpb.ReqGetClubAgentList();
        reqAgentList.clubId = clubId;
        return this.requestPB(OKGAME_ROUTE.ReqGetAgentMemberList, okpb.ReqGetClubAgentList.encode(reqAgentList), okpb.RspGetClubAgentList);
    }

    /**
     * 设置单个权限开关请求
     * @param clubId 俱乐部ID
     * @param clubUserId 被操作者ID
     * @param opBits 操作位(要设置的权限位)
     * @param isOpen 开启/关闭
     * @returns Promise<okpb.RspSetClubUserPermissions> 设置权限结果，包含变化后的权限位
     */
    public async ReqSetClubUserPermissions(clubId: number, clubUserId: number, opBits: number, isOpen: boolean): Promise<okpb.RspSetClubUserPermissions> {
        let req = {} as okpb.ReqSetClubUserPermissions;
        req.clubId = clubId;
        req.clubUserId = clubUserId;
        req.opBits = opBits;
        req.isOpen = isOpen;
        return this.requestPB(OKGAME_ROUTE.ReqSetClubUserPermissions, okpb.ReqSetClubUserPermissions.encode(req), okpb.RspSetClubUserPermissions);
    }

    /**
     * 设置/取消俱乐部成员为代理下级请求
     * @param clubId 俱乐部ID
     * @param operateUserIds 被修改的用户ID数组
     * @param targetAgentID 目标代理ID(传0表示取消已有的上级)
     * @returns Promise<okpb.RspSetClubUserToAgent> 设置结果
     */
    public async ReqSetClubUserToAgent(clubId: number, operateUserIds: number[], targetAgentID: number): Promise<okpb.RspSetClubUserToAgent> {
        let req = {} as okpb.ReqSetClubUserToAgent;
        req.clubId = clubId;
        req.operateUserIds = operateUserIds;
        req.targetAgentID = targetAgentID;
        console.log("设置/取消俱乐部成员为代理下级请求： ", JSON.stringify(req));
        return this.requestPB(OKGAME_ROUTE.ReqSetClubUserToAgent, okpb.ReqSetClubUserToAgent.encode(req), okpb.RspSetClubUserToAgent);
    }

    /**
     * 获取代理的下级列表请求
     * @param clubId 俱乐部ID
     * @param agentId 代理ID(管理员查询指定代理的下线列表需要传该值，代理查询自己的下线列表可不传)
     * @returns Promise<okpb.RspGetClubAgentSubList> 代理下级列表信息
     */
    public async ReqGetClubAgentSubList(clubId: number, agentId?: number): Promise<okpb.RspGetClubAgentSubList> {
        let req = {} as okpb.ReqGetClubAgentSubList;
        req.clubId = clubId;
        if (agentId !== undefined) {
            req.agentId = agentId;
        }
        return this.requestPB(OKGAME_ROUTE.ReqGetClubAgentSubList, okpb.ReqGetClubAgentSubList.encode(req), okpb.RspGetClubAgentSubList);
    }

    /**
     * 获取总计数据请求
     * @description 重要: 这个是总计的数据
     * @param clubId 俱乐部ID
     * @param userId 成员id
     * @returns Promise<okpb.RspGetUserHistoryData>
     * @deprecated 0701决定: 这个接口已经废弃
     */
    // public async ReqGetTotalAgentHistoryData(clubId: number, userId: number): Promise<okpb.RspGetUserHistoryData> {
    //     let req = {} as okpb.ReqGetUserHistoryData;
    //     req.clubId = clubId;
    //     req.userId = userId;
    //     return this.requestPB(OKGAME_ROUTE.ReqGetAgentHistoryData, okpb.ReqGetUserHistoryData.encode(req), okpb.RspGetUserHistoryData);
    // }

    /**
     * 按日期获取代理历史数据请求
     * @param clubId 俱乐部ID
     * @param agentId 代理ID
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @returns Promise<okpb.RspGetUserHistoryDataByDate>
     * @deprecated 0701决定: 这个接口已经废弃
     */
    // public async ReqGetAgentHistoryDataByDate(clubId: number, agentId: number, beginDate: number, endDate: number): Promise<okpb.RspGetUserHistoryDataByDate> {
    //     let req = {} as okpb.ReqGetUserHistoryDataByDate;
    //     req.clubId = clubId;
    //     req.userId = agentId;
    //     req.beginDate = beginDate;
    //     req.endDate = endDate;
    //     return this.requestPB(OKGAME_ROUTE.ReqGetAgentHistoryDataByDate, okpb.ReqGetUserHistoryDataByDate.encode(req), okpb.RspGetUserHistoryDataByDate);
    // }

    /**
     * 俱乐部柜台 - 兑换俱乐部柜台筹码的请求
     * @param diamond   兑换筹码的钻石数量
     * @returns Promise<okpb.RspExchangeClubCounterChips>
     */
    public async ReqExchangeClubCounterChips(diamond: number): Promise<okpb.RspExchangeClubCounterChips> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqExchangeClubCounterChips();
        req.clubId = clubInfo.clubId;
        req.diamond = diamond;
        return this.requestPB(OKGAME_ROUTE.ReqExchangeClubCounterChips, okpb.ReqExchangeClubCounterChips.encode(req), okpb.RspExchangeClubCounterChips);
    }

    /**
     * 俱乐部柜台 - 申请俱乐部筹码
     * @param chips     申请筹码
     * @param chipType 筹码类型 0:普通筹码申请  1:授信申请，申请俱乐部代币
     * @param chipType  申请类型 0:俱乐部筹码申请  1:授信申请，申请俱乐部代币
     * @returns Promise<okpb.RspApplyClubChips>
     */
    public async ReqApplyClubChips(chips: number, chipType: okpb.ClubChipType): Promise<okpb.RspApplyClubChips> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqApplyClubChips();
        req.clubId = clubInfo.clubId;
        req.chips = chips;
        req.chipType = chipType;
        return this.requestPB(OKGAME_ROUTE.ReqApplyClubChips, okpb.ReqApplyClubChips.encode(req), okpb.RspApplyClubChips);
    }

    /**
     * 俱乐部柜台 - 获取俱乐部柜台信息的请求
     * @returns Promise<okpb.RspGetClubCounterInfo>
     */
    public async ReqGetClubCounterInfo(): Promise<okpb.RspGetClubCounterInfo> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqGetClubCounterInfo();
        req.clubId = clubInfo.clubId;
        return this.requestPB(OKGAME_ROUTE.ReqGetClubCounterInfo, okpb.ReqGetClubCounterInfo.encode(req), okpb.RspGetClubCounterInfo);
    }

    /**
     * 俱乐部柜台 - 获取代理柜台信息的请求
     * @returns Promise<okpb.RspGetClubAgentCounterInfo>
     */
    public async ReqGetClubAgentCounterInfo(): Promise<okpb.RspGetClubAgentCounterInfo> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqGetClubAgentCounterInfo();
        req.clubId = clubInfo.clubId;
        return this.requestPB(OKGAME_ROUTE.ReqGetClubAgentCounterInfo, okpb.ReqGetClubAgentCounterInfo.encode(req), okpb.RspGetClubAgentCounterInfo);
    }

    /**
     * 修改俱乐部用户角色请求
     * @param clubId 俱乐部ID
     * @param operateUserIds 被修改的用户ID数组(目前需求是单个，传一个即可)
     * @param targetIdentity 目标角色(代理需要首先清理完下线才能修改角色)
     * @returns Promise<okpb.RspChangeClubUserIdentity> 修改用户角色结果
     */
    public async ReqChangeClubUserIdentity(clubId: number, operateUserIds: number[], targetIdentity: okpb.Identity): Promise<okpb.RspChangeClubUserIdentity> {
        let req = {} as okpb.ReqChangeClubUserIdentity;
        req.clubId = clubId;
        req.operateUserIds = operateUserIds;
        req.targetIdentity = targetIdentity;
        console.log("修改用户角色，请求参数:", JSON.stringify(req));
        return this.requestPB(OKGAME_ROUTE.ReqChangeClubUserIdentity, okpb.ReqChangeClubUserIdentity.encode(req), okpb.RspChangeClubUserIdentity);
    }

    /**
     * 获取俱乐部筹码交易记录列表
     * @param createTime 交易时间（最后一条记录的时间戳，用于分页，首页为当前时间）
     * @param pageSize 每页条数
     * @param chipType 筹码类型
     * @param targetId 被操作人UID（0表示所有）
     * @returns Promise<okpb.RspGetClubChipTransactionList> 俱乐部筹码交易记录列表
     */
    public async ReqGetClubChipTransactionList(createTime: number, pageSize: number, chipType: okpb.ClubChipType, targetId: number = 0): Promise<okpb.RspGetClubChipTransactionList> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = {} as okpb.ReqGetClubChipTransactionList;
        req.clubId = clubInfo.clubId;
        req.createTime = createTime;
        req.pageSize = pageSize;
        req.chipType = chipType;
        req.targetId = targetId;
        return this.requestPB(OKGAME_ROUTE.ReqGetClubChipTransactionList, okpb.ReqGetClubChipTransactionList.encode(req), okpb.RspGetClubChipTransactionList);
    }

    /**
     * 俱乐部柜台 - 获取俱乐部筹码申请列表
     * @returns Promise<okpb.RspGetClubChipsApplyList>
     */
    public async ReqGetClubChipsApplyList(): Promise<okpb.RspGetClubChipsApplyList> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqGetClubChipsApplyList();
        req.clubId = clubInfo.clubId;
        return this.requestPB(OKGAME_ROUTE.ReqGetClubChipsApplyList, okpb.ReqGetClubChipsApplyList.encode(req), okpb.RspGetClubChipsApplyList);
    }

    /**
     * 俱乐部柜台 - 审核俱乐部筹码申请的请求
     * @param isApproved    是否通过 true:允许，false:拒绝
     * @param applyId       申请玩家列表
     * @returns Promise<okpb.RspAuditClubChipsApply>
     */
    public async ReqAuditClubChipsApply(isApproved: boolean, applyId: number[]): Promise<okpb.RspAuditClubChipsApply> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqAuditClubChipsApply();
        req.clubId = clubInfo.clubId;
        req.isApproved = isApproved;
        req.applyId = applyId;
        return this.requestPB(OKGAME_ROUTE.ReqAuditClubChipsApply, okpb.ReqAuditClubChipsApply.encode(req), okpb.RspAuditClubChipsApply);
    }

    // /**
    //  * 俱乐部柜台 - 获取交易记录的请求
    //  * @param clubId        俱乐部ID
    //  * @param createTime    交易时间【最后一条记录的时间戳，用于分页，首页为当前时间】
    //  * @param pageSize      每页条数
    //  * @returns Promise<okpb.RspGetClubChipTransactionList>
    //  */
    // public async ReqGetClubChipTransactionList(clubId: number, createTime: number, pageSize: number): Promise<okpb.RspGetClubChipTransactionList> {
    //     let req = new okpb.ReqGetClubChipTransactionList();
    //     req.clubId = clubId;
    //     req.createTime = createTime;
    //     req.pageSize = pageSize;
    //     return this.requestPB(OKGAME_ROUTE.ReqGetClubChipTransactionList, okpb.ReqGetClubChipTransactionList.encode(req), okpb.RspGetClubChipTransactionList);
    // }

    /**
     * 俱乐部柜台 - 回收俱乐部筹码的请求
     * @returns Promise<okpb.RspRecycleClubChips>
     */
    public async ReqRecycleClubChips(isAll: boolean, userIds: number[], chips?: number, chipType: okpb.ClubChipType = 1): Promise<okpb.RspRecycleClubChips> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqRecycleClubChips();
        req.clubId = clubInfo.clubId;
        req.isAll = isAll;
        req.clubUserIds = userIds;
        req.chipType = chipType;
        if (chips) req.chips = chips;
        return this.requestPB(OKGAME_ROUTE.ReqRecycleClubChips, okpb.ReqRecycleClubChips.encode(req), okpb.RspRecycleClubChips);
    }

    /**
     * 俱乐部柜台 - 发放俱乐部筹码的请求
     * @returns Promise<okpb.RspGrantClubChips>
     */
    public async ReqGrantClubChips(userIds: number[], chips: number, chipType: okpb.ClubChipType): Promise<okpb.RspGrantClubChips> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqGrantClubChips();
        req.clubId = clubInfo.clubId;
        req.clubUserIds = userIds;
        req.chips = chips;
        req.chipType = chipType;
        return this.requestPB(OKGAME_ROUTE.ReqGrantClubChips, okpb.ReqGrantClubChips.encode(req), okpb.RspGrantClubChips);
    }

    /**
     * 俱乐部柜台 - 请求查询自己的筹码信息
     * @param userIds       俱乐部
     * @returns Promise<okpb.RspClubUserChipBalance>
     */
    public async ReqClubMyUserChip(): Promise<okpb.RspClubUserChipBalance> {
        let myUserId = 0;
        return this.ReqClubUserChipBalance([myUserId]);
    }

    /**
     * 俱乐部柜台 - 请求俱乐部用户列表筹码余额
     * @param userIds       俱乐部用户id列表
     * @returns Promise<okpb.RspClubUserChipBalance>
     */
    public async ReqClubUserChipBalance(userIds: number[]): Promise<okpb.RspClubUserChipBalance> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = new okpb.ReqClubUserChipBalance();
        req.clubId = clubInfo.clubId;
        req.userIds = userIds;
        return this.requestPB(OKGAME_ROUTE.ReqClubUserChipBalance, okpb.ReqClubUserChipBalance.encode(req), okpb.RspClubUserChipBalance);
    }

    /**
     * 获取玩家历史数据请求
     * @param clubId 俱乐部ID
     * @param userId 成员ID
     * @returns Promise<okpb.RspGetUserHistoryData> 玩家历史数据
     */
    public async ReqGetUserHistoryData(clubId: number, userId: number): Promise<okpb.RspGetUserHistoryData> {
        let req = {} as okpb.ReqGetUserHistoryData;
        req.clubId = clubId;
        req.userId = userId;
        return this.requestPB(OKGAME_ROUTE.ReqGetUserHistoryData, okpb.ReqGetUserHistoryData.encode(req), okpb.RspGetUserHistoryData);
    }

    /**
     * 按日期获取玩家历史数据请求
     * @param clubId 俱乐部ID
     * @param userId 成员ID
     * @param beginDate 开始日期
     * @param endDate 结束日期
     * @returns Promise<okpb.RspGetUserHistoryDataByDate> 玩家历史数据
     */
    public async ReqGetUserHistoryDataByDate(clubId: number, userId: number, beginDate: number, endDate: number): Promise<okpb.RspGetUserHistoryDataByDate> {
        let req = {} as okpb.ReqGetUserHistoryDataByDate;
        req.clubId = clubId;
        req.userId = userId;
        req.beginDate = beginDate;
        req.endDate = endDate;
        return this.requestPB(OKGAME_ROUTE.ReqGetUserHistoryDataByDate, okpb.ReqGetUserHistoryDataByDate.encode(req), okpb.RspGetUserHistoryDataByDate);
    }

    /**
     * 查看俱乐部代理历史数据统计
     * @param clubId 俱乐部ID
     * @param agentId 被查看代理玩家ID
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @returns Promise<okpb.RspClubAgentDataStatistics> 代理历史数据统计
     */
    public async ReqClubAgentDataStatistics(clubId: number, agentId: number, beginDate: number, endDate: number): Promise<okpb.RspClubAgentDataStatistics> {
        let req = {} as okpb.ReqClubAgentDataStatistics;
        req.clubId = clubId;
        req.agentId = agentId;
        req.beginDate = beginDate;
        req.endDate = endDate;
        return this.requestPB(OKGAME_ROUTE.ReqClubAgentDataStatistics, okpb.ReqClubAgentDataStatistics.encode(req), okpb.RspClubAgentDataStatistics);
    }

    /**
     * 获取俱乐部用户备注列表
     * @param clubId 俱乐部ID
     * @returns Promise<okpb.RspClubUserRemarkList> 俱乐部用户备注列表
     */
    public async ReqGetClubUserRemarkList(clubId: number): Promise<okpb.RspClubUserRemarkList> {
        let req = {} as okpb.ReqClubUserRemarkList;
        req.clubId = clubId;
        return this.requestPB(OKGAME_ROUTE.ReqGetClubUserRemarkList, okpb.ReqClubUserRemarkList.encode(req), okpb.RspClubUserRemarkList);
    }

    /**
     * 请求俱乐部玩家自身信息
     * @returns Promise<okpb.RspClubUserSelfInfo> 俱乐部玩家自身信息
     */
    public async ReqClubUserSelfInfo(): Promise<okpb.RspClubUserSelfInfo> {
        let clubInfo = ClubDataManager.getInstance().getClubInfo();
        if (!clubInfo) return;
        let req = {} as okpb.ReqClubUserSelfInfo;
        req.clubId = clubInfo.clubId;
        return this.requestPB(OKGAME_ROUTE.ReqClubUserSelfInfo, okpb.ReqClubUserSelfInfo.encode(req), okpb.RspClubUserSelfInfo);
    }
}
