
/**
 * 游戏通用模块工具
 */
export default class ComGameUtils {
    /**
     * 飞筹码
     * @param node 
     * @param pos 
     * @param count 
     * @param cb 
     */
    static playFlyBetAnim(node: cc.Node, pos: cc.Vec3, dt: number, count: number, cb?: Function) {
        let anim = cc.tween(node).to(dt, {
            position: pos
        });
        if(cb){
            anim.call(cb);
        }
        anim.call(()=>{
            node.removeFromParent(true);
            node.destroy();
        })
        anim.start();
        node.active = true;
    }
}