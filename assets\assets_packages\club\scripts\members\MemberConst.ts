/**
 * 公用的一些配置
 */
export default class MemberConst {
    static readonly COLOR_WHITE = cc.color().fromHEX("#ffffff");
    static readonly COLOR_YELLOW = cc.color().fromHEX("#ffd400");
    static readonly COLOR_GRAY = cc.color().fromHEX("#9699c7");
    static readonly COLOR_PINK = cc.color().fromHEX("#ff7a7a");
    static readonly COLOR_GREEN = cc.color().fromHEX("#7aff8a");
}

/**
 * 排序类型
 * @description 用于成员列表的排序类型
 */
export enum SortTypeEnum {
    /**
     * 服务费
     */
    ServiceFee = 0,
    /**
     * 盈亏
     */
    Profit = 1,
    /**
     * 手牌数
     */
    HandCardCount = 2,
    /**
     * 上次登录
     */
    LastLogin = 3,
    /**
     * 上次玩牌
     */
    LastPlay = 4,
    /**
     * 筹码
     */
    ChipBalance = 5,
    /**
     * 加入俱乐部时间
     */
    JoinClubTime = 6,
}

/**
 * 代理排序类型
 * @description 用于代理下拉菜单的排序类型
 */
export enum AgentSortTypeEnum {
    /**
     * 本周盈亏
     */
    ThisWeekProfitLoss = 0,
    /**
     * 本周服务费
     */
    ThisWeekServiceFee = 1,
    /**
     * 上周盈亏
     */
    LastWeekProfitLoss = 2,
    /**
     * 上周服务费
     */
    LastWeekServiceFee = 3,
    /**
     * 总计盈亏
     */
    TotalProfitLoss = 4,
    /**
     * 总计服务费
     */
    TotalServiceFee = 5,
}

/**
 * 详情页功能模块
 * @description 用于详情页的功能模块权限配置
 */
export enum DetailFeatureEnum {
    /**
     * 基础信息、历史数据、备注信息
     */
    BaseInfo = 0,
    /**
     * 角色设置
     */
    RoleSetting = 1,
    /**
     * 权限设置
     */
    PermissionSetting = 2,
    /**
     * 所属代理
     */
    AgentInfo = 3,
    /**
     * 删除成员
     */
    DeleteMember = 4,
}

/**
 * 身份设置类型
 * @description 用于身份设置的类型
 */
export enum IdentitySettingEnum {
    /**
     * 管理员和开桌员
     */
    AdminAndTableOperator = 0,
    /**
     * 超级代理
     */
    SuperAgent = 1,
    /**
     * 代理
     */
    Agent = 2,
    /**
     * 普通成员
     */
    Member = 3,
}
