import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import { PermissionUtils } from "../PermissionUtils";
import ScrollViewHelper from "../member/ScrollViewHelper";
import ClubAPI from "../../ClubAPI";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameData from "../../../../okgame/public/OKGameData";
import ItemHistoryChipsView from "./ItemHistoryChipsView";
import ClubManger from "../../ClubManger";
import ClubDataManager from "../../ClubDataManager";

const { ccclass, property } = cc._decorator;
const TAG = "[ChipsHistoryDialog]";
/**
 * 代理授信弹框： 筹码历史记录弹框
 */
@ccclass
export default class ChipsHistoryDialog extends UBaseDialog {
    @property(cc.Prefab)
    itemHistoryViewPrefab: cc.Prefab = null;

    @property(cc.Label)
    titleTxt: cc.Label = null;

    @property(cc.Button)
    closeBtn: cc.Button = null;

    @property(cc.Label)
    balanceTxt: cc.Label = null;

    @property(cc.Label)
    balanceValueTxt: cc.Label = null;

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Node)
    content: cc.Node = null;

    @property(cc.Button)
    btnCollection: cc.Button = null;

    @property(cc.Button)
    btnSend: cc.Button = null;

    // ScrollViewHelper 相关
    private scrollViewHelper: ScrollViewHelper = null;
    /**
     * 被操作人UID
     */
    private targetUserId: number = 0;
    /**
     * 俱乐部ID
     */
    private clubId: number = 0;
    /**
     * 最后一条记录的时间戳
     */
    private lastCreateTime: number = 0;
    /**
     * 授信余额
     */
    private chipBalance: number = 0;

    private dataList: okpb.ClubChipTransactionData[] = [];

    onUILoad(): void {
        this.initView();
        this.initScrollViewHelper();
    }

    private initView(): void {
        // 获取当前俱乐部ID - 需要从外部传入
        // this.clubId = OKGameData.getInstance().getCurrentClubId();
        this.onRegisterEvent(this.closeBtn.node, this.closeDialog.bind(this));
        this.onRegisterEvent(this.btnCollection.node, this.onCollection.bind(this));
        this.onRegisterEvent(this.btnSend.node, this.onSend.bind(this));
    }

    /**
     * 显示筹码历史记录弹框
     * @param params balance：授信余额，targetUserId：被操作人UID，clubId：俱乐部ID，myPermissions：权限位掩码
     */
    onShow(params: { myPermissions: number; targetUserId: number; chipBalance: number }): void {
        console.log(TAG, "onShow() 显示筹码历史记录弹框,接收到的传参: ", JSON.stringify(params));
        this.chipBalance = params.chipBalance;
        this.balanceValueTxt.string = this.chipBalance.toString();
        this.targetUserId = params.targetUserId;
        this.clubId = ClubDataManager.getInstance().getClubId();

        // 收回筹码权限
        const hasTakeChipsPermission = PermissionUtils.hasTakeChipsPermission(params.myPermissions);
        this.btnCollection.interactable = hasTakeChipsPermission;
        // 发放筹码权限
        const hasSendChipsPermission = PermissionUtils.hasSendChipsPermission(params.myPermissions);
        this.btnSend.interactable = hasSendChipsPermission;
        console.log(TAG, "onShow() 收回筹码权限:", hasTakeChipsPermission, "发放筹码权限:", hasSendChipsPermission, "权限位掩码:", params.myPermissions);

        // 初始化加载数据
        this.initLoadData();
    }

    /**
     * 初始化ScrollViewHelper
     */
    private initScrollViewHelper(): void {
        this.scrollViewHelper = new ScrollViewHelper();
        this.scrollViewHelper.init(this.scrollView, this.onRefresh.bind(this), this.onLoadMore.bind(this));
    }

    /**
     * 下拉刷新回调
     */
    private onRefresh(): void {
        console.log(TAG, "onRefresh() 下拉刷新");
        this.scrollViewHelper.resetPage();
        this.lastCreateTime = Date.now(); // 重置为当前时间
        this.requestChipTransactionList(true);
    }

    /**
     * 上拉加载更多回调
     */
    private onLoadMore(): void {
        console.log(TAG, "onLoadMore() 上拉加载更多");
        this.requestChipTransactionList(false);
    }

    /**
     * 请求筹码交易记录列表
     * @param isRefresh 是否为刷新操作
     */
    private async requestChipTransactionList(isRefresh: boolean = false): Promise<void> {
        try {
            this.scrollViewHelper.setIsRequesting(true);

            console.log(TAG, "Req 参数:", {
                clubId: this.clubId,
                targetUserId: this.targetUserId,
                createTime: this.lastCreateTime,
                isRefresh,
            });

            const result = await ClubAPI.getInstance().ReqGetClubChipTransactionList(
                this.lastCreateTime,
                10, // 每页10条
                okpb.ClubChipType.CCT_CREDIT,
                this.targetUserId
            );

            if (result.errorCode === 0) {
                console.log(TAG, "requestChipTransactionList() 请求成功", result.data);
                // 更新最后一条记录的时间戳，用于下次分页
                if (result.data && result.data.length > 0) {
                    this.lastCreateTime = result.data[result.data.length - 1].createTime;
                }
                // 处理数据，更新UI
                this.handleTransactionListData(result.data, isRefresh);
            } else {
                console.error(TAG, "requestChipTransactionList() 请求失败:", result.errMsg);
            }
        } catch (error) {
            console.error(TAG, "requestChipTransactionList() 请求异常:", error);
        } finally {
            this.scrollViewHelper.setIsRequesting(false);
        }
    }

    /**
     * 处理交易记录数据
     * @param data 交易记录数据
     * @param isRefresh 是否为刷新操作
     */
    private handleTransactionListData(data: okpb.ClubChipTransactionData[], isRefresh: boolean): void {
        console.log(TAG, "handleTransactionListData() 处理数据", {
            dataLength: data?.length || 0,
            isRefresh,
        });

        if (!data || data.length === 0) {
            console.log(TAG, "handleTransactionListData() 筹码交易记录为空");
            return;
        }

        if (isRefresh) {
            // 刷新：清空现有列表，添加新数据
            this.content.removeAllChildren();
            this.dataList = [];
        }

        // 添加新的列表项到content中
        data.forEach((item) => {
            // 创建列表项UI
            const itemView = cc.instantiate(this.itemHistoryViewPrefab);
            itemView.getComponent(ItemHistoryChipsView).setData(item);
            this.content.addChild(itemView);
            this.dataList.push(item);
        });
    }

    /**
     * 初始化加载数据
     */
    private initLoadData(): void {
        this.scrollViewHelper.resetPage();
        this.lastCreateTime = Date.now();
        this.requestChipTransactionList(true);
    }

    /**
     * 刷新筹码交易记录列表
     * @description 公共方法，可供外部调用刷新数据
     */
    public refreshTransactionList(): void {
        console.log(TAG, "refreshTransactionList() 外部调用刷新交易记录");
        this.onRefresh();
    }

    private onCollection(): void {
        console.log(TAG, "onCollection() 收回筹码");
        ClubManger.getInstance().showIssueAndCollectChipsDialog("collect", this.targetUserId, this.chipBalance, this.onChipsOperationSuccess.bind(this));
    }

    private onSend(): void {
        console.log(TAG, "onSend() 发放筹码");
        ClubManger.getInstance().showIssueAndCollectChipsDialog("issue", this.targetUserId, this.chipBalance, this.onChipsOperationSuccess.bind(this));
    }

    /**
     * 筹码操作成功回调
     * @description 发放或回收筹码成功后，刷新历史记录列表
     */
    private onChipsOperationSuccess(): void {
        console.log(TAG, "onChipsOperationSuccess() 筹码操作成功，刷新历史记录");
        // 触发下拉刷新，重新加载数据
        this.onRefresh();
    }

    onDestroy(): void {
        // 清理ScrollViewHelper
        if (this.scrollViewHelper) {
            this.scrollViewHelper.destroy();
            this.scrollViewHelper = null;
        }
        super.onDestroy();
    }
}
