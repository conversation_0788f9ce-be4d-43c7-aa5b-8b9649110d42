import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseView from "../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UIClubAvatar from "../../../okgame/club/scripts/UIClubAvatar";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameUtils from "../../../okgame/public/OKGameUtils";
import ClubDataManager from "../ClubDataManager";
import ClubManger, { CLUB_EVENT, CLUB_UIID } from "../ClubManger";
import { EDIT_TYPE_NOTICE } from "../editprofile/EditSubView";
import { PermissionUtils } from "../members/PermissionUtils";
import UIClubInfoView_auto from "./UIClubInfoView_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubInfoView extends UBaseView {
    protected ui: UIClubInfoView_auto = null;
    private clubInfo: okpb.ClubResp = null;
    onUILoad(): void {
        this.ui = new UIClubInfoView_auto(this.node);
        this.onRegisterEvent(this.ui.clubNoticeNode, this.onNoticeBtnClick.bind(this));
        this.onRegisterEvent(this.ui.avatarNode, this.onEditClubProfile.bind(this));
        this.onRegisterEvent(this.ui.infoNode, this.onEditClubProfile.bind(this));
        this.onRegisterEvent(this.ui.clubCashierNode, this.onClubCashierNodeClick.bind(this));
        this.onRegisterEvent(this.ui.clubCashierAdd, this.onClubCashierNodeClick.bind(this));
        this.onRegisterEvent(this.ui.clubChipNode, this.onClubChipNodeClick.bind(this));
        this.onRegisterEvent(this.ui.clubChipAdd, this.onClubChipNodeClick.bind(this));
        this.onRegisterEvent(this.ui.clubServiceBtn, this.onServiceBtnClick.bind(this));

        //柜台筹码相关数据更新
        this.registerEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO, this.updateClubCounterInfo.bind(this));
        //更新个人信息，当前只有个人筹码显示
        this.registerEvent(CLUB_EVENT.UPDATE_CLUB_USER_SELF_INFO, this.updateClubUserSelfInfo.bind(this));
    }

    public updateInfo(clubInfo: okpb.ClubResp) {
        this.clubInfo = clubInfo;
        this.ui.clubId.getComponent(cc.Label).string = `${this.clubInfo.clubId}`;
        this.updateClubName(this.clubInfo.clubName);
        this.updateNotices(this.clubInfo.intro);
        this.updateClubAvatar(this.clubInfo.icon);
        this.updateClubChips(0);
        this.updateclubCashier(0);
    }

    /**
     * 更新俱乐部名称
     * @param clubName
     */
    private updateClubName(clubName: string) {
        this.ui.clubName.getComponent(cc.Label).string = `${clubName}`;
    }

    private updateClubAvatar(icon: string) {
        if (!icon || icon.length == 0) {
            return;
        }
        this.ui.avatarNode.getComponent(UPrefabContainer).getNodeComponent(UIClubAvatar).setIcon(icon);
    }

    /**
     * 更新公告
     * @param noticeText
     */
    private updateNotices(noticeText: string) {
        noticeText = noticeText || ULanguage.getInstance().getLangByID("club.welcomeToOKPoker");
        this.ui.noticeText.getComponent(cc.Label).string = OKGameUtils.getStringMaxBit(noticeText.replace(/[\r\n]+/g, " "), 50);
    }

    /**
     * 更新俱乐部筹码
     * @param chips
     */
    private updateClubChips(chips: number) {
        this.ui.clubChipText.getComponent(cc.Label).string = `${chips}`;
    }

    /**
     * 更新俱乐部钱柜金额
     * @param chips
     */
    private updateclubCashier(cashier: number) {
        this.ui.clubCashierText.getComponent(cc.Label).string = `${cashier}`;
    }

    //更新当前的柜台筹码信息
    updateClubCounterInfo() {
        let data = ClubDataManager.getInstance().getClubCounterInfo();
        if (data) {
            let counterInfo = data.data;
            this.updateclubCashier(counterInfo.counterChips);
        }
    }

    //更新当前个人信息（目前个人筹码）
    updateClubUserSelfInfo() {
        let data = ClubDataManager.getInstance().getClubUserSelfInfo();
        if (data) {
            this.updateClubChips(data.chipBalance);
        }
    }

    /**
     * 编辑俱乐部信息
     * 根据用户身份显示不同的编辑界面
     * 0: 普通成员 1：管理员 2:创建者
     */
    private onEditClubProfile(): void {
        if (!this.clubInfo) {
            console.warn("[UIClubScene] 俱乐部信息为空，无法编辑");
            return;
        }
        console.log("用户角色:", this.clubInfo.identity);
        let isEdit = this.isHaveEditClubProfilePermission();
        if (isEdit) {
            ClubManger.getInstance().showClubEditProfileView(this.clubInfo);
        } else {
            ClubManger.getInstance().showClubProfilePreviewView(this.clubInfo);
        }
    }

    private onNoticeBtnClick() {
        if (!this.clubInfo) {
            console.warn("[UIClubScene] 俱乐部信息为空，无法编辑公告");
            return;
        }
        console.log("用户角色:", this.clubInfo.identity);
        let isEdit = this.isHaveEditClubProfilePermission();
        if (isEdit) {
            ClubManger.getInstance().showClubEditProfileSubView(this.clubInfo, EDIT_TYPE_NOTICE, (clubInfo: okpb.ClubResp) => {
                this.clubInfo = clubInfo;
                this.emitEvent(CLUB_EVENT.UPDATE_CLUB_INFO, this.clubInfo);
            });
        } else {
            ClubManger.getInstance().showClubProfilePreviewView(this.clubInfo);
        }
    }

    /**
     * 是否有编辑俱乐部信息的权限
     * @description 创建者、管理员可以编辑俱乐部信息, 但是还有一层是创建者可以对管理员的编辑权限进行控制
     */
    private isHaveEditClubProfilePermission(): boolean {
        const isHavePermission = PermissionUtils.hasEditInfoPermission(this.clubInfo.permissions);
        console.log("是否有编辑俱乐部信息的权限:", isHavePermission, "权限位掩码:", this.clubInfo.permissions);
        return ClubManger.getInstance().hasEditClubProfileAccess(this.clubInfo) && isHavePermission;
    }

    /**
     * 钱柜筹码兑换
     */
    private onClubCashierNodeClick() {
        console.log("钱柜筹码兑换");
        if (this.clubInfo.identity >= okpb.Identity.IDE_AGENT) {
            ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubCreditRequestView, this.clubInfo);
        }
    }

    /**
     * 俱乐部筹码申请
     */
    private onClubChipNodeClick() {
        console.log("俱乐部筹码申请");
        ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubChipRequestView, this.clubInfo);
    }
    /**
     * 服务中心
     */
    private onServiceBtnClick() {
        console.log("客服");
    }
}
