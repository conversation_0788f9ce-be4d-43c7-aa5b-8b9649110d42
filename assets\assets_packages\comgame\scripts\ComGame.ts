import { UStorage } from "../../../framwork/utils/UStorage";
import { EnterRoomData } from "../../okgame/room/scripts/OKRoomManger";
import { ComGameKey } from "../pubilc/ComGameConst";
import ComGameData from "../pubilc/ComGameData";
import ComGameManager from "../pubilc/ComGameManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ComGame extends cc.Component {

    onComEnter(data: EnterRoomData) {
        ComGameData.getInstance().onEnterRoom(data.enterRoomData);
    }

    onComExit() {

    }

    loadGameBg(): Promise<any> {
        return new Promise((resolve) => {
            //获取缓存的值
            let bgStyle = ComGameManager.getInstance().getGameBgStyle();
            //加载背景资源
            ComGameManager.getInstance().setGameBgStyle(bgStyle, resolve)
        })
    }
}
