import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import OKExtManager from "../../../okgame/public/OKExtManager";
import ComGameWs from "../../network/ComGameWs";
import UIGameUserSelfView_auto from "./UIGameUserSelfView_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIGameUserSelfView extends UBaseDialog {
	protected ui: UIGameUserSelfView_auto = null;
	onUILoad(): void {
		this.ui = new UIGameUserSelfView_auto(this.node);
		this.initEmojiView();
	}

	initEmojiView() {
		this.ui.emojiItem.active = false;
		for (let i = 1; i <= 12; i++) {
			let id = i < 10 ? '0' + i : '' + i;
			let item = cc.instantiate(this.ui.emojiItem)
			item.parent = this.ui.propNode;
			item.active = true;
			//---
			let icon = item.getChildByName(this.ui.emojiIcon.name).getComponent(cc.Sprite);
			OKExtManager.getInstance().loadEmojiIcon(id, (err: Error, spriteFrame: cc.SpriteFrame) => {
				if (!cc.isValid(item)) {
					return;
				}
				icon.spriteFrame = spriteFrame;
			})
			this.onRegisterEvent(item, () => {
				ComGameWs.getInstance().sendEmoji(id);
				this.closeDialog();
			})
		}
	}
}