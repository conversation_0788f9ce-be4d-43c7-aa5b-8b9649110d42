export default class UIGameUserSelfView_auto {
    node:cc.Node = null;   
	GameUserSelfView: cc.Node;
	mask: cc.Node;
	view: cc.Node;
	grayBg: cc.Node;
	userInfo: cc.Node;
	tableInfo: cc.Node;
	propNode: cc.Node;
	emojiItem: cc.Node;
	emojiIcon: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.GameUserSelfView = this.node;
		this.mask = this.GameUserSelfView.getChildByName("mask");
		this.view = this.GameUserSelfView.getChildByName("view");
		this.grayBg = this.view.getChildByName("grayBg");
		this.userInfo = this.grayBg.getChildByName("userInfo-");
		this.tableInfo = this.grayBg.getChildByName("tableInfo-");
		this.propNode = this.grayBg.getChildByName("propNode");
		this.emojiItem = this.propNode.getChildByName("emojiItem");
		this.emojiIcon = this.emojiItem.getChildByName("emojiIcon");

    }
}
