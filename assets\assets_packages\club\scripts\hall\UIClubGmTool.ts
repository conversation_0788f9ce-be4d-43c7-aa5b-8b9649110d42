import UIGmTool from "../../../okgame/common/scripts/UIGmTool";
import ClubManger, { CLUB_UIID } from "../ClubManger";
import UIClubScene from "./UIClubScene";

const { ccclass, property } = cc._decorator;

@ccclass
export default class UIClubGmTool extends UIGmTool {
    showMainMenu() {
        super.showMainMenu();
        this.addItem("UI功能", this.showRoomUI);
    }

    private showRoomUI() {
        this.clearAllItem();
        this.addItem("返回", this.showMainMenu);

        //数据页面
        this.addItem('数据', () => {
            let scene = this.node.getComponent(UIClubScene);
            let clubInfo = null;
            if (scene) clubInfo = scene.getClubInfo();
            ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubDetailDataView, clubInfo);
        });

        //柜台页面
        this.addItem('柜台', () => {
            let scene = this.node.getComponent(UIClubScene);
            let clubInfo = null;
            if (scene) clubInfo = scene.getClubInfo();
            ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubCashierView, clubInfo);
        });
    }
}
