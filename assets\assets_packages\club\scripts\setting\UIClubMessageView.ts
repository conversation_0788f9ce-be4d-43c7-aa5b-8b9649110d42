import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UIClubMessageView_auto from "./UIClubMessageView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubMessageView]";

/**
 * 俱乐部消息设置
 *
 * @export
 * @class UIClubMessageView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubMessageView")
export default class UIClubMessageView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubMessageView_auto = null;

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubMessageView_auto(this.node);
        this.initEvent();
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubMessageView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIClubMessageView
     */
    private initEvent() {
        this.onRegisterEvent(this.ui.closedBtn, this.onClosedBtnClick.bind(this));
        this.onRegisterEvent(this.ui.memberApplyToggle, this.onClickMemberApplyToggle.bind(this));
        this.onRegisterEvent(this.ui.memberExitToggle, this.onClickMemberExitToggle.bind(this));
        this.onRegisterEvent(this.ui.chipApplyToggle, this.onClickChipApplyToggle.bind(this));
    }

    //#endregion

    //#region 事件处理
    private onClosedBtnClick() {
        this.closeDialog();
    }

    /**
     * 点击成员退出开关按钮
     *
     * @private
     * @memberof UIClubMessageView
     */
    private onClickMemberExitToggle(e: cc.Toggle) {
        this.log(`onClickMemberExitToggle = ${e.isChecked}`);
    }

    /**
     * 点击成员加入开关按钮
     *
     * @private
     * @memberof UIClubMessageView
     */
    private onClickMemberApplyToggle(e: cc.Toggle) {
        this.log(`onClickMemberApplyToggle = ${e.isChecked}`);
    }

    /**
     * 点击筹码申请开关按钮
     *
     * @private
     * @memberof UIClubMessageView
     */
    private onClickChipApplyToggle(e: cc.Toggle) {
        this.log(`onClickChipApplyToggle = ${e.isChecked}`);
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
