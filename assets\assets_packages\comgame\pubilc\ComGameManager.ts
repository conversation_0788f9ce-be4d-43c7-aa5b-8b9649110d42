import UAssetDispenser from "../../../framwork/utils/UAssetDispenser";
import { UEventManager } from "../../../framwork/utils/UEventManager";
import { UStorage } from "../../../framwork/utils/UStorage";
import { ComGameEvent, ComGameKey } from "./ComGameConst";

/**
 * 游戏通用模块管理器类
 */
export default class ComGameManager extends UAssetDispenser {
    //------------
    private static _instance = null;
    public static getInstance(): ComGameManager {
        if (this._instance == null) {
            this._instance = new ComGameManager();
        }
        return this._instance;
    }
    //------------
    private _curBgStyle: number = -1;
    private _curBgPrefab: cc.Prefab = null;
    //------------
    constructor() {
        super('comgame');
    }

    loadGameBgStyle(): number {
        return this._curBgStyle;
    }


    setGameBgStyle(style: number, cb?: Function) {
        //背景数量只有6个
        if (style < 0 || style > 5) {
            style = 0;
        }
        if (this._curBgStyle == style) {
            cb && cb(true)
            return
        }
        let curBgStyle = this._curBgStyle;
        this._curBgStyle = style;
        let path = this.getGameBgPath(style);
        this.loadPrefab(path, (err: Error, prefab: cc.Prefab) => {
            if (err) {
                console.error(err)
                if (this._curBgStyle == style) {
                    this._curBgStyle = curBgStyle;
                }
                cb && cb(false)
                return;
            }
            if (this._curBgStyle != style) {
                prefab.destroy();
                cb && cb(false)
                return
            }
            UStorage.getInstance().set(ComGameKey.GameBgStyle, style, false);
            this.setGameBgPrefab(style, prefab)
            cb && cb(true)
        })
    }

    getGameBgStyle(): number {
        return UStorage.getInstance().get(ComGameKey.GameBgStyle, false, 0);
    }

    setGameBgPrefab(style: number, prefab: cc.Prefab) {
        this._curBgStyle = style;
        let curBgPrefab = this._curBgPrefab
        this._curBgPrefab = prefab;
        if (this._curBgPrefab) {
            this._curBgPrefab.addRef();
        }
        UEventManager.getInstance().emit(ComGameEvent.UpdateGameBg)
        if (curBgPrefab) {
            curBgPrefab.decRef();
        }
    }

    createGameBg(): cc.Node {
        if (!this._curBgPrefab) {
            return null;
        }
        return cc.instantiate(this._curBgPrefab);
    }

    private getGameBgPath(style: number) {
        let level = this.getResolutionLevel();
        return `view/bg/${level}/GameBg${style}`;
    }

    private getResolutionLevel(): string {
        if (this.isLongScreen()) {
            return 'xxhdpi';
        } else {
            return 'hdpi';
        }
    }

    /**
     *  检测屏幕类型
     * @returns 如果是长屏幕返回 true，标准屏幕返回 false
     * @returns 
     */
    public isLongScreen(): boolean {
        const { width, height } = cc.view.getFrameSize();
        const aspectRatio = height / width;
        const standardAspectRatio = 16 / 9;
        const tolerance = 0.001;
        // 考虑误差范围进行比较
        return aspectRatio > (standardAspectRatio + tolerance);
    }


    /**
     * 获取当前窗口高度与设计分辨率高度的比值
     * @returns 当前窗口高度与设计分辨率高度的比值
     */
    public getWinSizeToDesignHeightRatio() {
        return cc.winSize.height / cc.view.getDesignResolutionSize().height;
    }

}