import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import { CLUB_EVENT } from "../ClubManger";
import UIClubChipRequestView_auto from "./UIClubChipRequestView_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubChipRequestView extends UBaseDialog {
	protected ui: UIClubChipRequestView_auto = null;


	onUILoad(): void {
		this.ui = new UIClubChipRequestView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.onRegisterEvent(this.ui.btnOK, this.onBtnOK.bind(this));

		//更新个人信息，当前只有个人筹码显示
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_USER_SELF_INFO, this.updateClubUserSelfInfo.bind(this));
	}

	onShow() {
		//	申请最新个人信息，不用及时使用，使用缓存数据显示
		// ClubDataManager.getInstance().requestClubUserSelfInfo();
		this.updateClubUserSelfInfo();
	}

	updateClubUserSelfInfo() {
		let data = ClubDataManager.getInstance().getClubUserSelfInfo();
		if (data && data.identity == okpb.Identity.IDE_AGENT) {
			this.ui.labelCurChip.getComponent(cc.Label).string = "" + data.chipBalance;
		}
	}

	async onBtnOK() {
		let edit = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		let chips = Number(edit);
		let data: okpb.RspExchangeClubCounterChips = await ClubAPI.getInstance().ReqApplyClubChips(chips, okpb.ClubChipType.CCT_NORMAL);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode == okpb.ERET.OK) {
			OKGameManager.getInstance().showToast("俱乐部筹码申请成功");
			this.closeDialog();
		}
	}
}