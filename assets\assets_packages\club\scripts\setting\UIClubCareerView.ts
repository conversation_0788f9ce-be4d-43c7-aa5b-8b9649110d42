import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UScrollView from "../../../../framwork/widget/UScrollView/USFixedHeight";
import UIClubCareerCell from "./UIClubCareerCell";
import UIClubCareerView_auto from "./UIClubCareerView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubCareerView]";

/**
 * 俱乐部等级
 *
 * @export
 * @class UIClubCareerView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubCareerView")
export default class UIClubCareerView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubCareerView_auto = null;

    @property({ type: UScrollView, tooltip: "数据列表" })
    private scrollView: UScrollView = null;

    @property({ type: cc.Prefab, tooltip: "俱乐部生涯cell预制体" })
    private clubCareerCell: cc.Prefab = null;

    /**
     * 俱乐部等级列表数据
     *
     * @private
     * @memberof UIClubCareerView
     */
    private listData = [];

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubCareerView_auto(this.node);
        this.scrollView.onUpdateCell = this.onUpdateCell.bind(this);
        this.initEvent();
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubCareerView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIClubCareerView
     */
    private initEvent() {
        this.onRegisterEvent(this.ui.backButton, this.onClickBackBtn.bind(this));
    }

    /**
     * 更新单元格数据
     *
     * @private
     * @param {number} i
     * @param {cc.Node} node
     * @return {*}  {cc.Node}
     * @memberof UIClubCareerView
     */
    private onUpdateCell(i: number, node: cc.Node): cc.Node {
        if (this.listData.length === 0) {
            return null;
        }

        if (!node) {
            node = cc.instantiate(this.clubCareerCell);
        }
        const cell = node.getComponent(UIClubCareerCell);
        cell.setCellData();

        return node;
    }

    //#endregion

    //#region 事件处理
    /**
     * 点击返回按钮
     *
     * @private
     * @memberof UIClubCareerView
     */
    private onClickBackBtn() {
        this.closeDialog();
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
