export default class UIClubCashierChipRequestLayer_auto {
    node:cc.Node = null;   
	ClubCashierChipRequestLayer: cc.Node;
	DataLayer: cc.Node;
	toggleSelect: cc.Node;
	checkmark: cc.Node;
	cashierBalanceNode: cc.Node;
	clubChipIcon1: cc.Node;
	labelCashierBalanceTips: cc.Node;
	labelCashierBalance: cc.Node;
	labelNoLongerTips: cc.Node;
	ChipRequestScrollView: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;
	ButtomLayer: cc.Node;
	btnRejectAll: cc.Node;
	labeRejectAll: cc.Node;
	btnAllowAll: cc.Node;
	labeAllowAll: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierChipRequestLayer = this.node;
		this.DataLayer = this.ClubCashierChipRequestLayer.getChildByName("DataLayer");
		this.toggleSelect = this.DataLayer.getChildByName("toggleSelect");
		this.checkmark = this.toggleSelect.getChildByName("checkmark");
		this.cashierBalanceNode = this.DataLayer.getChildByName("cashierBalanceNode");
		this.clubChipIcon1 = this.cashierBalanceNode.getChildByName("clubChipIcon1");
		this.labelCashierBalanceTips = this.DataLayer.getChildByName("labelCashierBalanceTips");
		this.labelCashierBalance = this.DataLayer.getChildByName("labelCashierBalance");
		this.labelNoLongerTips = this.DataLayer.getChildByName("labelNoLongerTips");
		this.ChipRequestScrollView = this.ClubCashierChipRequestLayer.getChildByName("ChipRequestScrollView");
		this.view = this.ChipRequestScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.ChipRequestScrollView.getChildByName("listEmpty");
		this.ButtomLayer = this.ClubCashierChipRequestLayer.getChildByName("ButtomLayer");
		this.btnRejectAll = this.ButtomLayer.getChildByName("btnRejectAll");
		this.labeRejectAll = this.btnRejectAll.getChildByName("labeRejectAll");
		this.btnAllowAll = this.ButtomLayer.getChildByName("btnAllowAll");
		this.labeAllowAll = this.btnAllowAll.getChildByName("labeAllowAll");

    }
}
