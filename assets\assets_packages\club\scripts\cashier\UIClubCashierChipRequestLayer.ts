import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import { CLUB_EVENT } from "../ClubManger";
import UIClubCashierChipRequestLayer_auto from "./UIClubCashierChipRequestLayer_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierChipRequestLayer extends UBaseSFixedDialog {
	protected ui: UIClubCashierChipRequestLayer_auto = null;

	onUILoad(): void {
		super.onUILoad();
		this.ui = new UIClubCashierChipRequestLayer_auto(this.node);
		this.onRegisterEvent(this.ui.btnRejectAll, this.onBtnRejectAll.bind(this));
		this.onRegisterEvent(this.ui.btnAllowAll, this.onBtnAllowAll.bind(this));
		this.initListEmpty("room.noTables");
		//初始场景，当前层隐藏
		this.node.parent.active = false;

		//柜台筹码相关数据更新
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO, this.updateClubCounterInfo.bind(this));
		this.updateClubCounterInfo();
	}

	onEnable(): void {
		// this.initListEmpty("room.noTables");
		this.fetchRoomList();
	}

	updateClubCounterInfo() {
		let data = ClubDataManager.getInstance().getClubCounterInfo();
		if (data) {
			this.ui.labelCashierBalance.getComponent(cc.Label).string = "" + data.data.counterChips;
		}
	}

	updateLabelInfo() {
		this.ui.labelCashierBalance.getComponent(cc.Label).string = "";
	}

	updateOperaChip(userId: number) {
		let index = this.listData.findIndex((item: okpb.ClubChipsApplyData) => { return item.userId == userId });
		if (index >= 0) {
			this.listData.splice(index, 1);
		}
		this.updateListView();
	}

	private async fetchRoomList() {
		let data = await ClubAPI.getInstance().ReqGetClubChipsApplyList();
		if (data) this.updateListData(data, data.data);
	}

	//全部拒绝
	async onBtnRejectAll() {
		let applyIdList = [];
		for (let i = 0; i < this.listData.length; i++) {
			applyIdList.push(this.listData[i].userId);
		}
		let data = await ClubAPI.getInstance().ReqAuditClubChipsApply(false, applyIdList);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode === 0) {
			this.listData.length = 0;
			this.updateListView();
		}
	}

	//全部允许
	async onBtnAllowAll() {
		let applyIdList = [];
		for (let i = 0; i < this.listData.length; i++) {
			applyIdList.push(this.listData[i].userId);
		}
		let data = await ClubAPI.getInstance().ReqAuditClubChipsApply(true, applyIdList);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode === 0) {
			this.listData.length = 0;
			this.updateListView();
		}
	}
}