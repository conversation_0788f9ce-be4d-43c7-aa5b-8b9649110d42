import UBaseView from "../../../../framwork/widget/UBaseView";
import UIClubCareerCell_auto from "./UIClubCareerCell_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubCareerCell]";

/**
 * 俱乐部生涯cell
 *
 * @export
 * @class UIClubCareerCell
 * @extends {UBaseView}
 */
@ccclass
@menu("okgame/UIClubCareerCell")
export default class UIClubCareerCell extends UBaseView {
    //#region 属性
    protected ui: UIClubCareerCell_auto = null;

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubCareerCell_auto(this.node);
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubCareerCell
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }
    //#endregion

    //#region 事件处理
    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    /**
     * 设置cell数据
     *
     * @memberof UIClubCareerCell
     */
    setCellData() {}

    //#endregion
}
