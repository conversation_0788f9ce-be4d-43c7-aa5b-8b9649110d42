export default class UIClubInformView_auto {
    node:cc.Node = null;   
	ClubInformView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	titleContainer: cc.Node;
	title: cc.Node;
	closedBtn: cc.Node;
	closedBtnBg: cc.Node;
	topContainer: cc.Node;
	iconMask: cc.Node;
	appIcon: cc.Node;
	infoTitle: cc.Node;
	tipContainer: cc.Node;
	tipIcon: cc.Node;
	tip: cc.Node;
	titleEditContainer: cc.Node;
	title_0: cc.Node;
	titleEditBox: cc.Node;
	titleEditBoxBg: cc.Node;
	titleEditBoxLabel: cc.Node;
	titleEditBoxPlaceholder: cc.Node;
	contentEditContainer: cc.Node;
	title_1: cc.Node;
	contentEditBox: cc.Node;
	contentEditBoxBg: cc.Node;
	contentEditBoxLabel: cc.Node;
	contentEditBoxPlaceholder: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubInformView = this.node;
		this.mask = this.ClubInformView.getChildByName("mask");
		this.mainContainer = this.ClubInformView.getChildByName("mainContainer");
		this.titleContainer = this.mainContainer.getChildByName("titleContainer");
		this.title = this.titleContainer.getChildByName("title");
		this.closedBtn = this.titleContainer.getChildByName("closedBtn");
		this.closedBtnBg = this.closedBtn.getChildByName("closedBtnBg");
		this.topContainer = this.mainContainer.getChildByName("topContainer");
		this.iconMask = this.topContainer.getChildByName("iconMask");
		this.appIcon = this.iconMask.getChildByName("appIcon");
		this.infoTitle = this.topContainer.getChildByName("infoTitle");
		this.tipContainer = this.mainContainer.getChildByName("tipContainer");
		this.tipIcon = this.tipContainer.getChildByName("tipIcon");
		this.tip = this.tipContainer.getChildByName("tip");
		this.titleEditContainer = this.mainContainer.getChildByName("titleEditContainer");
		this.title_0 = this.titleEditContainer.getChildByName("title_0");
		this.titleEditBox = this.titleEditContainer.getChildByName("titleEditBox");
		this.titleEditBoxBg = this.titleEditBox.getChildByName("titleEditBoxBg");
		this.titleEditBoxLabel = this.titleEditBox.getChildByName("titleEditBoxLabel");
		this.titleEditBoxPlaceholder = this.titleEditBox.getChildByName("titleEditBoxPlaceholder");
		this.contentEditContainer = this.mainContainer.getChildByName("contentEditContainer");
		this.title_1 = this.contentEditContainer.getChildByName("title_1");
		this.contentEditBox = this.contentEditContainer.getChildByName("contentEditBox");
		this.contentEditBoxBg = this.contentEditBox.getChildByName("contentEditBoxBg");
		this.contentEditBoxLabel = this.contentEditBox.getChildByName("contentEditBoxLabel");
		this.contentEditBoxPlaceholder = this.contentEditBox.getChildByName("contentEditBoxPlaceholder");

    }
}
