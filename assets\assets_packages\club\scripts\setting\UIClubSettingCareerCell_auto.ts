export default class UIClubSettingCareerCell_auto {
    node: cc.Node = null;
    ClubSettingCareerCell: cc.Node;
    cellTitle: cc.Node;
    today: cc.Node;
    week: cc.Node;
    lastWeek: cc.Node;
    total: cc.Node;
    line: cc.Node;

    constructor(node: cc.Node) {
        this.node = node;
        this.ClubSettingCareerCell = this.node;
        this.cellTitle = this.ClubSettingCareerCell.getChildByName("cellTitle");
        this.today = this.ClubSettingCareerCell.getChildByName("today");
        this.week = this.ClubSettingCareerCell.getChildByName("week");
        this.lastWeek = this.ClubSettingCareerCell.getChildByName("lastWeek");
        this.total = this.ClubSettingCareerCell.getChildByName("total");
        this.line = this.ClubSettingCareerCell.getChildByName("line");
    }
}
