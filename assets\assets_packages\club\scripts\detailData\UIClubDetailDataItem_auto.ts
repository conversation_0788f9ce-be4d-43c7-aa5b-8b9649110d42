export default class UIClubDetailDataItem_auto {
    node:cc.Node = null;   
	ClubDetailDataItem: cc.Node;
	icon1: cc.Node;
	icon2: cc.Node;
	dataBG: cc.Node;
	labelNLH: cc.Node;
	labelTime: cc.Node;
	labelID: cc.Node;
	labelGold: cc.Node;
	labelChip: cc.Node;
	label: cc.Node;
	labelTips: cc.Node;
	labelserviceFee: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubDetailDataItem = this.node;
		this.icon1 = this.ClubDetailDataItem.getChildByName("icon1");
		this.icon2 = this.ClubDetailDataItem.getChildByName("icon2");
		this.dataBG = this.ClubDetailDataItem.getChildByName("dataBG");
		this.labelNLH = this.dataBG.getChildByName("labelNLH");
		this.labelTime = this.ClubDetailDataItem.getChildByName("labelTime");
		this.labelID = this.ClubDetailDataItem.getChildByName("labelID");
		this.labelGold = this.ClubDetailDataItem.getChildByName("labelGold");
		this.labelChip = this.ClubDetailDataItem.getChildByName("labelChip");
		this.label = this.ClubDetailDataItem.getChildByName("label");
		this.labelTips = this.ClubDetailDataItem.getChildByName("labelTips");
		this.labelserviceFee = this.ClubDetailDataItem.getChildByName("labelserviceFee");

    }
}
