import UBaseView from "../../../../framwork/widget/UBaseView";
import { OKGameMsgType } from "../../../okgame/network/OKGameMsgType";
import { WSMsgData } from "../../../okgame/network/OKGameWS";
import { okpb } from "../../../okgame/proto/proto_msg";
import ComGameAPI from "../../network/ComGameAPI";
import { COMGAME_UIID } from "../../pubilc/ComGameConst";
import ComGameManager from "../../pubilc/ComGameManager";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("comgame/UIGameMsgBtn")
export default class UIGameMsgBtn extends UBaseView {

    @property(cc.Node)
    newsTips: cc.Node = null;
    @property(cc.Node)
    redDot: cc.Node = null;

    onUILoad(): void {
        //----注册协议-----
        this.registerProto(OKGameMsgType.clubMsg, this.onClubMsg.bind(this))

        //-----
        this.newsTips.active = false
        this.redDot.active = false
        ComGameAPI.getInstance().ReqGetUserUnDealMsgCount().then(this.onMsgCount.bind(this))
    }

    private onClubMsg(data: WSMsgData) {
        //let obj = data.decode(wspb.ChatMsgPO)
        this.newsTips.active = true
    }


    private onMsgCount(data: okpb.RspGetUserUnDealMsgCount) {
        this.newsTips.active = data.data > 0
    }

    onGameMsgBtn() {
        console.log('onGameMsgBtn')
        ComGameManager.getInstance().showDialog(COMGAME_UIID.GameMsg)
    }
}
