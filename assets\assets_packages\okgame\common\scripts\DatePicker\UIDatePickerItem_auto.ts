export default class UIDatePickerItem_auto {
    node:cc.Node = null;   
	DatePickerItem: cc.Node;
	itemparent: cc.Node;
	day1: cc.Node;
	daySelect1: cc.Node;
	dayText1: cc.Node;
	day2: cc.Node;
	daySelect2: cc.Node;
	dayText2: cc.Node;
	day3: cc.Node;
	daySelect3: cc.Node;
	dayText3: cc.Node;
	day4: cc.Node;
	daySelect4: cc.Node;
	dayText4: cc.Node;
	day5: cc.Node;
	daySelect5: cc.Node;
	dayText5: cc.Node;
	day6: cc.Node;
	daySelect6: cc.Node;
	dayText6: cc.Node;
	day7: cc.Node;
	daySelect7: cc.Node;
	dayText7: cc.Node;
	date: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.DatePickerItem = this.node;
		this.itemparent = this.DatePickerItem.getChildByName("itemparent");
		this.day1 = this.itemparent.getChildByName("day1");
		this.daySelect1 = this.day1.getChildByName("daySelect1");
		this.dayText1 = this.day1.getChildByName("dayText1");
		this.day2 = this.itemparent.getChildByName("day2");
		this.daySelect2 = this.day2.getChildByName("daySelect2");
		this.dayText2 = this.day2.getChildByName("dayText2");
		this.day3 = this.itemparent.getChildByName("day3");
		this.daySelect3 = this.day3.getChildByName("daySelect3");
		this.dayText3 = this.day3.getChildByName("dayText3");
		this.day4 = this.itemparent.getChildByName("day4");
		this.daySelect4 = this.day4.getChildByName("daySelect4");
		this.dayText4 = this.day4.getChildByName("dayText4");
		this.day5 = this.itemparent.getChildByName("day5");
		this.daySelect5 = this.day5.getChildByName("daySelect5");
		this.dayText5 = this.day5.getChildByName("dayText5");
		this.day6 = this.itemparent.getChildByName("day6");
		this.daySelect6 = this.day6.getChildByName("daySelect6");
		this.dayText6 = this.day6.getChildByName("dayText6");
		this.day7 = this.itemparent.getChildByName("day7");
		this.daySelect7 = this.day7.getChildByName("daySelect7");
		this.dayText7 = this.day7.getChildByName("dayText7");
		this.date = this.DatePickerItem.getChildByName("date");

    }
}
