export default class UIClubNoticeView_auto {
    node:cc.Node = null;   
	ClubNoticeView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	titleContainer: cc.Node;
	title: cc.Node;
	closedBtn: cc.Node;
	closedBtnBg: cc.Node;
	contentEditBox: cc.Node;
	contentEditBoxBg: cc.Node;
	contentEditBoxLabel: cc.Node;
	contentEditBoxPlaceholder: cc.Node;
	pushEditContainer: cc.Node;
	title_0: cc.Node;
	pushEditBox: cc.Node;
	pushEditBoxBg: cc.Node;
	pushEditBoxLabel: cc.Node;
	pushEditBoxPlaceholder: cc.Node;
	rightIcon: cc.Node;
	tip: cc.Node;
	buttonContainer: cc.Node;
	cancelButton: cc.Node;
	cancelButtonBg: cc.Node;
	cancelButtonTitle: cc.Node;
	trueButton: cc.Node;
	trueButtonBg: cc.Node;
	trueButtonTitle: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubNoticeView = this.node;
		this.mask = this.ClubNoticeView.getChildByName("mask");
		this.mainContainer = this.ClubNoticeView.getChildByName("mainContainer");
		this.titleContainer = this.mainContainer.getChildByName("titleContainer");
		this.title = this.titleContainer.getChildByName("title");
		this.closedBtn = this.titleContainer.getChildByName("closedBtn");
		this.closedBtnBg = this.closedBtn.getChildByName("closedBtnBg");
		this.contentEditBox = this.mainContainer.getChildByName("contentEditBox");
		this.contentEditBoxBg = this.contentEditBox.getChildByName("contentEditBoxBg");
		this.contentEditBoxLabel = this.contentEditBox.getChildByName("contentEditBoxLabel");
		this.contentEditBoxPlaceholder = this.contentEditBox.getChildByName("contentEditBoxPlaceholder");
		this.pushEditContainer = this.mainContainer.getChildByName("pushEditContainer");
		this.title_0 = this.pushEditContainer.getChildByName("title_0");
		this.pushEditBox = this.pushEditContainer.getChildByName("pushEditBox");
		this.pushEditBoxBg = this.pushEditBox.getChildByName("pushEditBoxBg");
		this.pushEditBoxLabel = this.pushEditBox.getChildByName("pushEditBoxLabel");
		this.pushEditBoxPlaceholder = this.pushEditBox.getChildByName("pushEditBoxPlaceholder");
		this.rightIcon = this.pushEditBox.getChildByName("rightIcon");
		this.tip = this.mainContainer.getChildByName("tip");
		this.buttonContainer = this.mainContainer.getChildByName("buttonContainer");
		this.cancelButton = this.buttonContainer.getChildByName("cancelButton");
		this.cancelButtonBg = this.cancelButton.getChildByName("cancelButtonBg");
		this.cancelButtonTitle = this.cancelButtonBg.getChildByName("cancelButtonTitle");
		this.trueButton = this.buttonContainer.getChildByName("trueButton");
		this.trueButtonBg = this.trueButton.getChildByName("trueButtonBg");
		this.trueButtonTitle = this.trueButtonBg.getChildByName("trueButtonTitle");

    }
}
