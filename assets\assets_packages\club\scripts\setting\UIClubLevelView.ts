import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UScrollView from "../../../../framwork/widget/UScrollView/USFixedHeight";
import UIClubLevelCell from "./UIClubLevelCell";
import UIClubLevelView_auto from "./UIClubLevelView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubLevelView]";

/**
 * 俱乐部等级
 *
 * @export
 * @class UIClubLevelView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubLevelView")
export default class UIClubLevelView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubLevelView_auto = null;

    @property({ type: UScrollView, tooltip: "数据列表" })
    private scrollView: UScrollView = null;

    @property({ type: cc.Prefab, tooltip: "俱乐部等级cell预制体" })
    private clubLevelCell: cc.Prefab = null;

    /**
     * 俱乐部等级列表数据
     *
     * @private
     * @memberof UIClubLevelView
     */
    private listData = [];

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubLevelView_auto(this.node);
        this.scrollView.onUpdateCell = this.onUpdateCell.bind(this);
        this.initEvent();
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubLevelView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIClubLevelView
     */
    private initEvent() {
        this.onRegisterEvent(this.ui.closedBtn, this.onClickBackBtn.bind(this));
    }

    /**
     * 更新单元格数据
     *
     * @private
     * @param {number} i
     * @param {cc.Node} node
     * @return {*}  {cc.Node}
     * @memberof UIClubLevelView
     */
    private onUpdateCell(i: number, node: cc.Node): cc.Node {
        if (this.listData.length === 0) {
            return null;
        }

        if (!node) {
            node = cc.instantiate(this.clubLevelCell);
        }
        const cell = node.getComponent(UIClubLevelCell);
        cell.setCellData();

        return node;
    }

    //#endregion

    //#region 事件处理
    /**
     * 点击返回按钮
     *
     * @private
     * @memberof UIClubLevelView
     */
    private onClickBackBtn() {
        this.closeDialog();
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
