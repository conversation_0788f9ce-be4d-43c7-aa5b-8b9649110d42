import UBaseView from "../../../../../framwork/widget/UBaseView";
import ClubManger from "../../ClubManger";
import { IPermissionConfig } from "../MemberTypes";

const { ccclass, property } = cc._decorator;
const TAG = "[ItemFeatureView]";
/**
 * 成员详情-->功能权限ItemView
 */
@ccclass
export default class ItemFeatureView extends UBaseView {
    @property(cc.Node)
    rootNode: cc.Node = null;

    @property(cc.Label)
    nameTxt: cc.Label = null;

    @property(cc.Label)
    subValueTxt: cc.Label = null;

    @property(cc.Sprite)
    subIconImg: cc.Sprite = null;

    onUILoad(): void {
        this.onRegisterEvent(this.rootNode, this.clickFeature.bind(this));
    }

    private data: IPermissionConfig = null;
    private clickCallback: Function = null;

    public setData(_data: IPermissionConfig, clickCallback: Function) {
        this.clickCallback = clickCallback;
        this.data = _data;
        this.nameTxt.string = _data.featureName;
        this.subValueTxt.string = _data.value;
        this.subIconImg.spriteFrame = _data.icon;
    }

    private clickFeature(event: cc.Event.EventTouch) {
        console.log(TAG, "onTouchStart() 点击了", this.nameTxt.string);
        this.clickCallback && this.clickCallback();
    }
}
