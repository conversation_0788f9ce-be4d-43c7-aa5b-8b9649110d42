export default class UIClubDetailDataView_auto {
    node:cc.Node = null;   
	ClubDetailDataView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	DIBG: cc.Node;
	TitleBG: cc.Node;
	labelTitle: cc.Node;
	btnBack: cc.Node;
	SelectTimeLayer: cc.Node;
	btnYesterDay: cc.Node;
	labelYesterDay: cc.Node;
	BackYesterDay: cc.Node;
	labelYesterDay2: cc.Node;
	btnLastWeek: cc.Node;
	labelLastWeek: cc.Node;
	BackLastWeek: cc.Node;
	labelLastWeek2: cc.Node;
	btnChoiceDate: cc.Node;
	labelChoiceDate: cc.Node;
	triangle: cc.Node;
	BackChoiceDate: cc.Node;
	labelChoiceDate2: cc.Node;
	triangle2: cc.Node;
	TimeLayer: cc.Node;
	labelTimeStart: cc.Node;
	labelTimeEnd: cc.Node;
	labelSeparator: cc.Node;
	btnArrowL: cc.Node;
	btnArrowR: cc.Node;
	TotalDataLayer: cc.Node;
	line1: cc.Node;
	line2: cc.Node;
	line3: cc.Node;
	labelTips1: cc.Node;
	labelTips2: cc.Node;
	labelTips3: cc.Node;
	labelTips4: cc.Node;
	labelTotalRound: cc.Node;
	labelWinLose: cc.Node;
	labelserviceFee: cc.Node;
	labelInsurance: cc.Node;
	SearchLayer: cc.Node;
	SelectFilterLayer: cc.Node;
	FilterScrollView: cc.Node;
	filterView: cc.Node;
	filterContent: cc.Node;
	btnAll: cc.Node;
	labelAll: cc.Node;
	BackAll: cc.Node;
	labelAll2: cc.Node;
	btnNLH: cc.Node;
	labelNLH: cc.Node;
	BackNLH: cc.Node;
	labelNLH2: cc.Node;
	btnPLO: cc.Node;
	labelPLO: cc.Node;
	BackPLO: cc.Node;
	labelPLO2: cc.Node;
	btnFilter: cc.Node;
	sprFilter: cc.Node;
	DetailDataScrollView: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubDetailDataView = this.node;
		this.Mask = this.ClubDetailDataView.getChildByName("Mask");
		this.View = this.ClubDetailDataView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.DIBG = this.BG.getChildByName("DIBG");
		this.TitleBG = this.View.getChildByName("TitleBG");
		this.labelTitle = this.TitleBG.getChildByName("labelTitle");
		this.btnBack = this.TitleBG.getChildByName("btnBack");
		this.SelectTimeLayer = this.View.getChildByName("SelectTimeLayer");
		this.btnYesterDay = this.SelectTimeLayer.getChildByName("btnYesterDay");
		this.labelYesterDay = this.btnYesterDay.getChildByName("labelYesterDay");
		this.BackYesterDay = this.btnYesterDay.getChildByName("BackYesterDay");
		this.labelYesterDay2 = this.BackYesterDay.getChildByName("labelYesterDay2");
		this.btnLastWeek = this.SelectTimeLayer.getChildByName("btnLastWeek");
		this.labelLastWeek = this.btnLastWeek.getChildByName("labelLastWeek");
		this.BackLastWeek = this.btnLastWeek.getChildByName("BackLastWeek");
		this.labelLastWeek2 = this.BackLastWeek.getChildByName("labelLastWeek2");
		this.btnChoiceDate = this.SelectTimeLayer.getChildByName("btnChoiceDate");
		this.labelChoiceDate = this.btnChoiceDate.getChildByName("labelChoiceDate");
		this.triangle = this.btnChoiceDate.getChildByName("triangle");
		this.BackChoiceDate = this.btnChoiceDate.getChildByName("BackChoiceDate");
		this.labelChoiceDate2 = this.BackChoiceDate.getChildByName("labelChoiceDate2");
		this.triangle2 = this.BackChoiceDate.getChildByName("triangle2");
		this.TimeLayer = this.View.getChildByName("TimeLayer");
		this.labelTimeStart = this.TimeLayer.getChildByName("labelTimeStart");
		this.labelTimeEnd = this.TimeLayer.getChildByName("labelTimeEnd");
		this.labelSeparator = this.TimeLayer.getChildByName("labelSeparator");
		this.btnArrowL = this.TimeLayer.getChildByName("btnArrowL");
		this.btnArrowR = this.TimeLayer.getChildByName("btnArrowR");
		this.TotalDataLayer = this.View.getChildByName("TotalDataLayer");
		this.line1 = this.TotalDataLayer.getChildByName("line1");
		this.line2 = this.TotalDataLayer.getChildByName("line2");
		this.line3 = this.TotalDataLayer.getChildByName("line3");
		this.labelTips1 = this.TotalDataLayer.getChildByName("labelTips1");
		this.labelTips2 = this.TotalDataLayer.getChildByName("labelTips2");
		this.labelTips3 = this.TotalDataLayer.getChildByName("labelTips3");
		this.labelTips4 = this.TotalDataLayer.getChildByName("labelTips4");
		this.labelTotalRound = this.TotalDataLayer.getChildByName("labelTotalRound");
		this.labelWinLose = this.TotalDataLayer.getChildByName("labelWinLose");
		this.labelserviceFee = this.TotalDataLayer.getChildByName("labelserviceFee");
		this.labelInsurance = this.TotalDataLayer.getChildByName("labelInsurance");
		this.SearchLayer = this.View.getChildByName("SearchLayer");
		this.SelectFilterLayer = this.View.getChildByName("SelectFilterLayer");
		this.FilterScrollView = this.SelectFilterLayer.getChildByName("FilterScrollView");
		this.filterView = this.FilterScrollView.getChildByName("filterView");
		this.filterContent = this.filterView.getChildByName("filterContent");
		this.btnAll = this.filterContent.getChildByName("btnAll");
		this.labelAll = this.btnAll.getChildByName("labelAll");
		this.BackAll = this.btnAll.getChildByName("BackAll");
		this.labelAll2 = this.BackAll.getChildByName("labelAll2");
		this.btnNLH = this.filterContent.getChildByName("btnNLH");
		this.labelNLH = this.btnNLH.getChildByName("labelNLH");
		this.BackNLH = this.btnNLH.getChildByName("BackNLH");
		this.labelNLH2 = this.BackNLH.getChildByName("labelNLH2");
		this.btnPLO = this.filterContent.getChildByName("btnPLO");
		this.labelPLO = this.btnPLO.getChildByName("labelPLO");
		this.BackPLO = this.btnPLO.getChildByName("BackPLO");
		this.labelPLO2 = this.BackPLO.getChildByName("labelPLO2");
		this.btnFilter = this.SelectFilterLayer.getChildByName("btnFilter");
		this.sprFilter = this.btnFilter.getChildByName("sprFilter");
		this.DetailDataScrollView = this.View.getChildByName("DetailDataScrollView");
		this.view = this.DetailDataScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.DetailDataScrollView.getChildByName("listEmpty");

    }
}
