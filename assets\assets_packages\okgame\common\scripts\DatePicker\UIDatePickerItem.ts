import UBaseView from "../../../../../framwork/widget/UBaseView";
import UIDatePickerItem_auto from "./UIDatePickerItem_auto";
const { ccclass, property } = cc._decorator;
const SELECT_COLOR = cc.color().fromHEX("#739EFF");
const TODAY_COLOR = cc.color().fromHEX("#FFC03A"); // 当日黄色字体
const NORMAL_COLOR = cc.color().fromHEX("#FFFFFF");
const DISABLED_COLOR = cc.color().fromHEX("#666666"); // 不可选择的日期颜色
@ccclass
export default class UIDatePickerItem extends UBaseView {
    protected ui: UIDatePickerItem_auto = null;
    private clickNodes: cc.Node[] = [];
    private curYear: number = 0;
    private curMonth: number = 0;
    private checkTimer: Function = null;
    private getIsCurDay: Function = null;
    private getcheckTimer: Function = null;
    onUILoad(): void {
        this.ui = new UIDatePickerItem_auto(this.node);
        this.clickNodes = [this.ui.day1, this.ui.day2, this.ui.day3, this.ui.day4, this.ui.day5, this.ui.day6, this.ui.day7];
        for (let i = 0; i < this.clickNodes.length; i++) {
            this.clickNodes[i].on(cc.Node.EventType.TOUCH_END, this.itemClick.bind(this), this);
        }
    }

    private itemClick(event) {
        // 禁止点击不可选择的日期（灰色日期）
        if (DISABLED_COLOR.equals(event.target.children[1].color)) return;
        if (SELECT_COLOR.equals(event.target.children[1].color)) return;
        event.target.children[0].active = true;
        let str = parseInt(event.target.children[1].getComponent(cc.Label).string);
        let timer = new Date(this.curYear, this.curMonth - 1, str).getTime();
        this.checkTimer && this.checkTimer(timer);
    }

    public setData(info: any, checkTimer: Function, getIsCurDay: Function, getcheckTimer: Function): void {
        this.checkTimer = checkTimer;
        this.getIsCurDay = getIsCurDay;
        this.getcheckTimer = getcheckTimer;
        this.curMonth = info.month;
        this.curYear = info.year;
        let data = info.dateAry;
        if (data) {
            this.node.children[0].active = true;
            this.node.children[1].active = false;
            for (let i = 0; i < data.length; i++) {
                if (data[i]) {
                    this.node.children[0].children[i].active = true;
                    this.node.children[0].children[i].children[1].getComponent(cc.Label).string = data[i];
                    let dataInfo = this.getIsCurDay();
                    let timer = new Date(this.curYear, this.curMonth - 1, data[i]).getTime();
                    let today = new Date();
                    today.setHours(23, 59, 59, 999);
                    let todayTime = today.getTime();
                    
                    if (timer >= dataInfo.startTime && timer <= dataInfo.endTime) {
                        // 在可选择范围内
                        if (this.istoday(timer)) {
                            // 当日显示黄色字体
                            this.node.children[0].children[i].children[1].color = TODAY_COLOR;
                        } else if (timer <= todayTime) {
                            // 已过日期可选，显示白色
                            this.node.children[0].children[i].children[1].color = NORMAL_COLOR;
                        } else {
                            // 未过日期不可选，显示灰色
                            this.node.children[0].children[i].children[1].color = DISABLED_COLOR;
                        }
                    } else {
                        this.node.children[0].children[i].children[1].color = SELECT_COLOR;
                        this.node.children[0].children[i].children[0].active = false;
                    }

                    if (timer >= this.getcheckTimer().startTime && timer <= this.getcheckTimer().endTime) {
                        this.node.children[0].children[i].children[0].active = true;
                    } else {
                        this.node.children[0].children[i].children[0].active = false;
                    }
                } else {
                    this.node.children[0].children[i].active = false;
                }
            }
        } else {
            this.node.children[0].active = false;
            this.node.children[1].active = true;
            this.node.children[1].getComponent(cc.Label).string = this.curMonth + "/" + this.curYear;
        }
    }

    public updatebg(startItme: number, endTime: number) {
        for (let i = 0; i < this.clickNodes.length; i++) {
            let node = this.clickNodes[i];
            let str = parseInt(node.children[1].getComponent(cc.Label).string);
            let timer = new Date(this.curYear, this.curMonth - 1, str).getTime();
            if (!endTime) {
                if (timer == startItme) {
                    node.children[0].active = true;
                } else {
                    node.children[0].active = false;
                }
            } else {
                if (timer >= startItme && timer <= endTime) {
                    node.children[0].active = true;
                } else {
                    node.children[0].active = false;
                }
            }
        }
    }

    private istoday(timer: number) {
        let date = new Date();
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let curTime = new Date(year, month - 1, day).getTime();
        if (timer == curTime) return true;
        return false;
    }
}
