import UBaseView from "../../../../../framwork/widget/UBaseView";
import UIconSprite from "../../../../../framwork/widget/UIconSprite";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import OKGameUtils from "../../../../okgame/public/OKGameUtils";
import ClubAPI from "../../ClubAPI";
import ClubMessageManager from "./ClubMessageManager";

const { ccclass, property } = cc._decorator;

const TAG = "[ItemApplyView]";

/**
 * 成员管理-->申请ItemView
 */
@ccclass
export default class ItemApplyView extends UBaseView {
    @property({ type: cc.Sprite, tooltip: "头像" })
    private avatorImg: cc.Sprite = null;

    @property({ type: cc.Label, tooltip: "账号名" })
    private accountNameTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "用户ID" })
    private userID: cc.Label = null;

    @property({ type: cc.Sprite, tooltip: "角色类型" })
    private roleTypeImg: cc.Sprite = null;

    @property({ type: cc.Node, tooltip: "清除按钮" })
    private btnClear: cc.Node = null;

    @property({ type: cc.Node, tooltip: "同意按钮" })
    private btnAgree: cc.Node = null;

    @property({ type: cc.Label, tooltip: "申请消息" })
    private applyMsgTxt: cc.Label = null;

    /**
     * 同意或者拒绝之后的回调，解决：所有消息都处理完后，需要通知上层进行数据刷新
     */
    private doneCallback: Function = null;

    private mailId: number = 0;

    onUILoad(): void {
        this.initView();
        this.initEvent();
    }

    private initView(): void {
        this.roleTypeImg.node.active = false;
    }

    private initEvent(): void {
        this.onRegisterEvent(this.btnClear, this.onBtnClearClick.bind(this));
        this.onRegisterEvent(this.btnAgree, this.onBtnAgreeClick.bind(this));
    }

    /**
     * 设置申请信息
     * @param msgInfo 申请信息
     * @param _index 索引位置
     * @param doneCallback 处理完成后的回调
     */
    public setMsgInfo(msgInfo: okpb.JoinClubReqInfo, _index: number, doneCallback: Function): void {
        this.mailId = msgInfo.mailId;
        this.doneCallback = doneCallback;

        this.accountNameTxt.string = OKGameUtils.getStringMaxBitWithCase(msgInfo.nickname, 20);

        if (msgInfo?.extParam) {
            const extParam = JSON.parse(msgInfo?.extParam);
            this.applyMsgTxt.string = OKGameUtils.getStringMaxBitWithCase(extParam?.applyMsg ?? "", 35);
            this.userID.string = "ID:" + (extParam?.userId ?? "");
        } else {
            this.applyMsgTxt.string = "";
            this.userID.string = "";
        }

        // 头像
        const iconScript = this.avatorImg.getComponent(UIconSprite);
        if (iconScript) {
            iconScript.setIcon(msgInfo.avatar);
        }
    }

    /**
     * 处理申请请求
     * @param status 处理状态 1:同意, 0:拒绝
     */
    private async handleApplyRequest(status: number): Promise<void> {
        OKGameManager.getInstance().showLoading();
        console.log(TAG, "handleApplyRequest", this.mailId, status);
        await ClubAPI.getInstance()
            .ReqHandleClubJoinRequest(this.mailId, status)
            .then((res: okpb.RspJoinClubAudit) => {
                if (cc.isValid(this.node)) {
                    console.log(`处理申请请求成功: ${status === 1 ? "同意" : "拒绝"}`, res);
                    if (res.errorCode === 0) {
                        this.node.active = false;
                        this.doneCallback && this.doneCallback();
                        ClubMessageManager.getInstance().deleteMessage(this.mailId);
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    console.error(`处理申请请求失败: ${status === 1 ? "同意" : "拒绝"}`, err);
                    OKGameManager.getInstance().showToast(err.message);
                }
            })
            .finally(() => {
                if (cc.isValid(this.node)) {
                    OKGameManager.getInstance().hideLoading();
                }
            });
    }

    private onBtnClearClick(): void {
        this.handleApplyRequest(0);
    }

    private onBtnAgreeClick(): void {
        this.handleApplyRequest(1);
    }
}
