import UBaseView from "../../../../framwork/widget/UBaseView";
import UTextureArray from "../../../../framwork/widget/UTextureArray";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameUtils from "../../../okgame/public/OKGameUtils";
import UIClubCashierTransRecrodLayer from "./UIClubCashierTransRecrodLayer";
import UIClubTransRecrodItem_auto from "./UIClubTransRecrodItem_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubTransRecrodItem extends UBaseView {

	protected ui: UIClubTransRecrodItem_auto = null;
	private _curData: okpb.ClubChipTransactionData = null;
	private father: UIClubCashierTransRecrodLayer = null;

	onUILoad(): void {
		this.ui = new UIClubTransRecrodItem_auto(this.node);
	}

	onEnable(): void {
		this.updateUI();
	}

	setData(father: UIClubCashierTransRecrodLayer, data: okpb.ClubChipTransactionData) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;
		this.father = father;

		this.updateUI();
	}

	updateUI() {
		if (!cc.isValid(this.ui) || !this._curData) return;
		// this.ui.arrow.getComponent(UTextureArray).setTexture(this._curData.chipType);
		this.ui.clubChipIcon.getComponent(UTextureArray).setTexture(this._curData.chipType);
		this.ui.freeTxt.getComponent(cc.Label).string = "" + this._curData.chips;
		this.ui.userID1.getComponent(cc.Label).string = "" + this._curData.operatorId;
		this.ui.userID2.getComponent(cc.Label).string = "" + this._curData.targetId;
		this.ui.labelNoteName1.getComponent(cc.Label).string = "";
		this.ui.labelNoteName2.getComponent(cc.Label).string = "";
		this.ui.labelTime.getComponent(cc.Label).string = "" + OKGameUtils.dateStrFromTimestamp(this._curData.createTime);
	}
}