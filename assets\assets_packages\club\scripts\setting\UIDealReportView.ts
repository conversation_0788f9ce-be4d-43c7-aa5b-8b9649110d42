import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UScrollView from "../../../../framwork/widget/UScrollView/USFixedHeight";
import UIDealReportCell from "./UIDealReportCell";
import UIDealReportView_auto from "./UIDealReportView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIDealReportView]";

/**
 * 俱乐部交易报告
 *
 * @export
 * @class UIDealReportView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIDealReportView")
export default class UIDealReportView extends UBaseDialog {
    //#region 属性
    protected ui: UIDealReportView_auto = null;

    @property({ type: UScrollView, tooltip: "数据列表" })
    private scrollView: UScrollView = null;

    @property({ type: cc.Prefab, tooltip: "交易报告cell预制体" })
    private dealReportCell: cc.Prefab = null;

    /**
     * 交易报告列表数据
     *
     * @private
     * @memberof UIDealReportView
     */
    private listData = [];

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIDealReportView_auto(this.node);
        this.scrollView.onUpdateCell = this.onUpdateCell.bind(this);
        this.initEvent();
        this.loadData();
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIDealReportView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIDealReportView
     */
    private initEvent() {
        this.onRegisterEvent(this.ui.closedBtn, this.onClickBackBtn.bind(this));
    }

    /**
     * 更新单元格数据
     *
     * @private
     * @param {number} i
     * @param {cc.Node} node
     * @return {*}  {cc.Node}
     * @memberof UIDealReportView
     */
    private onUpdateCell(i: number, node: cc.Node): cc.Node {
        if (this.listData.length === 0) {
            return null;
        }

        if (!node) {
            node = cc.instantiate(this.dealReportCell);
        }
        const cell = node.getComponent(UIDealReportCell);
        const data = this.listData[i];
        cell.setCellData(data);

        return node;
    }

    /**
     * 加载数据
     *
     * @private
     * @memberof UIDealReportView
     */
    private loadData() {
        // TODO: 从服务器加载交易报告数据
        this.log("加载交易报告数据");

        // 模拟数据
        this.listData = [
            {
                title: "交易记录1",
                tip: "成功",
                shipValue: 1000,
                time: "2024-01-01 12:00:00",
            },
            {
                title: "交易记录2",
                tip: "失败",
                shipValue: 500,
                time: "2024-01-01 13:00:00",
            },
            {
                title: "交易记录3",
                tip: "成功",
                shipValue: 2000,
                time: "2024-01-01 14:00:00",
            },
        ];

        this.updateScrollView();
    }

    /**
     * 更新滚动视图
     *
     * @private
     * @memberof UIDealReportView
     */
    private updateScrollView() {
        this.scrollView.loadData(this.listData.length);
        this.log(`更新滚动视图，数据条数: ${this.listData.length}`);
    }

    //#endregion

    //#region 事件处理
    /**
     * 点击返回按钮
     *
     * @private
     * @memberof UIDealReportView
     */
    private onClickBackBtn() {
        this.log("点击返回按钮");
        this.closeDialog();
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法

    //#endregion
}
