export default class UIClubCareerCell_auto {
    node:cc.Node = null;   
	ClubCareerCell: cc.Node;
	dateLabel: cc.Node;
	detailContainer: cc.Node;
	gameNameNode: cc.Node;
	gameName: cc.Node;
	roomName: cc.Node;
	gameTime: cc.Node;
	blindBet: cc.Node;
	blindIcon: cc.Node;
	blindValue: cc.Node;
	shipNode: cc.Node;
	shipIcon: cc.Node;
	shipValue: cc.Node;
	arrowIcon: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCareerCell = this.node;
		this.dateLabel = this.ClubCareerCell.getChildByName("dateLabel");
		this.detailContainer = this.ClubCareerCell.getChildByName("detailContainer");
		this.gameNameNode = this.detailContainer.getChildByName("gameNameNode");
		this.gameName = this.gameNameNode.getChildByName("gameName");
		this.roomName = this.detailContainer.getChildByName("roomName");
		this.gameTime = this.detailContainer.getChildByName("gameTime");
		this.blindBet = this.detailContainer.getChildByName("blindBet");
		this.blindIcon = this.blindBet.getChildByName("blindIcon");
		this.blindValue = this.blindBet.getChildByName("blindValue");
		this.shipNode = this.detailContainer.getChildByName("shipNode");
		this.shipIcon = this.shipNode.getChildByName("shipIcon");
		this.shipValue = this.shipNode.getChildByName("shipValue");
		this.arrowIcon = this.detailContainer.getChildByName("arrowIcon");

    }
}
