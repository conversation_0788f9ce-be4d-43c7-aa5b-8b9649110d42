import OKBaseAP<PERSON> from "../../network/OKBaseAPI";
import { OKGAME_ROUTE } from "../../network/OKGameMsgRoute";
import { okpb } from "../../proto/proto_msg";
export default class OKClubAPI extends OKBaseAPI {
    protected static _instance = null;
    public static getInstance(): OKClubAPI {
        if (this._instance == null) {
            this._instance = new OKClubAPI();
        }
        return this._instance;
    }

    constructor() {
        super();
    }

    /**
     * 创建俱乐部请求
     * @param name - 俱乐部名称
     * @param iconUrl - 俱乐部图标URL地址
     * @param intro - 俱乐部简介
     */
    public async ReqCreateClub(name: string, iconUrl: string, intro: string): Promise<okpb.RspJoinClub> {
        let req = {} as okpb.ReqCreateClub;
        req.userId = this.getUserId();
        req.name = name;
        req.iconUrl = iconUrl;
        req.intro = intro;
        return this.requestPB(OKGAME_ROUTE.ReqCreateClub, okpb.ReqCreateClub.encode(req), okpb.RspJoinClub);
    }

    /**
     * 加入俱乐部请求
     * @param clubId - 要加入的目标俱乐部ID
     */
    public async ReqJoinClub(clubId: number, recommenderUid: number, recommendMsg: string): Promise<okpb.RspJoinClub> {
        let req = {} as okpb.ReqJoinClub;
        req.userId = this.getUserId();
        req.clubId = clubId;
        req.recommenderUid = recommenderUid;
        req.applyMsg = recommendMsg;
        console.log("ReqJoinClub", "加入俱乐部传参:", JSON.stringify(req));
        return this.requestPB(OKGAME_ROUTE.ReqJoinClub, okpb.ReqJoinClub.encode(req), okpb.RspJoinClub);
    }

    /**
     * 搜索俱乐部请求
     * @param clubId - 要搜索的目标俱乐部ID
     * @param referrerId - 推荐人ID
     */
    public async ReqSearchClub(clubId: number, referrerId: number): Promise<okpb.RspSearchClub> {
        let req = {} as okpb.ReqSearchClub;
        req.userId = this.getUserId();
        req.clubId = clubId;
        req.recommenderUid = referrerId;
        return this.requestPB(OKGAME_ROUTE.ReqSearchClub, okpb.ReqSearchClub.encode(req), okpb.RspSearchClub);
    }

    /**
     * 获取俱乐部列表请求
     * @param bShowApplying - 是否显示申请中的俱乐部（1-显示，0-不显示）
     */
    public async ReqGetClubList(bShowApplying: number = 0): Promise<okpb.RspGetClubList> {
        let req = {} as okpb.ReqGetClubList;
        req.userId = this.getUserId();
        req.bShowApplying = bShowApplying;
        return this.requestPB(OKGAME_ROUTE.ReqGetClubList, okpb.ReqGetClubList.encode(req), okpb.RspGetClubList);
    }

    /**
     * 俱乐部内页请求
     * @param clubId 俱乐部ID
     * @returns
     */
    public async ReqGetClubView(clubId: number): Promise<okpb.RspClubView> {
        let req = {} as okpb.ReqClubView;
        req.clubId = clubId;
        let data = await this.request("/clubView.ajax", req);
        return data;
    }

    /**
     * 编辑俱乐部信息请求
     * @param clubId 俱乐部ID
     * @param iconUrl 俱乐部icon
     * @param name 俱乐部名称
     * @param intro 俱乐部简介
     * @param prologue 俱乐部开场白
     * @param prologueStatus 俱乐部开场白开关
     */
    public async ReqClubEdit(clubId: number, iconUrl: string, name: string, intro: string): Promise<okpb.RspJoinClub> {
        let req = {} as okpb.ReqClubEdit;
        req.userId = this.getUserId();
        req.clubId = clubId;
        req.iconUrl = iconUrl;
        req.name = name;
        req.intro = intro;
        req.prologue = "";
        req.prologueStatus = 0;
        return this.requestPB(OKGAME_ROUTE.ReqClubEdit, okpb.ReqClubEdit.encode(req), okpb.RspJoinClub);
    }
}
