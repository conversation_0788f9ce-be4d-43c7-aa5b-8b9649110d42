export default class UIClubCreditRequestView_auto {
    node:cc.Node = null;   
	ClubCreditRequestView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	btnOK: cc.Node;
	labeOK: cc.Node;
	ChipNode: cc.Node;
	ChipBg: cc.Node;
	ChipIcon: cc.Node;
	labelCurChip: cc.Node;
	EditBoxNum: cc.Node;
	BACKGROUND_SPRITE: cc.Node;
	TEXT_LABEL: cc.Node;
	PLACEHOLDER_LABEL: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCreditRequestView = this.node;
		this.Mask = this.ClubCreditRequestView.getChildByName("Mask");
		this.View = this.ClubCreditRequestView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.btnOK = this.View.getChildByName("btnOK");
		this.labeOK = this.btnOK.getChildByName("labeOK");
		this.ChipNode = this.View.getChildByName("ChipNode");
		this.ChipBg = this.ChipNode.getChildByName("ChipBg");
		this.ChipIcon = this.ChipNode.getChildByName("ChipIcon");
		this.labelCurChip = this.ChipNode.getChildByName("labelCurChip");
		this.EditBoxNum = this.View.getChildByName("EditBoxNum");
		this.BACKGROUND_SPRITE = this.EditBoxNum.getChildByName("BACKGROUND_SPRITE");
		this.TEXT_LABEL = this.EditBoxNum.getChildByName("TEXT_LABEL");
		this.PLACEHOLDER_LABEL = this.EditBoxNum.getChildByName("PLACEHOLDER_LABEL");

    }
}
