export default class UIClubCashierAgentCreditView_auto {
    node:cc.Node = null;   
	ClubCashierAgentCreditView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	ScrollView: cc.Node;
	sortLayer: cc.Node;
	labelAgent: cc.Node;
	selectSortBox: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;
	labelTotalCredit: cc.Node;
	labelTotalChip: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierAgentCreditView = this.node;
		this.Mask = this.ClubCashierAgentCreditView.getChildByName("Mask");
		this.View = this.ClubCashierAgentCreditView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.ScrollView = this.View.getChildByName("ScrollView");
		this.sortLayer = this.ScrollView.getChildByName("sortLayer");
		this.labelAgent = this.sortLayer.getChildByName("labelAgent");
		this.selectSortBox = this.sortLayer.getChildByName("selectSortBox");
		this.view = this.ScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.ScrollView.getChildByName("listEmpty");
		this.labelTotalCredit = this.View.getChildByName("labelTotalCredit");
		this.labelTotalChip = this.View.getChildByName("labelTotalChip");

    }
}
