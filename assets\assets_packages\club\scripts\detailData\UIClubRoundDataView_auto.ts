export default class UIClubRoundDataView_auto {
    node:cc.Node = null;   
	ClubRoundDataView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	DIBG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	clubChipIcon: cc.Node;
	icon1: cc.Node;
	icon2: cc.Node;
	commBg1: cc.Node;
	commBg2: cc.Node;
	commBg3: cc.Node;
	dataBG: cc.Node;
	labelNLH: cc.Node;
	labelGold: cc.Node;
	labelTotalTime: cc.Node;
	label: cc.Node;
	labelTime: cc.Node;
	labelID: cc.Node;
	labelChip: cc.Node;
	labelBuyTips: cc.Node;
	labelFeeTips: cc.Node;
	labelInsTips: cc.Node;
	labelBuy: cc.Node;
	labelServiceFee: cc.Node;
	labelInsurance: cc.Node;
	ScrollView: cc.Node;
	sortLayer: cc.Node;
	labelUser: cc.Node;
	cardSortBoxItem: cc.Node;
	winLoseSortBoxItem: cc.Node;
	view: cc.Node;
	content: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubRoundDataView = this.node;
		this.Mask = this.ClubRoundDataView.getChildByName("Mask");
		this.View = this.ClubRoundDataView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.DIBG = this.BG.getChildByName("DIBG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.clubChipIcon = this.View.getChildByName("clubChipIcon");
		this.icon1 = this.View.getChildByName("icon1");
		this.icon2 = this.View.getChildByName("icon2");
		this.commBg1 = this.View.getChildByName("commBg1");
		this.commBg2 = this.View.getChildByName("commBg2");
		this.commBg3 = this.View.getChildByName("commBg3");
		this.dataBG = this.View.getChildByName("dataBG");
		this.labelNLH = this.dataBG.getChildByName("labelNLH");
		this.labelGold = this.View.getChildByName("labelGold");
		this.labelTotalTime = this.View.getChildByName("labelTotalTime");
		this.label = this.View.getChildByName("label");
		this.labelTime = this.View.getChildByName("labelTime");
		this.labelID = this.View.getChildByName("labelID");
		this.labelChip = this.View.getChildByName("labelChip");
		this.labelBuyTips = this.View.getChildByName("labelBuyTips");
		this.labelFeeTips = this.View.getChildByName("labelFeeTips");
		this.labelInsTips = this.View.getChildByName("labelInsTips");
		this.labelBuy = this.View.getChildByName("labelBuy");
		this.labelServiceFee = this.View.getChildByName("labelServiceFee");
		this.labelInsurance = this.View.getChildByName("labelInsurance");
		this.ScrollView = this.View.getChildByName("ScrollView");
		this.sortLayer = this.ScrollView.getChildByName("sortLayer");
		this.labelUser = this.sortLayer.getChildByName("labelUser");
		this.cardSortBoxItem = this.sortLayer.getChildByName("cardSortBoxItem");
		this.winLoseSortBoxItem = this.sortLayer.getChildByName("winLoseSortBoxItem");
		this.view = this.ScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");

    }
}
