import UBaseView from "../../../../framwork/widget/UBaseView";
import UIClubRoundDataItem_auto from "./UIClubRoundDataItem_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubRoundDataItem extends UBaseView {

	protected ui: UIClubRoundDataItem_auto = null;
	private _curData = null;

	onUILoad(): void {
		this.ui = new UIClubRoundDataItem_auto(this.node);
	}

	setData(data: any) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;
		this.ui.labelName.getComponent(cc.Label).string = "";
		this.ui.labelID.getComponent(cc.Label).string = "";
		this.ui.labelCardCount.getComponent(cc.Label).string = "";
		this.ui.labelWinLose.getComponent(cc.Label).string = "";
	}
}