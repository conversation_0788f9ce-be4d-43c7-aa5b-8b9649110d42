{"frameRate": 60, "isGlobal": 0, "name": "xihongshi_2", "version": "4.5", "armature": [{"slot": [{"color": {}, "name": "Layer3", "parent": "Layer3"}, {"z": 1, "color": {}, "name": "Layer1", "parent": "Layer1"}, {"z": 2, "color": {}, "name": "Layer2", "parent": "Layer2"}], "type": "Armature", "ik": [], "defaultActions": [{"gotoAndPlay": "Animation1"}], "aabb": {"width": 177, "y": -97.2723, "height": 177, "x": -91.746}, "skin": [{"slot": [{"display": [{"path": "组-4-拷贝1", "type": "image", "name": "组-4-拷贝1", "transform": {"y": -0.5, "x": -0.5}}, {"path": "004", "type": "image", "name": "004", "transform": {}}, {"path": "005", "type": "image", "name": "005", "transform": {}}], "name": "Layer1"}, {"display": [{"path": "003", "type": "image", "name": "003", "transform": {}}], "name": "Layer3"}, {"display": [{"path": "004", "type": "image", "name": "004", "transform": {}}], "name": "Layer2"}], "name": ""}], "animation": [{"slot": [{"frame": [{"tweenEasing": 0, "duration": 25}, {"tweenEasing": 0, "duration": 65}, {"tweenEasing": null, "duration": 0}], "name": "Layer2"}, {"frame": [{"tweenEasing": 0, "duration": 20, "displayIndex": -1}, {"tweenEasing": 0, "duration": 6, "displayIndex": -1}, {"tweenEasing": 0, "duration": 64, "displayIndex": 2}, {"tweenEasing": null, "duration": 0, "displayIndex": 2}], "name": "Layer1"}, {"frame": [{"tweenEasing": 0, "duration": 4}, {"tweenEasing": 0, "duration": 7, "displayIndex": -1}, {"tweenEasing": 0, "duration": 79}, {"tweenEasing": null, "duration": 0}], "name": "Layer3"}], "frame": [], "ik": [], "ffd": [], "duration": 90, "name": "Animation1", "bone": [{"frame": [{"duration": 25, "transform": {}, "tweenEasing": 0}, {"duration": 65, "transform": {}, "tweenEasing": 0}, {"duration": 0, "transform": {"y": 49.4117, "x": -1.1765}}], "name": "Layer2"}, {"frame": [{"duration": 20, "transform": {}, "tweenEasing": 0}, {"duration": 6, "transform": {}, "tweenEasing": 0}, {"duration": 64, "transform": {"y": -6.4706, "scY": 0.122, "x": 2.9412}, "tweenEasing": 0}, {"duration": 0, "transform": {"y": 5.8824, "x": 4.1176}}], "name": "Layer1"}, {"frame": [{"duration": 4, "transform": {"scX": 0.5747, "scY": 0.1439}, "tweenEasing": 0}, {"duration": 7, "transform": {}, "tweenEasing": 0}, {"duration": 79, "transform": {}, "tweenEasing": 0}, {"duration": 0, "transform": {}}], "name": "Layer3"}, {"frame": [{"duration": 90, "transform": {}}], "name": "root"}]}], "frameRate": 60, "name": "armatureName", "bone": [{"transform": {}, "name": "root"}, {"inheritScale": false, "transform": {"y": -22.3204, "scX": 1.1756, "scY": 1.1097, "x": -5.3823}, "name": "Layer3", "parent": "root"}, {"inheritScale": false, "transform": {"y": -8.2028, "scX": 1.139, "scY": 1.139, "x": -2.6765}, "name": "Layer1", "parent": "root"}, {"inheritScale": false, "transform": {"y": -23.9675, "x": 1.0882}, "name": "Layer2", "parent": "root"}]}]}