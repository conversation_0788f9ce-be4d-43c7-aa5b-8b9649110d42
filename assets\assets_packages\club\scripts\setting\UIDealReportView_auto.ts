export default class UIDealReportView_auto {
    node:cc.Node = null;   
	DealReportView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	titleContainer: cc.Node;
	title: cc.Node;
	closedBtn: cc.Node;
	closedBtnBg: cc.Node;
	dataList: cc.Node;
	scrollBar: cc.Node;
	bar: cc.Node;
	view: cc.Node;
	content: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.DealReportView = this.node;
		this.mask = this.DealReportView.getChildByName("mask");
		this.mainContainer = this.DealReportView.getChildByName("mainContainer");
		this.titleContainer = this.mainContainer.getChildByName("titleContainer");
		this.title = this.titleContainer.getChildByName("title");
		this.closedBtn = this.titleContainer.getChildByName("closedBtn");
		this.closedBtnBg = this.closedBtn.getChildByName("closedBtnBg");
		this.dataList = this.mainContainer.getChildByName("dataList");
		this.scrollBar = this.dataList.getChildByName("scrollBar");
		this.bar = this.scrollBar.getChildByName("bar");
		this.view = this.dataList.getChildByName("view");
		this.content = this.view.getChildByName("content");

    }
}
