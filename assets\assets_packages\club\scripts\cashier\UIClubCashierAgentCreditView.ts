import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UISelectBox from "../../../okgame/common/scripts/SelectBox/UISelectBox";
import { HORIZONTAL_ALIGN, OptionsItemMode, SORT_TYPE, VERTICAL_ALIGN } from "../../../okgame/common/scripts/SelectBox/UISelectBoxOptions";
import UIClubCashierAgentCreditView_auto from "./UIClubCashierAgentCreditView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierAgentCreditView extends UBaseSFixedDialog {

	protected ui: UIClubCashierAgentCreditView_auto = null;

	private sortField: number = 0;
	private sortDirection: SORT_TYPE = SORT_TYPE.ASC;

	onUILoad(): void {
		super.onUILoad();
		this.ui = new UIClubCashierAgentCreditView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.initSort();
	}

	onShow(): void {
		this.reqData();
	}

	initSort() {
		let selectBox: UISelectBox = this.ui.selectSortBox.getComponent(UPrefabContainer).getNodeComponent(UISelectBox);
		let options = [
			ULanguage.getInstance().getLangByID("club.creditBalance"),
			ULanguage.getInstance().getLangByID("club.downlineChips"),
		];
		selectBox.init(options, options[0], (_sortFiled: number, value: number | string, _sortDirection: SORT_TYPE) => {
			this.sortField = _sortFiled;
			this.sortDirection = _sortDirection;
			// this.refreshListView();
		}, OptionsItemMode.SORT, VERTICAL_ALIGN.DOWN, HORIZONTAL_ALIGN.RIGHT);
	}

	/**
	* 获取服务器成员列表
	* 重要: 0616决定不采用分页模式，请求接口全部返回后，本地做排序处理
	* 另外: 涉及到几千上万个俱乐部成员的话，后期再做处理，目前先不考虑
	*/
	async reqData() {
		// let data: okpb.RspGetClubUserList = await ClubAPI.getInstance().ReqGetClubMemberList();
		// if (!cc.isValid(this.node)) return;
		// if (data && data.errorCode == okpb.ERET.OK) {
		// 	let rspMap = data.data.clubUserMap;
		// 	if (rspMap) {
		// 		let allUsers: okpb.ClubUserPO[] = [];
		// 		for (const key in rspMap) {
		// 			if (rspMap.hasOwnProperty(key)) {
		// 				allUsers.push(...(rspMap[key].users ?? []));
		// 			}
		// 		}
		// 		this.updateListData(data, allUsers);
		// 	}
		// }
	}
}