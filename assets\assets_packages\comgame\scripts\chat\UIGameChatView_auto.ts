export default class UIGameChatView_auto {
    node:cc.Node = null;   
	GameChatView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	chatList: cc.Node;
	view: cc.Node;
	content: cc.Node;
	chatInputContainer: cc.Node;
	partingLine: cc.Node;
	chatNormal: cc.Node;
	whiteBg: cc.Node;
	chatEditBox: cc.Node;
	BACKGROUND_SPRITE: cc.Node;
	TEXT_LABEL: cc.Node;
	PLACEHOLDER_LABEL: cc.Node;
	touchBlocker: cc.Node;
	keybordButton: cc.Node;
	emoji: cc.Node;
	keybord: cc.Node;
	sendBtn: cc.Node;
	text: cc.Node;
	chatDisabled: cc.Node;
	disableWhiteBg: cc.Node;
	disableIcon: cc.Node;
	disableSilenceTxt: cc.Node;
	disableSendBtn: cc.Node;
	disableText: cc.Node;
	goBottom: cc.Node;
	loading: cc.Node;
	Flower_Line_1: cc.Node;
	Flower_Line_2: cc.Node;
	Flower_Line_3: cc.Node;
	Flower_Line_4: cc.Node;
	Flower_Line_5: cc.Node;
	Flower_Line_6: cc.Node;
	Flower_Line_7: cc.Node;
	Flower_Line_8: cc.Node;
	Flower_Line_9: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.GameChatView = this.node;
		this.mask = this.GameChatView.getChildByName("mask");
		this.mainContainer = this.GameChatView.getChildByName("mainContainer");
		this.chatList = this.mainContainer.getChildByName("chatList");
		this.view = this.chatList.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.chatInputContainer = this.mainContainer.getChildByName("chatInputContainer");
		this.partingLine = this.chatInputContainer.getChildByName("partingLine");
		this.chatNormal = this.chatInputContainer.getChildByName("chatNormal");
		this.whiteBg = this.chatNormal.getChildByName("whiteBg");
		this.chatEditBox = this.whiteBg.getChildByName("chatEditBox");
		this.BACKGROUND_SPRITE = this.chatEditBox.getChildByName("BACKGROUND_SPRITE");
		this.TEXT_LABEL = this.chatEditBox.getChildByName("TEXT_LABEL");
		this.PLACEHOLDER_LABEL = this.chatEditBox.getChildByName("PLACEHOLDER_LABEL");
		this.touchBlocker = this.whiteBg.getChildByName("touchBlocker");
		this.keybordButton = this.whiteBg.getChildByName("keybordButton");
		this.emoji = this.keybordButton.getChildByName("emoji");
		this.keybord = this.keybordButton.getChildByName("keybord");
		this.sendBtn = this.chatNormal.getChildByName("sendBtn");
		this.text = this.sendBtn.getChildByName("text");
		this.chatDisabled = this.chatInputContainer.getChildByName("chatDisabled");
		this.disableWhiteBg = this.chatDisabled.getChildByName("disableWhiteBg");
		this.disableIcon = this.disableWhiteBg.getChildByName("disableIcon");
		this.disableSilenceTxt = this.disableWhiteBg.getChildByName("disableSilenceTxt");
		this.disableSendBtn = this.chatDisabled.getChildByName("disableSendBtn");
		this.disableText = this.disableSendBtn.getChildByName("disableText");
		this.goBottom = this.mainContainer.getChildByName("goBottom");
		this.loading = this.mainContainer.getChildByName("loading");
		this.Flower_Line_1 = this.loading.getChildByName("Flower_Line_1");
		this.Flower_Line_2 = this.loading.getChildByName("Flower_Line_2");
		this.Flower_Line_3 = this.loading.getChildByName("Flower_Line_3");
		this.Flower_Line_4 = this.loading.getChildByName("Flower_Line_4");
		this.Flower_Line_5 = this.loading.getChildByName("Flower_Line_5");
		this.Flower_Line_6 = this.loading.getChildByName("Flower_Line_6");
		this.Flower_Line_7 = this.loading.getChildByName("Flower_Line_7");
		this.Flower_Line_8 = this.loading.getChildByName("Flower_Line_8");
		this.Flower_Line_9 = this.loading.getChildByName("Flower_Line_9");

    }
}
