export default class UIClubLevelView_auto {
    node:cc.Node = null;   
	ClubLevelView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	titleContainer: cc.Node;
	closedBtn: cc.Node;
	closedBtnBg: cc.Node;
	levelTitle: cc.Node;
	levelDetail: cc.Node;
	levelLabel: cc.Node;
	levelManage: cc.Node;
	manageIcon: cc.Node;
	manageNum: cc.Node;
	levelMember: cc.Node;
	memberIcon: cc.Node;
	memberNum: cc.Node;
	diamond: cc.Node;
	diamondIcon: cc.Node;
	diamondNum: cc.Node;
	levelTip: cc.Node;
	tipText: cc.Node;
	tipIcon: cc.Node;
	dataList: cc.Node;
	scrollBar: cc.Node;
	bar: cc.Node;
	view: cc.Node;
	content: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubLevelView = this.node;
		this.mask = this.ClubLevelView.getChildByName("mask");
		this.mainContainer = this.ClubLevelView.getChildByName("mainContainer");
		this.titleContainer = this.mainContainer.getChildByName("titleContainer");
		this.closedBtn = this.titleContainer.getChildByName("closedBtn");
		this.closedBtnBg = this.closedBtn.getChildByName("closedBtnBg");
		this.levelTitle = this.titleContainer.getChildByName("levelTitle");
		this.levelDetail = this.mainContainer.getChildByName("levelDetail");
		this.levelLabel = this.levelDetail.getChildByName("levelLabel");
		this.levelManage = this.levelDetail.getChildByName("levelManage");
		this.manageIcon = this.levelManage.getChildByName("manageIcon");
		this.manageNum = this.levelManage.getChildByName("manageNum");
		this.levelMember = this.levelDetail.getChildByName("levelMember");
		this.memberIcon = this.levelMember.getChildByName("memberIcon");
		this.memberNum = this.levelMember.getChildByName("memberNum");
		this.diamond = this.levelDetail.getChildByName("diamond");
		this.diamondIcon = this.diamond.getChildByName("diamondIcon");
		this.diamondNum = this.diamond.getChildByName("diamondNum");
		this.levelTip = this.levelDetail.getChildByName("levelTip");
		this.tipText = this.levelTip.getChildByName("tipText");
		this.tipIcon = this.levelTip.getChildByName("tipIcon");
		this.dataList = this.mainContainer.getChildByName("dataList");
		this.scrollBar = this.dataList.getChildByName("scrollBar");
		this.bar = this.scrollBar.getChildByName("bar");
		this.view = this.dataList.getChildByName("view");
		this.content = this.view.getChildByName("content");

    }
}
