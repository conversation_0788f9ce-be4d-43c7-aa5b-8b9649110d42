export default class UIDealReportCell_auto {
    node:cc.Node = null;   
	ClubCareerCell: cc.Node;
	cellTitle: cc.Node;
	cellTip: cc.Node;
	shipNode: cc.Node;
	shipIcon: cc.Node;
	shipValue: cc.Node;
	cellTime: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCareerCell = this.node;
		this.cellTitle = this.ClubCareerCell.getChildByName("cellTitle");
		this.cellTip = this.ClubCareerCell.getChildByName("cellTip");
		this.shipNode = this.ClubCareerCell.getChildByName("shipNode");
		this.shipIcon = this.shipNode.getChildByName("shipIcon");
		this.shipValue = this.shipNode.getChildByName("shipValue");
		this.cellTime = this.ClubCareerCell.getChildByName("cellTime");

    }
}
