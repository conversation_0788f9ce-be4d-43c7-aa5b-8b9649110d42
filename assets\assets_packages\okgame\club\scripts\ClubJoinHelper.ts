import { ULanguage } from "../../../../framwork/language/ULanguage";
import OKClubAP<PERSON> from "./OKClubAPI";
import OKGameManager from "../../public/OKGameManager";
import { okpb } from "../../proto/proto_msg";
import { OKGAME_UIID } from "../../public/OKGameConst";

const TAG = "[ClubJoinHelper]";
/**
 * 俱乐部加入帮助类
 * @description UIEmptyClubNode、ClubJoinView 2个不同的地方会调用
 */
export class ClubJoinHelper {
    private static instance: ClubJoinHelper = null;

    public static getInstance(): ClubJoinHelper {
        if (!this.instance) {
            this.instance = new ClubJoinHelper();
        }
        return this.instance;
    }

    /**
     * 处理俱乐部加入请求
     * @param clubId 俱乐部ID
     * @param referrer 推荐人ID
     */
    public handleClubJoin(clubId: string, referrer: string, callback: Function) {
        if (clubId.length === 0) {
            OKGameManager.getInstance().showToastById("club.enterClubId");
            return;
        }

        // 如果clubId不能转换为数字，则return
        const clubIdNumber = parseInt(clubId);
        if (isNaN(clubIdNumber)) {
            OKGameManager.getInstance().showToastById("club.enterClubId");
            return;
        }

        this.reqGetClubInfo(clubId, referrer, callback);
    }

    /**
     * 获取俱乐部信息
     * @param clubID 俱乐部ID
     * @param referrID 推荐人ID
     */
    private reqGetClubInfo(clubID: string, referrID: string, callback: Function) {
        const reqReferrerID = referrID && !isNaN(Number(referrID)) ? Number(referrID) : 0;
        OKGameManager.getInstance().showLoading();
        console.log(TAG, "reqGetClubInfo: ", clubID, referrID, reqReferrerID);
        OKClubAPI.getInstance()
            .ReqSearchClub(Number(clubID), reqReferrerID)
            .then((rsp: okpb.RspSearchClub) => {
                // rsp.data = this.getTestData();
                if (rsp.errorCode === 0) {
                    if (rsp.data) {
                        this.onQueryClubSuccess(rsp.data, reqReferrerID, callback);
                    } else {
                        console.warn(TAG, "Rsp 俱乐部信息为空");
                        this.onQueryClubFailed(1010, "Rsp 俱乐部信息为空");
                    }
                } else {
                    this.onQueryClubFailed(rsp.errorCode, rsp.errMsg);
                }
            })
            .catch((err) => {
                console.error(TAG, "reqGetClubInfo error: ", err);
                this.onQueryClubFailed(1010, err.message);
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }

    /**
     * 查询俱乐部信息成功回调
     */
    private onQueryClubSuccess(
        clubInfo: okpb.ClubResp,
        reqReferrerID: number,
        callback: Function
    ): void {
        // 跳转申请加入俱乐部dialog：
        OKGameManager.getInstance().showDialog(
            OKGAME_UIID.ClubApplicationView,
            clubInfo,
            reqReferrerID
        );

        callback && callback(clubInfo);
    }

    /**
     * 查询俱乐部信息失败回调
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     */
    private onQueryClubFailed(errorCode: number, errorMsg: string): void {
        console.log(TAG, "查询俱乐部信息失败", errorCode, errorMsg);
        OKGameManager.getInstance().showToast(
            ULanguage.getInstance().getLangByID("errorCode." + errorCode)
        );
    }
}
