import UBaseView from "../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UIIdentityIcon from "../../../okgame/common/scripts/UIIdentityIcon";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubAPI from "../ClubAPI";
import UIClubCashierChipRequestLayer from "./UIClubCashierChipRequestLayer";
import UIClubChipRequestItem_auto from "./UIClubChipRequestItem_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubChipRequestItem extends UBaseView {

	protected ui: UIClubChipRequestItem_auto = null;
	private _curData: okpb.ClubChipsApplyData = null;
	private father: UIClubCashierChipRequestLayer = null;

	onUILoad(): void {
		this.ui = new UIClubChipRequestItem_auto(this.node);
		this.onRegisterEvent(this.ui.btnOK, this.onBtnOK.bind(this));
		this.onRegisterEvent(this.ui.btnCancel, this.onBtnCancel.bind(this));
	}

	onEnable(): void {
		this.updateUI();
	}

	setData(father: UIClubCashierChipRequestLayer, data: okpb.ClubChipsApplyData) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;
		this.father = father;
		this.updateUI();
	}

	updateUI() {
		if (!cc.isValid(this.ui) || !this._curData) return;
		this.ui.labelTime.getComponent(cc.Label).string = "";
		this.ui.labelNoteName.getComponent(cc.Label).string = "";
		this.ui.freeTxt.getComponent(cc.Label).string = "" + this._curData.applyChips;
		this.ui.userID.getComponent(cc.Label).string = "" + this._curData.userId;
		// this.ui.roleTypeImg.getComponent(UPrefabContainer).getNodeComponent(UIIdentityIcon).setIcon(this._curData.identity);
	}

	//筹码申请，同意
	async onBtnOK() {
		let data = await ClubAPI.getInstance().ReqAuditClubChipsApply(true, [this._curData.applyId]);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode === 0) {
			if (this.father.updateOperaChip) this.father.updateOperaChip(this._curData.userId);
		}
	}

	//筹码申请，拒绝
	async onBtnCancel() {
		let data = await ClubAPI.getInstance().ReqAuditClubChipsApply(false, [this._curData.applyId]);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode === 0) {
			if (this.father.updateOperaChip) this.father.updateOperaChip(this._curData.userId);
		}
	}
}