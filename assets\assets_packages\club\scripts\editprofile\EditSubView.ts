import { ULanguage } from "../../../../framwork/language/ULanguage";
import { okpb } from "../../../okgame/proto/proto_msg";
import BaseEditView from "./BaseEditView";

const { ccclass, property } = cc._decorator;
const TAG = "[EditSubView]";

// 编辑类型常量
export const EDIT_TYPE_NAME = 1; // 编辑名称
export const EDIT_TYPE_NOTICE = 2; // 编辑公告

/**
 * 编辑俱乐部名称和俱乐部公告的子视图
 */
@ccclass
export default class EditSubView extends BaseEditView {
    @property(cc.Label)
    titleLabel: cc.Label = null;

    @property(cc.Node)
    nameNode: cc.Node = null;

    @property(cc.EditBox)
    nameEditBox: cc.EditBox = null;

    @property(cc.Label)
    nameDescLabel: cc.Label = null;

    @property(cc.Node)
    noticeNode: cc.Node = null;

    @property(cc.EditBox)
    noticeEditBox: cc.EditBox = null;

    @property(cc.Button)
    btnConfirm: cc.Button = null;

    private clubInfo: okpb.ClubResp;
    private editType: number;
    private callbackFun: (clubInfo: okpb.ClubResp) => void;

    onUILoad(): void {
        this.onRegisterEvent(this.btnConfirm.node, this.clickConfirm.bind(this));
    }

    /**
     * 显示编辑视图
     * @param clubInfo 俱乐部信息
     * @param editType 编辑类型
     * @param updateCallback 更新回调
     */
    onShow(clubInfo: okpb.ClubResp, editType: number, _callbackFun: (clubInfo: okpb.ClubResp) => void) {
        this.clubInfo = clubInfo;
        this.editType = editType;
        console.log(TAG, "二级编辑页面: ", this.clubInfo, this.editType);
        this.callbackFun = _callbackFun;

        // 根据编辑类型显示对应的编辑框
        this.nameEditBox.node.active = editType === EDIT_TYPE_NAME;
        this.noticeEditBox.node.active = editType === EDIT_TYPE_NOTICE;

        if (editType === EDIT_TYPE_NAME) {
            this.nameNode.active = true;
            this.noticeNode.active = false;
            this.titleLabel.string = this.getLangTxt("club.clubName");
            const clubName = this.clubInfo.clubName;
            if (clubName && clubName.length > 0) {
                this.nameEditBox.string = clubName;
            }
            // 判断是否是首次改名
            if (this.isFirstRename()) {
                console.log(TAG, "isFirstRename");
                this.nameDescLabel.string = this.getLangTxt("firstRenameFree");
            } else {
                console.log(TAG, "not isFirstRename");
                const diamondCost = this.clubInfo.clubNameModifyCfgPo.price;
                const costText = this.getLangTxt("club.renameCost").replace("{0}", diamondCost.toString());
                this.nameDescLabel.string = costText;
            }
        } else if (editType === EDIT_TYPE_NOTICE) {
            this.nameNode.active = false;
            this.noticeNode.active = true;
            this.titleLabel.string = this.getLangTxt("club.announcement");
            const clubNotice = this.clubInfo.intro;
            if (clubNotice && clubNotice.length > 0) {
                this.noticeEditBox.string = clubNotice;
            } else {
                // 限制输入256个字符
                this.noticeEditBox.placeholder = this.getLangTxt("club.clubNoticePlaceholder");
            }
        }
    }

    private getLangTxt(key: string): string {
        return ULanguage.getInstance().getLangByID(key);
    }

    /**
     * 判断是否是首次改名
     */
    private isFirstRename(): boolean {
        const modifyCount = this.clubInfo.clubNameModifyCount;
        console.log(TAG, "modifyCount: ", modifyCount, modifyCount === 0);
        return modifyCount === 0;
    }

    private clickConfirm(): void {
        if (this.editType === EDIT_TYPE_NAME) {
            const newClubName = this.nameEditBox.string;
            if (newClubName.length === 0) {
                this.showThisToast("club.clubNameEmpty");
                return;
            }
            if (newClubName.length < 6) {
                this.showThisToast("club.clubNameTooShort");
                return;
            }
            if (newClubName.length > 20) {
                this.showThisToast("club.clubNameTooLong");
                return;
            }
            this.clubInfo.clubName = newClubName;
        } else if (this.editType === EDIT_TYPE_NOTICE) {
            this.clubInfo.intro = this.noticeEditBox.string;
        }
        // 执行网络请求，更新俱乐部名称or俱乐部公告
        this.requestEditProfile(this.clubInfo, (clubInfo: okpb.ClubResp) => {
            this.callbackFun && this.callbackFun(clubInfo);
            this.closeDialog();
        });
    }
}
