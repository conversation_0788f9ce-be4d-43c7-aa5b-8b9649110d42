import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import ClubAPI from "../../ClubAPI";
import ClubDataManager from "../../ClubDataManager";
import MemberHelper from "../MemberHelper";

const { ccclass, property } = cc._decorator;
const TAG = "SetRoleDialog";
/**
 * 设置角色弹框：管理员、开桌员
 */
@ccclass
export default class SetRoleDialog extends UBaseDialog {
    @property(cc.Button)
    closeBtn: cc.Button = null;

    @property(cc.Button)
    confirmBtn: cc.Button = null;

    @property(cc.ToggleContainer)
    toggleContainer: cc.ToggleContainer = null;

    @property(cc.Toggle)
    toggleAdmin: cc.Toggle = null;

    @property(cc.Toggle)
    toggleDealer: cc.Toggle = null;

    private clubId: number = 0;
    private targetUserId: number = 0;
    private operationCallback: Function = null;
    /**
     * 原始的角色
     */
    private sourceIdentity: okpb.Identity = null;
    /**
     * 选择的角色
     */
    private newIdentity: okpb.Identity = okpb.Identity.IDE_MANAGER;

    onUILoad(): void {
        this.initView();
        this.initEvent();
    }

    private initView(): void {
        this.onRegisterEvent(this.toggleAdmin.node, this.onAdminToggleClick.bind(this));
        this.onRegisterEvent(this.toggleDealer.node, this.onDealerToggleClick.bind(this));
    }

    private onAdminToggleClick(): void {
        console.log(TAG, "onAdminToggleClick()");
        this.newIdentity = okpb.Identity.IDE_MANAGER;
    }

    private onDealerToggleClick(): void {
        console.log(TAG, "onDealerToggleClick()");
        this.newIdentity = okpb.Identity.IDE_TABLE_OPERATOR;
    }

    private initEvent(): void {
        this.onRegisterEvent(this.closeBtn.node, this.clickCancel.bind(this));
        this.onRegisterEvent(this.confirmBtn.node, this.changeIdentity.bind(this));
    }

    onShow(clubId: number, targetUserId: number, sourceIdentity: okpb.Identity, operationCallback: Function): void {
        console.log(TAG, "onShow() 原始角色: ", MemberHelper.getRoleName(sourceIdentity));
        this.clubId = clubId;
        this.targetUserId = targetUserId;
        this.operationCallback = operationCallback;
        this.sourceIdentity = sourceIdentity;

        // 设置默认选中的角色
        if (sourceIdentity === okpb.Identity.IDE_TABLE_OPERATOR) {
            console.log(TAG, "onShow() 设置默认选中开桌员 toggle");
            this.toggleAdmin.isChecked = false;
            this.toggleDealer.isChecked = true;
        } else if (sourceIdentity === okpb.Identity.IDE_MANAGER) {
            console.log(TAG, "onShow() 设置默认选中管理员 toggle");
            this.toggleAdmin.isChecked = true;
            this.toggleDealer.isChecked = false;
        }
    }

    /**
     * 点击取消
     */
    private clickCancel(): void {
        this.operationCallback && this.operationCallback(this.sourceIdentity);
        this.closeDialog();
    }

    /**
     * 修改用户角色
     * @param newIdentity 新角色
     */
    private changeIdentity(): void {
        console.log(TAG, "Req: 修改角色请求: ", this.clubId, [this.targetUserId], MemberHelper.getRoleName(this.newIdentity));
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqChangeClubUserIdentity(this.clubId, [this.targetUserId], this.newIdentity)
            .then((res: okpb.RspChangeClubUserIdentity) => {
                console.log(TAG, "Rsp: 修改角色返回: ", res);
                if (cc.isValid(this.node)) {
                    if (res && res.errorCode === 0) {
                        ClubDataManager.getInstance().updateMemberIdentity(this.clubId, this.targetUserId, this.newIdentity);
                        this.operationCallback && this.operationCallback(this.newIdentity);
                        this.closeDialog();
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    console.error(TAG, "Req: 修改角色请求失败: ", err);
                    this.operationCallback && this.operationCallback(this.sourceIdentity);
                }
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }
}
