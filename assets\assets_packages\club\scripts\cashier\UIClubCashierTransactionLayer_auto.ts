export default class UIClubCashierTransactionLayer_auto {
    node:cc.Node = null;   
	ClubCashierTransactionLayer: cc.Node;
	DataLayer: cc.Node;
	btnCashierBalance: cc.Node;
	cashierBalanceBG: cc.Node;
	clubChipIcon1: cc.Node;
	btnChipAdd: cc.Node;
	sprChipAdd: cc.Node;
	btnCashierChip: cc.Node;
	cashierChipBG: cc.Node;
	clubChipIcon3: cc.Node;
	btnCashierAgent: cc.Node;
	cashierAgentBG: cc.Node;
	clubChipIcon2: cc.Node;
	labelAgentTips: cc.Node;
	labelAgent: cc.Node;
	btnCurChips: cc.Node;
	cashierCurChipsBG: cc.Node;
	clubChipIcon4: cc.Node;
	labelHoldChips: cc.Node;
	labelHoldChipsTips: cc.Node;
	labelCashierBalanceTips: cc.Node;
	labelChipsTips: cc.Node;
	labelCashierBalance: cc.Node;
	labelChips: cc.Node;
	SearchLayer: cc.Node;
	SelectLayer: cc.Node;
	toggleSelect: cc.Node;
	Background: cc.Node;
	checkmark: cc.Node;
	labelSelectTips: cc.Node;
	selectSortBox: cc.Node;
	TransactionScrollView: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;
	ButtomLayer: cc.Node;
	btnRecevor: cc.Node;
	labeRecevor: cc.Node;
	btnTicket: cc.Node;
	labeTicket: cc.Node;
	btnSend: cc.Node;
	labeSend: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierTransactionLayer = this.node;
		this.DataLayer = this.ClubCashierTransactionLayer.getChildByName("DataLayer");
		this.btnCashierBalance = this.DataLayer.getChildByName("btnCashierBalance");
		this.cashierBalanceBG = this.btnCashierBalance.getChildByName("cashierBalanceBG");
		this.clubChipIcon1 = this.btnCashierBalance.getChildByName("clubChipIcon1");
		this.btnChipAdd = this.btnCashierBalance.getChildByName("btnChipAdd");
		this.sprChipAdd = this.btnChipAdd.getChildByName("sprChipAdd");
		this.btnCashierChip = this.DataLayer.getChildByName("btnCashierChip");
		this.cashierChipBG = this.btnCashierChip.getChildByName("cashierChipBG");
		this.clubChipIcon3 = this.btnCashierChip.getChildByName("clubChipIcon3");
		this.btnCashierAgent = this.DataLayer.getChildByName("btnCashierAgent");
		this.cashierAgentBG = this.btnCashierAgent.getChildByName("cashierAgentBG");
		this.clubChipIcon2 = this.btnCashierAgent.getChildByName("clubChipIcon2");
		this.labelAgentTips = this.btnCashierAgent.getChildByName("labelAgentTips");
		this.labelAgent = this.btnCashierAgent.getChildByName("labelAgent");
		this.btnCurChips = this.DataLayer.getChildByName("btnCurChips");
		this.cashierCurChipsBG = this.btnCurChips.getChildByName("cashierCurChipsBG");
		this.clubChipIcon4 = this.btnCurChips.getChildByName("clubChipIcon4");
		this.labelHoldChips = this.btnCurChips.getChildByName("labelHoldChips");
		this.labelHoldChipsTips = this.btnCurChips.getChildByName("labelHoldChipsTips");
		this.labelCashierBalanceTips = this.DataLayer.getChildByName("labelCashierBalanceTips");
		this.labelChipsTips = this.DataLayer.getChildByName("labelChipsTips");
		this.labelCashierBalance = this.DataLayer.getChildByName("labelCashierBalance");
		this.labelChips = this.DataLayer.getChildByName("labelChips");
		this.SearchLayer = this.ClubCashierTransactionLayer.getChildByName("SearchLayer");
		this.SelectLayer = this.ClubCashierTransactionLayer.getChildByName("SelectLayer");
		this.toggleSelect = this.SelectLayer.getChildByName("toggleSelect");
		this.Background = this.toggleSelect.getChildByName("Background");
		this.checkmark = this.toggleSelect.getChildByName("checkmark");
		this.labelSelectTips = this.SelectLayer.getChildByName("labelSelectTips");
		this.selectSortBox = this.SelectLayer.getChildByName("selectSortBox");
		this.TransactionScrollView = this.ClubCashierTransactionLayer.getChildByName("TransactionScrollView");
		this.view = this.TransactionScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.TransactionScrollView.getChildByName("listEmpty");
		this.ButtomLayer = this.ClubCashierTransactionLayer.getChildByName("ButtomLayer");
		this.btnRecevor = this.ButtomLayer.getChildByName("btnRecevor");
		this.labeRecevor = this.btnRecevor.getChildByName("labeRecevor");
		this.btnTicket = this.ButtomLayer.getChildByName("btnTicket");
		this.labeTicket = this.btnTicket.getChildByName("labeTicket");
		this.btnSend = this.ButtomLayer.getChildByName("btnSend");
		this.labeSend = this.btnSend.getChildByName("labeSend");

    }
}
