import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import { ULanguage } from "../../../../../framwork/language/ULanguage";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import ClubAPI from "../../ClubAPI";
import ClubDataManager from "../../ClubDataManager";

const { ccclass, property } = cc._decorator;
const TAG = "[IssueAndCollectChipsDialog]";
@ccclass
export default class IssueAndCollectChipsDialog extends UBaseDialog {
    @property(cc.Label)
    titleTxt: cc.Label = null;

    @property(cc.Button)
    closeBtn: cc.Button = null;

    @property(cc.Label)
    chipDescTxt: cc.Label = null;

    @property(cc.Node)
    ChipBalanceNode: cc.Node = null;

    @property(cc.Sprite)
    CurrencyBgImg: cc.Sprite = null;

    @property(cc.Sprite)
    clubChipIcon: cc.Sprite = null;

    @property(cc.Label)
    balanceTxt: cc.Label = null;

    @property(cc.Node)
    numberNode: cc.Node = null;

    @property(cc.EditBox)
    numberEditBox: cc.EditBox = null;

    @property(cc.Button)
    btnConfirm: cc.Button = null;

    private type: string = "";

    private targetUserId: number = 0;

    /**
     * 授信余额，数据来源于： ChipshistoryDialog的chipBalance
     */
    private chipBalance: number = 0;

    // 俱乐部柜台筹码
    private clubCounterChips: number = 0;

    /** 成功回调函数 */
    private onSuccessCallback: (() => void) | null = null;

    onUILoad(): void {
        this.onRegisterEvent(this.closeBtn.node, this.closeDialog.bind(this));
        this.onRegisterEvent(this.btnConfirm.node, this.onConfirm.bind(this));

        // 设置输入框属性和监听
        this.setupNumberEditBox();
    }

    /**
     * 设置数字输入框的规范和监听
     */
    private setupNumberEditBox(): void {
        // 设置输入类型为数字
        this.numberEditBox.inputMode = cc.EditBox.InputMode.DECIMAL;
        this.numberEditBox.inputFlag = cc.EditBox.InputFlag.SENSITIVE;

        // 监听输入变化
        this.numberEditBox.node.on("text-changed", this.onNumberInputChanged, this);
        this.numberEditBox.node.on("editing-did-ended", this.onNumberInputEnded, this);
    }

    /**
     * 输入过程中的实时验证和格式化
     * @param editBox 输入框组件
     */
    private onNumberInputChanged(editBox: cc.EditBox): void {
        let inputValue = editBox.string;

        // 格式化输入
        const formattedValue = this.formatNumberInput(inputValue);

        // 如果格式化后的值与原值不同，更新输入框
        if (formattedValue !== inputValue) {
            editBox.string = formattedValue;
        }
    }

    /**
     * 输入结束时的最终格式化
     * @param editBox 输入框组件
     */
    private onNumberInputEnded(editBox: cc.EditBox): void {
        let inputValue = editBox.string.trim();

        // 如果输入为空，保持空白，让用户看到空状态
        if (!inputValue || inputValue === "") {
            editBox.string = "";
            return;
        }

        // 如果只有小数点，清空
        if (inputValue === ".") {
            editBox.string = "";
            return;
        }

        // 最终格式化
        const formattedValue = this.formatNumberInput(inputValue);
        editBox.string = formattedValue;
    }

    /**
     * 格式化数字输入
     * @param input 原始输入
     * @returns 格式化后的输入
     */
    private formatNumberInput(input: string): string {
        if (!input) return "";

        // 移除所有非数字和小数点的字符
        let cleaned = input.replace(/[^0-9.]/g, "");

        // 处理多个小数点的情况，只保留第一个
        const dotIndex = cleaned.indexOf(".");
        if (dotIndex !== -1) {
            const beforeDot = cleaned.substring(0, dotIndex);
            const afterDot = cleaned.substring(dotIndex + 1).replace(/\./g, "");
            cleaned = beforeDot + "." + afterDot;
        }

        // 分割整数部分和小数部分
        const parts = cleaned.split(".");
        let integerPart = parts[0] || "";
        let decimalPart = parts[1] || "";

        // 限制整数部分最多10位
        if (integerPart.length > 10) {
            integerPart = integerPart.substring(0, 10);
        }

        // 限制小数部分最多2位
        if (decimalPart.length > 2) {
            decimalPart = decimalPart.substring(0, 2);
        }

        // 移除整数部分开头的0，但保留单独的0
        if (integerPart.length > 1) {
            integerPart = integerPart.replace(/^0+/, "") || "0";
        }

        // 如果整数部分为空，设置为0
        if (!integerPart) {
            integerPart = "0";
        }

        // 组合结果
        let result = integerPart;
        if (decimalPart || cleaned.includes(".")) {
            result += "." + decimalPart;
        }

        // 如果结果只是小数点，返回0
        if (result === ".") {
            result = "0";
        }

        return result;
    }

    onShow(params: { type: string; targetUserId: number; chipBalance: number; onSuccess?: () => void }): void {
        console.log(TAG, "onShow() 显示发放/收回筹码弹框,接收到的传参: ", JSON.stringify(params));
        this.type = params.type;
        this.targetUserId = params.targetUserId;
        this.chipBalance = params.chipBalance;
        this.onSuccessCallback = params.onSuccess || null;
        this.clubCounterChips = ClubDataManager.getInstance().getClubInfoById(ClubDataManager.getInstance().getClubId())?.counterChips ?? 0;

        this.initView();
    }

    private initView(): void {
        this.titleTxt.string = this.type === "issue" ? ULanguage.getInstance().getLangByID("club.send") : ULanguage.getInstance().getLangByID("club.recover");
        // 如果是收回的话，显示被收回人的昵称
        if (this.type === "collect") {
            const targetUserInfo = ClubDataManager.getInstance().getMemberInfoById(this.targetUserId);
            this.chipDescTxt.string = targetUserInfo.nickname;
            this.balanceTxt.string = this.chipBalance.toString();
        } else {
            this.chipDescTxt.string = ULanguage.getInstance().getLangByID("club.cashierChipsBalance") + ":";
            // 俱乐部的柜台筹码余额
            const clubCounterChips = ClubDataManager.getInstance().getClubInfo()?.counterChips ?? 0;
            this.balanceTxt.string = clubCounterChips.toString();
        }
        this.numberEditBox.string = "";
    }

    onDestroy(): void {
        // 清理事件监听器
        if (this.numberEditBox && this.numberEditBox.node) {
            this.numberEditBox.node.off("text-changed", this.onNumberInputChanged, this);
            this.numberEditBox.node.off("editing-did-ended", this.onNumberInputEnded, this);
        }
    }

    private onConfirm(): void {
        console.log(TAG, "onConfirm() 确认发放/收回筹码");

        let inputValue = this.numberEditBox.string.trim();

        // 如果输入为空，设置为0
        if (!inputValue) {
            inputValue = "0";
            this.numberEditBox.string = "0";
        }

        // 基础验证：输入不能为0或等效的0值
        if (inputValue === "0" || inputValue === "0." || inputValue === "0.0" || inputValue === "0.00") {
            OKGameManager.getInstance().showToastById("club.inputError");
            return;
        }

        // 数值验证
        const chips = parseFloat(inputValue);
        if (isNaN(chips) || chips <= 0) {
            OKGameManager.getInstance().showToastById("club.inputError");
            return;
        }

        // 验证数值范围：不能超过最大值（10位整数+2位小数）
        if (chips >= 10000000000) {
            // 10^10
            OKGameManager.getInstance().showToast("输入金额过大，请重新输入");
            return;
        }

        // 验证小数位数（虽然输入时已经限制，这里再次确保）
        const decimalPlaces = this.getDecimalPlaces(inputValue);
        if (decimalPlaces > 2) {
            OKGameManager.getInstance().showToast("小数位数不能超过2位");
            return;
        }

        // 收回筹码时检查是否超过余额
        if (this.type === "collect" && chips > this.chipBalance) {
            OKGameManager.getInstance().showToastById("club.insufficientBalance");
            return;
        }

        // 发放筹码时检查柜台余额是否足够
        if (this.type === "issue" && chips > this.clubCounterChips) {
            OKGameManager.getInstance().showToast("柜台筹码余额不足");
            return;
        }

        if (this.type === "issue") {
            this.grantChips(chips);
        } else if (this.type === "collect") {
            this.recycleChips(chips);
        }
    }

    /**
     * 获取小数位数
     * @param value 数值字符串
     * @returns 小数位数
     */
    private getDecimalPlaces(value: string): number {
        const decimalIndex = value.indexOf(".");
        if (decimalIndex === -1) {
            return 0;
        }
        return value.length - decimalIndex - 1;
    }

    /**
     * 发放筹码
     * @param chips 筹码数量
     */
    private grantChips(chips: number): void {
        console.log(TAG, "grantChips() 发放筹码:", chips);
        OKGameManager.getInstance().showLoading();

        ClubAPI.getInstance()
            .ReqGrantClubChips([this.targetUserId], chips, okpb.ClubChipType.CCT_CREDIT)
            .then((res: okpb.RspGrantClubChips) => {
                console.log(TAG, "grantChips() 发放筹码结果:", res);
                if (cc.isValid(this.node)) {
                    if (res && res.errorCode === 0) {
                        OKGameManager.getInstance().showToastById("club.success");
                        // 调用成功回调
                        if (this.onSuccessCallback) {
                            this.onSuccessCallback();
                        }
                        this.closeDialog();
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    console.error(TAG, "grantChips() 发放筹码失败:", err);
                    OKGameManager.getInstance().showToast(err.message);
                }
            })
            .finally(() => {
                if (cc.isValid(this.node)) {
                    OKGameManager.getInstance().hideLoading();
                }
            });
    }

    /**
     * 回收筹码
     * @param chips 筹码数量
     */
    private recycleChips(chips: number): void {
        console.log(TAG, "recycleChips() Req回收筹码:", chips);
        OKGameManager.getInstance().showLoading();

        ClubAPI.getInstance()
            .ReqRecycleClubChips(false, [this.targetUserId], chips, okpb.ClubChipType.CCT_CREDIT)
            .then((res: okpb.RspRecycleClubChips) => {
                console.log(TAG, "recycleChips() Req回收筹码结果:", res);
                if (cc.isValid(this.node)) {
                    if (res && res.errorCode === 0) {
                        OKGameManager.getInstance().showToastById("club.success");
                        // 调用成功回调
                        if (this.onSuccessCallback) {
                            this.onSuccessCallback();
                        }
                        this.closeDialog();
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    console.error(TAG, "recycleChips() 回收筹码失败:", err);
                    OKGameManager.getInstance().showToast(err.message);
                }
            })
            .finally(() => {
                if (cc.isValid(this.node)) {
                    OKGameManager.getInstance().hideLoading();
                }
            });
    }
}
