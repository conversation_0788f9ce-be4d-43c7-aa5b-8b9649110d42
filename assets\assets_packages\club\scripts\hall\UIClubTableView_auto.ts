export default class UIClubTableView_auto {
    node:cc.Node = null;   
	ClubTableView: cc.Node;
	clubTabNode: cc.Node;
	clubTabScrollView: cc.Node;
	clubTabScrollMask: cc.Node;
	clubTabScrollContent: cc.Node;
	TabItem: cc.Node;
	TabItemDef: cc.Node;
	TabItemDefText: cc.Node;
	TabItemCheck: cc.Node;
	TabItemCheckText: cc.Node;
	clubTableList: cc.Node;
	tableListBg: cc.Node;
	TableScroll: cc.Node;
	TableScrollView: cc.Node;
	TableScrollContent: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubTableView = this.node;
		this.clubTabNode = this.ClubTableView.getChildByName("clubTabNode");
		this.clubTabScrollView = this.clubTabNode.getChildByName("clubTabScrollView");
		this.clubTabScrollMask = this.clubTabScrollView.getChildByName("clubTabScrollMask");
		this.clubTabScrollContent = this.clubTabScrollMask.getChildByName("clubTabScrollContent");
		this.TabItem = this.clubTabScrollContent.getChildByName("TabItem");
		this.TabItemDef = this.TabItem.getChildByName("TabItemDef");
		this.TabItemDefText = this.TabItemDef.getChildByName("TabItemDefText");
		this.TabItemCheck = this.TabItem.getChildByName("TabItemCheck");
		this.TabItemCheckText = this.TabItemCheck.getChildByName("TabItemCheckText");
		this.clubTableList = this.ClubTableView.getChildByName("clubTableList");
		this.tableListBg = this.clubTableList.getChildByName("tableListBg");
		this.TableScroll = this.clubTableList.getChildByName("TableScroll");
		this.TableScrollView = this.TableScroll.getChildByName("TableScrollView");
		this.TableScrollContent = this.TableScrollView.getChildByName("TableScrollContent");

    }
}
