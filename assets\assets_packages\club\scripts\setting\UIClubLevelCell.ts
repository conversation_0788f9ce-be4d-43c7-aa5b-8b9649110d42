import UBaseView from "../../../../framwork/widget/UBaseView";
import UIClubLevelCell_auto from "./UIClubLevelCell_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubLevelCell]";

/**
 * 俱乐部等级cell
 *
 * @export
 * @class UIClubLevelCell
 * @extends {UBaseView}
 */
@ccclass
@menu("okgame/UIClubLevelCell")
export default class UIClubLevelCell extends UBaseView {
    //#region 属性
    protected ui: UIClubLevelCell_auto = null;

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubLevelCell_auto(this.node);
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }
    //#endregion

    //#region 事件处理
    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    /**
     * 设置cell数据
     *
     * @memberof UIClubLevelCell
     */
    setCellData() {}

    //#endregion
}
