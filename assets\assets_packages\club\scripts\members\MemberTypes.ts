import { okpb } from "../../../okgame/proto/proto_msg";

/**
 * 成员模块类型定义文件
 * 用于存储成员模块中使用的所有接口和类型定义
 */

/**
 * 权限配置项接口
 */
export interface IPermissionConfig {
    featureName: string;
    icon: cc.SpriteFrame;
    value: string;
    showDialogPath: string;
}

/**
 * 权限列表配置参数接口
 */
export interface IPermissionListConfigOptions {
    identity: {
        myIdentity: okpb.Identity;
        targetUserIdentity: okpb.Identity;
    };
    values: {
        agentId: string;
        creditValue: string;
        downlineValue: string;
    };
    images: {
        agentCreditImg: cc.SpriteFrame;
        peopleImg: cc.SpriteFrame;
        lockImg: cc.SpriteFrame;
    };
}

/**
 * 权限配置与状态接口
 * 整合了权限配置信息和状态信息
 */
export interface IPermission {
    /** 权限名称（本地化后的显示名称） */
    name: string;
    /** 权限是否开启 */
    isOpen: boolean;
    /** 权限位值 */
    value?: number;
    /** 多语言键 */
    langKey?: string;
}

/**
 * 完整的俱乐部成员信息
 * 包含ClubUserPO和ClubAgentData的所有属性，以及额外的筹码和时间信息
 * 使用场景： 成员-tab成员和tab代理，柜台
 */
export interface IClubMemberFullInfo {
    // ========== ClubUserPO 的所有属性 ==========
    /** 成员UID */
    userId: number;
    /** 头像 */
    avatar: string;
    /** 显示名称 */
    nickname: string;
    /** 原名 */
    realname: string;
    /** 备注名 */
    mark: string;
    /** 签名 */
    signature: string;
    /** 玩家身份 */
    identity: okpb.Identity;
    /** 活跃时间 */
    activeTime: string;
    /** 免审额度(后续作为授信额度使用) */
    freeScore: number;
    /** 服务费 */
    serviceFee: number;
    /** 盈亏 */
    winLose: number;
    /** 手牌数 */
    handCount: number;
    /** 上次登陆 */
    lastLogin: number;
    /** 上次玩牌 */
    lastPlay: number;
    /** 代理Id(如果有代理上线) */
    agentId: number;
    /** 代理昵称(如果有代理上线) */
    agentName: string;
    /** 下线数量 */
    subCount: number;
    /** 管理权限(管理员和代理才有) */
    permissions: number;
    /** 备注详情 */
    markDetail: string;
    /** 玩家的俱乐部筹码 */
    chipBalance: number;
    /** 加入俱乐部的时间戳 */
    joinClubTime: number;

    // ========== ClubAgentData 的所有属性（可选，仅代理有） ==========
    /** 下级总筹码 */
    subBalance: number;
    /** 授信额度 */
    creditChips: number;
    /** 本周盈亏 */
    changeScoreThisWeek: number;
    /** 本周服务费 */
    serviceFeeThisWeek: number;
    /** 上周盈亏 */
    changeScoreLastWeek: number;
    /** 上周服务费 */
    serviceFeeLastWeek: number;
    /** 总盈亏 */
    changeScoreTotal: number;
    /** 总服务费 */
    serviceFeeTotal: number;
}

/**
 * 俱乐部信息
 * 俱乐部基本信息 + 最完整的成员信息
 */
export interface IClubInfoEntry {
    clubBaseInfo: IClubCompleteInfo;
    memberMap: Map<number, IClubMemberFullInfo>; // key: userId, value: 成员完整信息
}

/**
 * 完整的俱乐部信息
 * 结合了ClubResp的所有属性和RspGetClubUserList中的额外参数
 * 用于获取俱乐部详细信息时的完整数据结构
 */
export interface IClubCompleteInfo {
    // ========== ClubResp 的所有属性 ==========
    /** 俱乐部ID */
    clubId: number;
    /** 俱乐部图标 */
    icon: string;
    /** 俱乐部名称 */
    clubName: string;
    /** 俱乐部人数 */
    peopleNum: number;
    /** 俱乐部人数上限 */
    peopleLimit: number;
    /** 是否在俱乐部中 */
    isInClub: number;
    /** 正在进行的房间数 */
    aliveRoom: number;
    /** 玩家身份 */
    identity: okpb.Identity;
    /** 简介 */
    intro: string;
    /** 俱乐部名称修改次数 */
    clubNameModifyCount: number;
    /** 俱乐部名称修改消耗配置 */
    clubNameModifyCfgPo: okpb.ClubNameModifyCfgPo;
    /** 请求用户在俱乐部中的权限 */
    permissions: number;

    // ========== RspGetClubUserList 的额外参数 ==========
    /** 在线人员数量 */
    onlineCount: number;

    // ========== RspGetClubCounterInfo 的额外参数 ==========
    //群主-管理员   柜台信息
    ClubCounterInfo: okpb.ClubCounterInfo;
    //代理  柜台信息
    ClubAgentCounterInfo: okpb.ClubAgentCounterInfo;

    // ========== 俱乐部中个人信息 ==========
    ClubUserSelfInfo: okpb.ClubUserSelfInfo;
}

/**
 * 成员操作回调接口
 */
export interface IMemberOperationCallbacks {
    onMemberClick?: (member: IClubMemberFullInfo) => void;
    onMemberCheck?: (memberId: number, isChecked: boolean) => void;
    onMemberDelete?: (memberIds: number[]) => void;
    onRoleChange?: (memberId: number, newRole: okpb.Identity) => void;
    onPermissionChange?: (memberId: number, permissions: number) => void;
}

/**
 * 成员模块常量定义
 */
export class MemberTypeConstants {
    // 权限相关常量
    static readonly PERMISSION_ALL_ADMIN = 1023;
    static readonly PERMISSION_ALL_AGENT = 262;

    // 排序类型常量
    static readonly SORT_ASC = 1;
    static readonly SORT_DESC = 2;

    // 角色优先级常量
    static readonly ROLE_PRIORITY = {
        [okpb.Identity.IDE_MASTER]: 1,
        [okpb.Identity.IDE_MANAGER]: 2,
        [okpb.Identity.IDE_AGENT]: 3,
        [okpb.Identity.IDE_TABLE_OPERATOR]: 4,
        [okpb.Identity.IDE_GENERAL]: 5,
    };
}
