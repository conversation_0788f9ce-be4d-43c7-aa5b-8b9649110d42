import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UIClubInformView_auto from "./UIClubInformView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubInformView]";

/**
 * 俱乐部通知设置
 *
 * @export
 * @class UIClubInformView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubInformView")
export default class UIClubInformView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubInformView_auto = null;

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubInformView_auto(this.node);
        this.initEvent();
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubInformView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIClubInformView
     */
    private initEvent() {
        this.onRegisterEvent(this.ui.closedBtn, this.onClosedBtnClick.bind(this));
    }

    //#endregion

    //#region 事件处理
    onClosedBtnClick() {
        this.closeDialog();
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
