import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import ClubDataManager from "../../ClubDataManager";
import ItemPermissionView from "./ItemPermissionView";
import { PermissionUtils } from "../PermissionUtils";
import ClubAPI from "../../ClubAPI";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import { IClubCompleteInfo, IPermission } from "../MemberTypes";

const { ccclass, property } = cc._decorator;
const TAG = "[PermissionDialog]";

/**
 * 权限弹框
 */
@ccclass
export default class PermissionDialog extends UBaseDialog {
    @property(cc.Button)
    btnClose: cc.Button = null;

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Prefab)
    itemPermissionViewPrefab: cc.Prefab = null;

    private clubInfo: IClubCompleteInfo = null;
    private memberId: number = 0;

    /**
     * 原始权限位，用于计算变化
     */
    private originalPermissionBits: number = 0;
    /**
     * 权限列表
     */
    private permissionList: IPermission[] = [];

    onUILoad(): void {
        this.onRegisterEvent(this.btnClose.node, this.closeDialog.bind(this));
        this.clubInfo = ClubDataManager.getInstance().getClubInfo();
        // this.myUserId = OKGameData.getInstance().getUserId();
    }

    /**
     * 显示权限弹框
     * @param memberId 成员ID
     * @param permissions 权限位
     */
    onShow(memberId: number, permissions: number): void {
        this.memberId = memberId;
        this.originalPermissionBits = permissions;
        this.permissionList = PermissionUtils.parsePermissionsFromBits(permissions);
        console.log(TAG, "onShow() 权限列表:", this.permissionList, "用户id:", this.memberId, "原始权限位:", this.originalPermissionBits);

        this.refreshView();
    }

    /**
     * 刷新视图
     */
    private refreshView(): void {
        this.scrollView.content.removeAllChildren();
        this.permissionList.forEach((permission) => {
            const item = cc.instantiate(this.itemPermissionViewPrefab);
            const itemScript: ItemPermissionView = item.getComponent(ItemPermissionView);
            itemScript.setData(permission.name, permission.isOpen, this.onPermissionChange.bind(this, permission.name));
            console.log(TAG, "refreshView() 单个权限名称:", permission.name, "是否开启:", permission.isOpen);
            this.scrollView.content.addChild(item);
        });
    }

    /**
     * 权限变化监听
     * @param name 权限名称
     * @param isOpen 是否开启
     */
    private async onPermissionChange(operateName: string, isOpen: boolean): Promise<void> {
        console.log(TAG, "onPermissionChange() 权限变化:", operateName, ",开关状态：", isOpen);

        // 获取要操作的权限位
        const opBits = PermissionUtils.getPermissionBitByName(operateName);
        if (opBits === 0) {
            console.error(TAG, "onPermissionChange()无法获取权限位:", operateName + ",无法执行同步服务器操作");
            return;
        }

        console.log(TAG, "onPermissionChange() Req: 设置权限:", operateName, ",权限位:", opBits, ",开关:", isOpen);
        OKGameManager.getInstance().showLoading();
        try {
            const response = await ClubAPI.getInstance().ReqSetClubUserPermissions(this.clubInfo.clubId, this.memberId, opBits, isOpen);
            if (!cc.isValid(this.node)) {
                return;
            }
            if (response.errorCode === 0) {
                this.originalPermissionBits = response.permissions;
                this.permissionList = PermissionUtils.parsePermissionsFromBits(response.permissions);
                console.log(
                    TAG,
                    "onPermissionChange() Rsp: 同步权限到服务器成功，新的本地权限：",
                    this.permissionList,
                    ",原始权限位修改为：",
                    this.originalPermissionBits
                );
            } else {
                console.error(TAG, "onPermissionChange() 权限设置失败:", response.errMsg);
                OKGameManager.getInstance().showToastById("errorCode." + response.errorCode);
                // 遍历所有的itemView,执行backToLastStatus方法
                this.scrollView.content.children.forEach((child) => {
                    const itemScript: ItemPermissionView = child.getComponent(ItemPermissionView);
                    itemScript.backToLastStatus(operateName);
                });
            }
        } catch (error) {
            console.error(TAG, "onPermissionChange() 权限设置请求异常:", error.message);
            OKGameManager.getInstance().showToast(error.message);
        } finally {
            OKGameManager.getInstance().hideLoading();
        }
    }
}
