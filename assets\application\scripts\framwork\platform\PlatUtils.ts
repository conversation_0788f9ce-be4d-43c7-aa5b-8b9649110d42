import AdjustConfig from './AdjustConfig';
import PlatUtilsAnd from './and/PlatUtilsAnd';
import PlatUtilsJS from './js/PlatUtilsJS';
import { PhotoCallback } from './js/SelectPhotoJS';


class EventStack {
    private _callbacks: Function[] = [];

    on(cb) {
        this._callbacks.push(cb);
    }
    off(cb) {
        let i = this._callbacks.indexOf(cb);
        if (i >= 0) {
            this._callbacks.splice(i, 1);
        }
    }

    emit(...params: any[]) {
        let tempCb = this._callbacks.slice();
        for (let i = tempCb.length - 1; i >= 0; i--) {
            if (this._callbacks.indexOf(tempCb[i]) >= 0) {
                if (tempCb[i].apply(null, params)) {
                    return true;
                }
            }
        }
        return false;
    }
}

export interface PlatListener {
    init: Function;
    getAppConfig: Function;
    getDeviceInfo: Function;
    setSceneInfo?: Function;
    appUpdate?: Function,
    openPhotoAlbum: Function;
    openCamera: Function;
    savePhotoAlbum?: Function;
    setSysClipboardText: Function,
    getSysClipboardText: Function,
    getStartupParam: Function,
    reportByParam: Function,
}

export interface DeviceInfo {
    version: string;
    versionCode: string;
    packageName: string;
    appName: string;
    osType: number;
    osName: string;
    osVersion: string;
    mobileName: string;
    mobileProvision: string; //签名
    idfa: string;
    //---
    deviceId: string;
    timeZone: string;
}

export default class PlatUtils {
    private static _platListener: PlatListener = null;
    //--------------------------------
    private static _appConfig: any = null;
    private static _deviceInfo: DeviceInfo = null;
    private static _isDev: boolean = false;
    private static _lastCopyTime: number = 0;
    //--------------------------------
    public static backKeyListener = new EventStack();

    //======================================
    public static get platListener(): PlatListener {
        if (!this._platListener) {
            if (!cc.sys.isNative) {
                this._platListener = PlatUtilsJS;
            } else if (cc.sys.os == cc.sys.OS_ANDROID) {
                this._platListener = PlatUtilsAnd;
            } else if (cc.sys.os == cc.sys.OS_ANDROID) {

            }
        }
        return this._platListener;
    }

    /**
     * 配置信息
     */
    public static get appConfig(): any {
        if (!this._appConfig) {
            this._appConfig = this.platListener.getAppConfig();
            if (!this._appConfig) {
                this._isDev = true;
                this._appConfig = {};
            }
            console.log('AppConfig', JSON.stringify(this._appConfig));
        }
        return this._appConfig;
    }

    /**
     * 设备信息
     */
    public static get deviceInfo(): DeviceInfo {
        if (!this._deviceInfo) {
            this._deviceInfo = this.platListener.getDeviceInfo();
            this._deviceInfo.osType = PlatUtilsJS.osType;
            this._deviceInfo.osName = PlatUtilsJS.osName;
            if (!this._deviceInfo.idfa) {
                this._deviceInfo.idfa = '';
            }
        }
        return this._deviceInfo;
    }

    /**
     * 地址栏配置
     */
    public static get queryConfig(): any {
        if (cc.sys.isNative) {
            return {}
        }
        return PlatUtilsJS.getQueryConfig();
    }

    /**
     * 是否开发版本
     */
    public static get isDev(): boolean {
        if (cc.sys.isNative) {
            return false;
        }
        return this._isDev;
    }

    /**
     * 是否模拟器
     */
    public static get isSimulator(): boolean {
        return false;
    }

    //*********************公共平台接口********************/
    /**
     * 获取当前系统语言
     */
    public static getSysLanguage(): string {
        let lang = cc.sys.languageCode.toLowerCase();
        var reg = /[^A-Za-z]/;
        let arr = lang.split(reg, 1);
        if (arr.length > 0) {
            return arr[0];
        }
        return lang;
    }

    /**
     * 获取应用信息
     */
    public static getAppConfigValue(key: string) {
        return this.appConfig[key];
    }

    //*********************分平台接口********************/
    /**
     * 初始化
     */
    public static init() {
        cc.systemEvent.on(
            cc.SystemEvent.EventType.KEY_DOWN,
            (event: cc.Event.EventKeyboard) => {
                console.log('onKeyDown=' + event.keyCode);
                switch (event.keyCode) {
                    case cc.macro.KEY.back:
                    case cc.macro.KEY.escape:
                        this.backKeyListener.emit()
                        break;
                }
            },
            this
        );
        //---
        this.platListener.init(this);
        //初始化应用配置
        this._appConfig = this.appConfig;
    }
    /**
     * 设备信息
     */
    public static getDeviceInfo(): DeviceInfo {
        if (!this._deviceInfo) {
            this._deviceInfo = this.platListener.getDeviceInfo();
            this._deviceInfo.osType = PlatUtilsJS.osType;
            this._deviceInfo.osName = PlatUtilsJS.osName;
            if (!this._deviceInfo.idfa) {
                this._deviceInfo.idfa = '';
            }
        }
        return this._deviceInfo;
    }

    /**
     * 更新
     */
    public static appUpdate(param) {
        this.platListener.appUpdate && this.platListener.appUpdate(param)
    }

    /**
     * 打开相册
     * @param limitSize 限制宽高,0为不限制
     * @param cb
     */
    public static openPhotoAlbum(limitSize: number, cb: PhotoCallback) {
        this.platListener.openPhotoAlbum(limitSize, cb);
    }

    /**
     * 打开摄像机
     * @param cb
     * @param limitSize 限制大小
     */
    public static openCamera(limitSize: number, cb: PhotoCallback) {
        this.platListener.openCamera(limitSize, cb);
    }

    /**
     * 保存相册
     */
    public static savePhotoAlbum(path: string, cb: Function = null) {
        this.platListener.savePhotoAlbum(path, cb)
    }

    /**
     * 设置场景信息
     * @param orientationH  屏幕方向
     * @param navigationBar 状态栏
     */
    public static setSceneInfo(orientation: number, navigationBar: boolean) {
        if (!cc.sys.isMobile) return;
        let frameSize = cc.view.getFrameSize();
        //设置横竖屏
        cc.view.setOrientation(orientation);

        if (orientation == cc.macro.ORIENTATION_PORTRAIT) {
            if (frameSize.width > frameSize.height) {
                cc.view.setFrameSize(frameSize.height, frameSize.width);
            }
            let designResolution = cc.Canvas.instance.designResolution;
            if (designResolution.width > designResolution.height) {
                cc.Canvas.instance.designResolution = cc.size(designResolution.height, designResolution.width);
            }
        } else if (orientation == cc.macro.ORIENTATION_LANDSCAPE) {
            if (frameSize.width < frameSize.height) {
                cc.view.setFrameSize(frameSize.height, frameSize.width);
            }
            let designResolution = cc.Canvas.instance.designResolution;
            if (designResolution.width < designResolution.height) {
                cc.Canvas.instance.designResolution = cc.size(designResolution.height, designResolution.width);
            }
        }
        //原生函数
        this.platListener.setSceneInfo && this.platListener.setSceneInfo(orientation, navigationBar);
    }

    /**
     * copy 复制文字
     * @param text 
     * @param callback 
     * @returns 
     */
    public static setSysClipboardText(text: string, cb?: (code: number) => void) {
        let curTime = (new Date()).getTime();
        if (curTime - this._lastCopyTime < 1000) {
            return;
        }
        this._lastCopyTime = curTime;
        //--------
        this.platListener.setSysClipboardText(text, cb)
    }

    /**
     * 获取剪贴板文字
     */
    public static getSysClipboardText(cb: (text: string) => void) {
        this.platListener.getSysClipboardText(cb);
    }

    /**
     * 获取启动参数
     */
    public static getStartupParam(): any {
        return this.platListener.getStartupParam()
    }

    /**
     * 上报
     */
    public static report(key: string) {
        this.reportByParam({ key })
    }

    /**
     * 带参数的上报
     */
    public static reportByParam(param: { key: string, adKey?: string }) {
        let adKey = AdjustConfig.getAdKey(param.key);
        if (adKey) {
            param.adKey = adKey
        }
        console.log('report', param)
        this.platListener.reportByParam(param)
    }

}
