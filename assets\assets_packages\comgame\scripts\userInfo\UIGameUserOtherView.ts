import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import OKExtManager from "../../../okgame/public/OKExtManager";
import ComGameWs from "../../network/ComGameWs";
import UIGameUserOtherView_auto from "./UIGameUserOtherView_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIGameUserOtherView extends UBaseDialog {
	private ui: UIGameUserOtherView_auto = null;
	private _userId: number = 0;


	onUILoad(): void {
		this.ui = new UIGameUserOtherView_auto(this.node);
		this.initPropView();
	}

	initPropView() {
		this.ui.propItem.active = false;
		for (let i = 1; i <= 15; i++) {
			let id = i < 10 ? '0' + i : '' + i;
			let item = cc.instantiate(this.ui.propItem)
			item.parent = this.ui.contentNode;
			item.active = true;
			//---
			let icon = item.getChildByName(this.ui.propIcon.name).getComponent(cc.Sprite);
			OKExtManager.getInstance().loadEachPropIcon(id, (err: Error, spriteFrame: cc.SpriteFrame) => {
				if (!cc.isValid(item)) {
					return;
				}
				icon.spriteFrame = spriteFrame;
			})
			this.onRegisterEvent(item, () => {
				ComGameWs.getInstance().sendEachProp(id, this._userId);
				this.closeDialog();
			})
		}
	}

	onShow(userId: number) {
		this._userId = userId;
	}
}