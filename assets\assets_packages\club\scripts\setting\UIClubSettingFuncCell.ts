import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseView from "../../../../framwork/widget/UBaseView";
import UIconSprite from "../../../../framwork/widget/UIconSprite";
import ClubManger from "../ClubManger";
import UIClubSettingFuncCell_auto from "./UIClubSettingFuncCell_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubSettingFuncCell]";

export interface IFuncCellData {
    title: string;
    viewPath: string;
}

/**
 * 俱乐部设置-功能单元格
 *
 * @export
 * @class UIClubSettingFuncCell
 * @extends {UBaseView}
 */
@ccclass
@menu("okgame/UIClubSettingFuncCell")
export default class UIClubSettingFuncCell extends UBaseView {
    //#region 属性
    protected ui: UIClubSettingFuncCell_auto = null;

    @property({ type: UIconSprite, tooltip: "图标" })
    private cellIcon: UIconSprite = null;

    @property({ type: [cc.SpriteFrame], tooltip: "图片数组" })
    private icons: cc.SpriteFrame[] = [];

    /**
     * 视图路径
     *
     * @private
     * @type {string}
     * @memberof UIClubSettingFuncCell
     */
    private viewPath: string = null;

    /**
     * 点击回调
     *
     * @private
     * @memberof UIClubSettingFuncCell
     */
    private onClickCb: (viewPath: string) => void = null;

    //#endregion

    //#region 生命周期
    onUILoad(): void {
        this.ui = new UIClubSettingFuncCell_auto(this.node);
        this.onRegisterEvent(this.ui.node, this.onClickCell.bind(this));
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubSettingFuncCell
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }
    //#endregion

    //#region 事件处理
    /**
     * 点击单元格
     *
     * @private
     * @memberof UIClubSettingFuncCell
     */
    private onClickCell() {
        this.onClickCb?.(this.viewPath);
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    /**
     * 设置单元格数据
     *
     * @param {IFuncCellData} data
     * @memberof UIClubSettingFuncCell
     */
    public setCellData(index: number, data: IFuncCellData, onClickCb: (viewPath: string) => void) {
        this.ui.cellTitle.getComponent(cc.Label).string = ULanguage.getInstance().getLangByID(data.title);
        this.cellIcon.setSpriteFrame(this.icons[index]);
        this.viewPath = data.viewPath;
        this.onClickCb = onClickCb;
    }

    //#endregion
}
