import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import UIClubRoundDataView_auto from "./UIClubRoundDataView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubRoundDataView extends UBaseSFixedDialog {

	protected ui: UIClubRoundDataView_auto = null;

	onUILoad(): void {
		this.ui = new UIClubRoundDataView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
	}

	updateLabel() {
		this.ui.label.getComponent(cc.Label).string = "";
		this.ui.labelID.getComponent(cc.Label).string = "";
		this.ui.labelTime.getComponent(cc.Label).string = "";
		this.ui.labelNLH.getComponent(cc.Label).string = "";
		this.ui.labelChip.getComponent(cc.Label).string = "";
		this.ui.labelGold.getComponent(cc.Label).string = "";
		this.ui.labelTotalTime.getComponent(cc.Label).string = "";

		this.ui.labelBuy.getComponent(cc.Label).string = "";
		this.ui.labelServiceFee.getComponent(cc.Label).string = "";
		this.ui.labelInsurance.getComponent(cc.Label).string = "";
	}
}