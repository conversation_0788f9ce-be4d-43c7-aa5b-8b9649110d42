import UEventHandle from "../../../framwork/utils/UEventHandle";
import { okpb } from "../../okgame/proto/proto_msg";
import ClubAPI from "./ClubAPI";
import { CLUB_EVENT } from "./ClubManger";
import { IClubCompleteInfo, IClubInfoEntry, IClubMemberFullInfo } from "./members/MemberTypes";

const TAG = "[ClubDataManager]";

/**
 * 俱乐部数据管理器
 *
 * 职责：
 * - 管理俱乐部基本信息缓存
 * - 管理俱乐部成员信息缓存（使用Map结构提供O(1)查找性能）
 * - 提供统一的数据接口和数据整合功能
 * - 支持观察者模式的数据更新通知
 *
 * 柜台使用：
 * - 1. 首先获得缓存的成员信息getCurClubMembersFullInfo
 * - 2. 如果没有成员，则执行:1. getCompleteClubMemberData获得所有成员信息，数据包含最新的筹码信息，直接使用数据即可
 * - 3. 如果1.有成员，则执行请求服务器的最新筹码信息，之后调用updateChipBalanceList刷新缓存，然后调用getCurClubMembersFullInfo获取成员信息
 *
 */
export default class ClubDataManager extends UEventHandle {
    private static instance: ClubDataManager | null = null;

    /** 当前进入的俱乐部ID */
    private curClubId: number = 0;

    /** 俱乐部信息缓存：key=clubId, value=俱乐部完整信息 */
    private readonly clubInfoCache: Map<number, IClubInfoEntry> = new Map();

    /** 数据更新订阅者列表 */
    private readonly updateCallbacks: Array<(clubInfo: IClubInfoEntry) => void> = [];

    /** 防重复请求标志 */
    private isRequesting: boolean = false;

    /** 成员数据请求订阅者列表 */
    private readonly memberDataSubscribers: Array<{
        clubId: number;
        callback: (memberList: IClubMemberFullInfo[] | null) => void;
    }> = [];

    private constructor() {
        super();
        console.log(TAG, "ClubDataManager 实例已创建");
    }

    /**
     * 获取单例实例
     * @returns ClubDataManager实例
     */
    public static getInstance(): ClubDataManager {
        if (!ClubDataManager.instance) {
            ClubDataManager.instance = new ClubDataManager();
        }
        return ClubDataManager.instance;
    }

    // ==================== 俱乐部信息管理 ====================

    /**
     * 设置当前俱乐部ID
     * @param clubId 俱乐部ID
     */
    public setClubId(clubId: number): void {
        if (clubId <= 0) {
            console.warn(TAG, "无效的俱乐部ID:", clubId);
            return;
        }

        this.curClubId = clubId;
        console.log(TAG, "当前俱乐部ID已设置为:", clubId);
    }

    /**
     * 获取当前俱乐部ID
     * @returns 当前俱乐部ID
     */
    public getClubId(): number {
        return this.curClubId;
    }

    /**
     * 缓存单个俱乐部基本信息
     * @param clubInfo 俱乐部信息
     * @description 用于进入俱乐部大厅时缓存基本信息
     */
    public cacheClubInfo(clubInfo: okpb.ClubResp): void {
        if (!clubInfo || !clubInfo.clubId) {
            console.warn(TAG, "俱乐部信息无效，无俱乐部id");
            return;
        }
        const clubEntry: IClubInfoEntry = {
            clubBaseInfo: this.createClubCompleteInfo(clubInfo),
            memberMap: new Map(),
        };

        this.clubInfoCache.set(clubInfo.clubId, clubEntry);
        console.log(TAG, "俱乐部信息已缓存,clubId:", clubInfo.clubId, "clubName:", clubInfo.clubName);

        this.notifyUpdate(clubEntry);
    }

    /**
     * 将clubInfo: okpb.ClubResp转成IClubCompleteInfo
     * @param clubInfo 俱乐部信息
     * @returns 完整的俱乐部信息
     */
    private createClubCompleteInfo(clubInfo: okpb.ClubResp): IClubCompleteInfo {
        return {
            clubId: clubInfo.clubId,
            clubName: clubInfo.clubName,
            peopleNum: clubInfo.peopleNum,
            peopleLimit: clubInfo.peopleLimit,
            isInClub: clubInfo.isInClub,
            aliveRoom: clubInfo.aliveRoom,
            identity: clubInfo.identity,
            intro: clubInfo.intro,
            clubNameModifyCount: clubInfo.clubNameModifyCount,
            clubNameModifyCfgPo: clubInfo.clubNameModifyCfgPo,
            permissions: clubInfo.permissions,
            onlineCount: 0,
            icon: clubInfo.icon,
            ClubCounterInfo: null,
            ClubAgentCounterInfo: null,
            ClubUserSelfInfo: null,
        };
    }

    /**
     * 批量缓存俱乐部列表信息
     * @param clubList 俱乐部列表
     * @deprecated 因为bundle依赖问题，暂时不推荐使用
     */
    public cacheClubInfoList(clubList: okpb.ClubResp[]): void {
        if (!Array.isArray(clubList) || clubList.length === 0) {
            console.warn(TAG, "俱乐部列表为空");
            return;
        }

        console.log(TAG, "开始批量缓存俱乐部列表，数量:", clubList.length);
        clubList.forEach((clubInfo) => {
            if (clubInfo?.clubId) {
                this.cacheClubInfo(clubInfo);
            }
        });
    }

    /**
     * 获取当前俱乐部基本信息
     * @returns 俱乐部基本信息，不含成员数据
     */
    public getClubInfo(): IClubCompleteInfo | null {
        const clubDetail = this.getClubInfoWithMembers();
        return clubDetail?.clubBaseInfo || null;
    }

    /**
     * 根据ID获取俱乐部基本信息
     * @param clubId 俱乐部ID
     * @returns 俱乐部基本信息，不含成员数据
     */
    public getClubInfoById(clubId: number): IClubCompleteInfo | null {
        const clubEntry = this.clubInfoCache.get(clubId);
        return clubEntry?.clubBaseInfo || null;
    }

    /**
     * 获取当前俱乐部的详细信息
     * @returns 包含基本信息和成员数据的完整信息
     */
    public getClubInfoWithMembers(): IClubInfoEntry | null {
        if (this.curClubId <= 0) {
            console.warn(TAG, "当前俱乐部ID无效:", this.curClubId);
            return null;
        }
        return this.clubInfoCache.get(this.curClubId) || null;
    }

    /**
     * 根据ID获取俱乐部详细信息
     * @param clubId 俱乐部ID
     * @returns 包含基本信息和成员数据的完整信息
     */
    public getClubInfoWithMembersById(clubId: number): IClubInfoEntry | null {
        return this.clubInfoCache.get(clubId) || null;
    }

    /**
     * 更新俱乐部信息
     * @param clubInfo 俱乐部信息
     */
    public updateClubInfo(clubInfo: IClubCompleteInfo): void {
        const clubEntry = this.clubInfoCache.get(clubInfo.clubId);
        if (clubEntry) {
            clubEntry.clubBaseInfo = clubInfo;
            this.notifyUpdate(clubEntry);
        }
    }

    // ==================== 成员信息管理 ====================

    /**
     * 获取完整的成员数据（整合成员列表和代理列表）
     * @param clubId 俱乐部ID，不传则使用当前俱乐部ID
     * @param callback 回调函数，返回整合后的完整成员数据
     * @description
     * - 并行请求成员列表、代理列表和成员备注列表
     * - 筹码信息已包含在成员列表接口中
     * - 自动整合代理特有数据到基础成员信息中
     * - 支持订阅模式，多个同时调用时会统一处理并通知所有订阅者
     */
    public requestMembersAndAgetnAndMark(clubId?: number, callback?: (memberList: IClubMemberFullInfo[] | null) => void): void {
        const targetClubId = clubId || this.curClubId;

        if (!this.validateClubId(targetClubId)) {
            callback?.(null);
            return;
        }

        // 添加到订阅者列表
        if (callback) {
            this.memberDataSubscribers.push({
                clubId: targetClubId,
                callback: callback,
            });
            console.log(TAG, `已添加成员数据订阅者，当前订阅者数量: ${this.memberDataSubscribers.length}`);
        }

        // 如果正在请求中，不需要重复发起请求，直接返回（订阅者已添加）
        if (this.isRequesting) {
            console.warn(TAG, "正在请求中，已将回调添加到订阅者列表");
            return;
        }

        this.isRequesting = true;
        console.log(TAG, "开始获取完整成员数据，俱乐部ID:", targetClubId);

        // API请求状态管理
        const requestState = {
            memberListData: null as okpb.RspGetClubUserList | null,
            agentListData: null as okpb.RspGetClubAgentList | null,
            memberRemarkData: null as okpb.RspClubUserRemarkList | null,
            errorCount: 0,
            completedCount: 0,
            totalRequests: 3, // 现在有3个请求：成员列表、代理列表、成员备注列表
            memberListSuccess: false,
        };

        // 请求完成检查器
        const checkAllCompleted = () => {
            requestState.completedCount++;
            console.log(TAG, `请求完成进度: ${requestState.completedCount}/${requestState.totalRequests}`);

            if (requestState.completedCount === requestState.totalRequests) {
                this.isRequesting = false;

                let result: IClubMemberFullInfo[] | null = null;

                if (requestState.errorCount > 0) {
                    console.error(TAG, `获取完整成员数据失败，错误数量: ${requestState.errorCount}`);
                } else {
                    try {
                        // 先整合成员列表和代理列表数据
                        this.integrateCompleteData(targetClubId, requestState.memberListData, requestState.agentListData);

                        // 然后应用成员备注数据
                        if (requestState.memberRemarkData) {
                            console.log(TAG, "开始应用成员备注数据");
                            this.updateMemberRemarkList(targetClubId, requestState.memberRemarkData);
                        }

                        result = this.getAllMemberListByClubId(targetClubId);
                        console.log(TAG, "获取完整成员数据成功，成员数量:", result.length);
                    } catch (error) {
                        console.error(TAG, "整合数据时发生错误:", error);
                        result = null;
                    }
                }

                // 通知所有订阅者
                this.notifyMemberDataSubscribers(targetClubId, result);
            }
        };

        // 1. 获取成员列表
        this.requestMemberList(targetClubId, requestState, checkAllCompleted);

        // 2. 获取代理列表
        this.requestAgentList(targetClubId, requestState, checkAllCompleted);

        // 3. 获取成员备注列表
        this.requestMemberRemarkListParallel(targetClubId, requestState, checkAllCompleted);
    }

    /**
     * 通用成员列表请求方法
     * @param clubId 俱乐部ID
     * @param onSuccess 成功处理回调
     * @param onError 错误处理回调
     * @param onComplete 完成回调
     */
    private requestMemberListCommon(
        clubId: number,
        onSuccess: (res: okpb.RspGetClubUserList) => void,
        onError: (err: any) => void,
        onComplete: () => void
    ): void {
        ClubAPI.getInstance()
            .ReqGetClubMemberList(clubId)
            .then((res: okpb.RspGetClubUserList) => {
                if (res.errorCode === 0) {
                    console.log(TAG, "成员列表请求成功，原始的返回:", res);
                    console.log(TAG, "成员列表请求成功，成员数量:", this.extractUserCount(res));
                    onSuccess(res);
                } else {
                    console.error(TAG, "成员列表请求失败，错误码:", res.errorCode);
                    onError(new Error(`API错误码: ${res.errorCode}`));
                }
            })
            .catch((err: any) => {
                console.error(TAG, "成员列表请求异常:", err);
                onError(err);
            })
            .finally(onComplete);
    }

    /**
     * 请求成员列表（用于并行请求场景）
     * @description 我的角色为创建者或者管理员
     */
    private requestMemberList(
        clubId: number,
        state: {
            memberListData: okpb.RspGetClubUserList | null;
            agentListData: okpb.RspGetClubAgentList | null;
            memberRemarkData: okpb.RspClubUserRemarkList | null;
            errorCount: number;
            memberListSuccess: boolean;
        },
        onComplete: () => void
    ): void {
        this.requestMemberListCommon(
            clubId,
            (res) => {
                state.memberListData = res;
                state.memberListSuccess = true; // 标记成员列表请求成功
            },
            () => {
                state.errorCount++;
                state.memberListSuccess = false; // 标记成员列表请求失败
            },
            onComplete
        );
    }

    /**
     * 处理成员信息数据
     */
    private handleRspMemberList(clubId: number, rsp: okpb.RspGetClubUserList): void {
        let resultMemberList: IClubMemberFullInfo[] = [];
        // console.log(TAG, "handleData() 处理数据", JSON.stringify(rsp));
        if (rsp?.errorCode === 0) {
            const rspMap = rsp.data.clubUserMap;
            if (rspMap) {
                const allUsers: IClubMemberFullInfo[] = [];
                for (const key in rspMap) {
                    if (rspMap.hasOwnProperty(key)) {
                        allUsers.push(...(rspMap[key].users ?? []).map((user) => ClubDataManager.getInstance().createMemberInfoWithAgent(user)));
                    }
                }
                resultMemberList = allUsers;
                console.log(TAG, "处理后的成员列表:", resultMemberList);
            }
            // 更新缓存的俱乐部信息的身份、人数、权限
            const clubInfo = ClubDataManager.getInstance().getClubInfoById(clubId);
            if (clubInfo) {
                clubInfo.identity = rsp.identity;
                clubInfo.onlineCount = rsp.onlineCount;
                clubInfo.permissions = rsp.permissions;
                ClubDataManager.getInstance().updateClubInfo(clubInfo);
            } else {
                console.error(TAG, "更新俱乐部信息失败,有大问题，当前俱乐部信息为空");
            }

            // 将resultMemberList缓存到内存中
            this.setMemberListByClubId(clubId, resultMemberList);
        } else {
            // console.error(TAG, "获取成员列表失败:", rsp);
        }
    }

    /**
     * 请求代理列表
     */
    private requestAgentList(
        clubId: number,
        state: {
            memberListData: okpb.RspGetClubUserList | null;
            agentListData: okpb.RspGetClubAgentList | null;
            memberRemarkData: okpb.RspClubUserRemarkList | null;
            errorCount: number;
        },
        onComplete: () => void
    ): void {
        ClubAPI.getInstance()
            .ReqGetAgentMemberList(clubId)
            .then((res: okpb.RspGetClubAgentList) => {
                if (res.errorCode === 0) {
                    console.log(TAG, "代理列表请求成功，代理数量:", res.data?.length || 0);
                    state.agentListData = res;
                } else {
                    console.error(TAG, "代理列表请求失败，错误码:", res.errorCode);
                    state.errorCount++;
                }
            })
            .catch((err: any) => {
                console.error(TAG, "代理列表请求异常:", err);
                state.errorCount++;
            })
            .finally(onComplete);
    }

    /**
     * 请求成员备注列表（用于并行请求场景）
     */
    private requestMemberRemarkListParallel(
        clubId: number,
        state: {
            memberListData: okpb.RspGetClubUserList | null;
            agentListData: okpb.RspGetClubAgentList | null;
            memberRemarkData: okpb.RspClubUserRemarkList | null;
            errorCount: number;
        },
        onComplete: () => void
    ): void {
        console.log(TAG, "并行请求成员备注列表:", clubId);
        ClubAPI.getInstance()
            .ReqGetClubUserRemarkList(clubId)
            .then((data: okpb.RspClubUserRemarkList) => {
                if (data.errorCode === okpb.ERET.OK) {
                    console.log(TAG, "成员备注列表请求成功，备注数量:", data.remarkList?.length || 0);
                    state.memberRemarkData = data;
                } else {
                    console.error(TAG, "成员备注列表请求失败，错误码:", data.errorCode);
                    state.errorCount++;
                }
            })
            .catch((error: any) => {
                console.error(TAG, "成员备注列表请求异常:", error);
                state.errorCount++;
            })
            .finally(onComplete);
    }

    /**
     * 获得代理的下级成员列表
     * @description 我的角色为代理
     * @param clubId 俱乐部ID
     */
    public getAgentSubMemberList(clubId: number, myUserId: number, onComplete: () => void) {
        ClubAPI.getInstance()
            .ReqGetClubAgentSubList(clubId, myUserId)
            .then((res: okpb.RspGetClubAgentSubList) => {
                if (res.errorCode === okpb.ERET.OK) {
                    console.log(TAG, "代理列表请求成功，代理数量:", res.subList?.users?.length || 0);
                    if (res.subList?.users) {
                        res.subList.users.forEach((user) => {
                            this.setMemberInfoByClubId(clubId, this.createMemberInfoWithAgent(user));
                        });
                    }
                } else {
                    console.error(TAG, "代理列表请求失败，错误码:", res.errorCode);
                }
            })
            .catch((err: any) => {
                console.error(TAG, "代理列表请求异常:", err);
            })
            .finally(onComplete);
    }

    /**
     * 将成员列表缓存到内存中
     * @param clubId 俱乐部ID
     * @param rsp 成员列表响应
     */
    private toCacheMemberList(clubId: number, rsp: okpb.RspGetClubUserList): void {
        if (!rsp.data?.clubUserMap) {
            console.warn(TAG, "成员列表数据为空");
            return;
        }

        const allUsers: okpb.ClubUserPO[] = [];

        // 提取所有成员数据
        Object.keys(rsp.data.clubUserMap).forEach((key) => {
            const userList = rsp.data.clubUserMap[key];
            if (userList?.users) {
                allUsers.push(...userList.users);
            }
        });

        console.log(TAG, "开始缓存成员列表，成员数量:", allUsers.length);

        // 转换并缓存成员信息
        allUsers.forEach((user) => {
            const memberFullInfo = this.createMemberInfoWithAgent(user);
            this.setMemberInfoByClubId(clubId, memberFullInfo);
        });
    }

    /**
     * 整合成员列表和代理列表数据
     * @param clubId 俱乐部ID
     * @param memberListData 成员列表数据
     * @param agentListData 代理列表数据
     */
    private integrateCompleteData(
        clubId: number,
        memberListData: okpb.RspGetClubUserList | null,
        agentListData: okpb.RspGetClubAgentList | null
    ): void {
        console.log(TAG, "开始整合完整数据...");

        // 清空现有缓存
        this.clearClubMembersCache(clubId);

        // 1. 处理成员基础数据
        const baseMembers = this.processMemberData(memberListData);
        console.log(TAG, "基础成员数据处理完成，数量:", baseMembers.size);

        // 2. 整合代理数据
        this.integrateAgentData(baseMembers, agentListData);

        // 3. 保存到缓存
        this.saveMembersToCache(clubId, baseMembers);

        console.log(TAG, "完整数据整合完成，最终成员数量:", this.getMemberList().length);
        console.log(TAG, "这个俱乐部的所有数据:" + JSON.stringify(this.getMemberList()));
    }

    /**
     * 处理成员基础数据
     */
    private processMemberData(memberListData: okpb.RspGetClubUserList | null): Map<number, IClubMemberFullInfo> {
        const baseMembers = new Map<number, IClubMemberFullInfo>();

        if (!memberListData?.data?.clubUserMap) {
            return baseMembers;
        }

        Object.keys(memberListData.data.clubUserMap).forEach((key) => {
            const userList = memberListData.data.clubUserMap[key];
            userList?.users?.forEach((user) => {
                const memberFullInfo = this.createMemberInfoWithAgent(user);
                baseMembers.set(user.userId, memberFullInfo);
            });
        });

        return baseMembers;
    }

    /**
     * 整合代理数据到成员信息中
     */
    private integrateAgentData(baseMembers: Map<number, IClubMemberFullInfo>, agentListData: okpb.RspGetClubAgentList | null): void {
        if (!agentListData?.data) {
            console.log(TAG, "无代理数据需要整合");
            return;
        }

        let integratedCount = 0;
        agentListData.data.forEach((agentData) => {
            const memberInfo = baseMembers.get(agentData.userId);
            if (memberInfo) {
                this.updateMemberWithAgentData(memberInfo, agentData);
                integratedCount++;
            } else {
                console.warn(TAG, `代理用户${agentData.userId}在成员列表中不存在`);
            }
        });

        console.log(TAG, `代理数据整合完成，整合数量: ${integratedCount}/${agentListData.data.length}`);
    }

    /**
     * 更新成员信息的代理数据
     */
    private updateMemberWithAgentData(memberInfo: IClubMemberFullInfo, agentData: okpb.ClubAgentData): void {
        memberInfo.subCount = agentData.subNum;
        memberInfo.subBalance = agentData.subBalance;
        memberInfo.creditChips = agentData.creditChips;
        memberInfo.changeScoreThisWeek = agentData.changeScoreThisWeek;
        memberInfo.serviceFeeThisWeek = agentData.serviceFeeThisWeek;
        memberInfo.changeScoreLastWeek = agentData.changeScoreLastWeek;
        memberInfo.serviceFeeLastWeek = agentData.serviceFeeLastWeek;
        memberInfo.changeScoreTotal = agentData.changeScoreTotal;
        memberInfo.serviceFeeTotal = agentData.serviceFeeTotal;
    }

    /**
     * 将成员数据保存到缓存
     */
    private saveMembersToCache(clubId: number, members: Map<number, IClubMemberFullInfo>): void {
        members.forEach((memberFullInfo) => {
            this.setMemberInfoByClubId(clubId, memberFullInfo);
        });
    }

    /**
     * 创建完整的俱乐部成员信息
     * @param user 成员基础信息
     * @param agentData 代理信息（可选）
     * @returns 完整的成员信息对象
     * @description
     * 将服务器返回的原始数据转换为统一的IClubMemberFullInfo格式
     * 适用于成员列表、代理列表、柜台模块等多个场景
     */
    public createMemberInfoWithAgent(user: okpb.ClubUserPO, agentData?: okpb.ClubAgentData): IClubMemberFullInfo {
        const fullInfo: IClubMemberFullInfo = {
            userId: user.userId,
            nickname: user.nickname,
            avatar: user.avatar,
            realname: user.realname,
            mark: user.mark,
            signature: user.signature,

            identity: user.identity,
            permissions: user.permissions,
            agentId: user.agentId,
            agentName: user.agentName,

            freeScore: user.freeScore,
            serviceFee: user.serviceFee + this.getRandomData(),
            winLose: user.winLose + this.getRandomSignData(),
            handCount: user.handCount + this.getRandomData(),
            subCount: user.subCount,

            activeTime: user.activeTime,
            lastLogin: user.lastLogin,
            lastPlay: user.lastPlay,
            joinClubTime: user.joinClubTime,

            // === 筹码信息 ===
            chipBalance: user.chipBalance,
            creditChips: 0, // 默认值，代理时会被覆盖

            // === 备注信息 ===
            markDetail: user.markDetail,

            // === 代理数据（默认值，代理时会被覆盖） ===
            subBalance: 0,
            changeScoreThisWeek: 0,
            serviceFeeThisWeek: 0,
            changeScoreLastWeek: 0,
            serviceFeeLastWeek: 0,
            changeScoreTotal: 0,
            serviceFeeTotal: 0,
        };

        // 如果提供了代理数据，则覆盖默认值
        if (agentData) {
            this.updateMemberWithAgentData(fullInfo, agentData);
        }

        return fullInfo;
    }

    /**
     *
     * @returns TODO: 测试数据,后面要删除
     */
    private getRandomData() {
        // 生成一个1000到5000的随机数
        const randomNum = Math.floor(Math.random() * 4000) + 1000;
        return randomNum;
    }

    /**
     *
     * @returns TODO: 测试数据,后面要删除
     */
    private getRandomSignData() {
        const randomNum = Math.floor(Math.random() * 2000) - 1000;
        return randomNum;
    }

    /**
     * 设置当前俱乐部的成员信息
     * @param memberFullInfo 完整成员信息
     */
    public setMemberInfo(memberFullInfo: IClubMemberFullInfo): void {
        this.setMemberInfoByClubId(this.curClubId, memberFullInfo);
    }

    /**
     * 设置指定俱乐部的单个成员信息
     * @param clubId 俱乐部ID
     * @param memberFullInfo 完整成员信息
     */
    public setMemberInfoByClubId(clubId: number, memberFullInfo: IClubMemberFullInfo): void {
        if (!this.clubInfoCache.has(clubId)) {
            this.clubInfoCache.set(clubId, {
                clubBaseInfo: null,
                memberMap: new Map(),
            });
        }

        const clubEntry = this.clubInfoCache.get(clubId)!;
        clubEntry.memberMap.set(memberFullInfo.userId, memberFullInfo);
    }

    /**
     * 批量设置成员信息
     * @param clubId 俱乐部ID
     * @param memberList 成员列表
     */
    public setMemberListByClubId(clubId: number, memberList: IClubMemberFullInfo[]): void {
        if (!Array.isArray(memberList) || memberList.length === 0) {
            console.warn(TAG, "成员列表为空");
            return;
        }

        console.log(TAG, `开始批量设置成员信息，俱乐部ID: ${clubId}，成员数量: ${memberList.length}`);
        memberList.forEach((memberFullInfo) => {
            this.setMemberInfoByClubId(clubId, memberFullInfo);
        });
    }

    /**
     * 获取当前俱乐部的单个成员信息
     * @param userId 用户ID
     * @returns 完整成员信息或null
     */
    public getMemberInfoById(userId: number): IClubMemberFullInfo | null {
        return this.getMemberInfoByClubIdAndUserId(this.curClubId, userId);
    }

    /**
     * 获取指定俱乐部的单个成员信息
     * @param clubId 俱乐部ID
     * @param userId 用户ID
     * @returns 完整成员信息或null
     */
    public getMemberInfoByClubIdAndUserId(clubId: number, userId: number): IClubMemberFullInfo | null {
        const clubEntry = this.clubInfoCache.get(clubId);
        return clubEntry?.memberMap.get(userId) || null;
    }

    /**
     * 获取指定俱乐部的所有成员信息
     * @param clubId 俱乐部ID
     * @returns 成员信息数组
     */
    public getAllMemberListByClubId(clubId: number): IClubMemberFullInfo[] {
        const clubEntry = this.clubInfoCache.get(clubId);
        return clubEntry ? Array.from(clubEntry.memberMap.values()) : [];
    }

    /**
     * 获取当前俱乐部的所有成员信息
     * @returns 成员信息数组
     */
    public getMemberList(): IClubMemberFullInfo[] {
        return this.getAllMemberListByClubId(this.curClubId);
    }

    /**
     * 更新指定成员的筹码信息
     * @param clubId 俱乐部ID
     * @param userId 用户ID
     * @param chipBalance 新的筹码余额
     */
    public updateChipBalanceByCludIdAndUserId(clubId: number, userId: number, chipBalance: number): void {
        const memberInfo = this.getMemberInfoByClubIdAndUserId(clubId, userId);
        if (memberInfo) {
            memberInfo.chipBalance = chipBalance;
            this.setMemberInfoByClubId(clubId, memberInfo);
            console.log(TAG, `成员${userId}筹码已更新为: ${chipBalance}`);
        } else {
            console.warn(TAG, `更新筹码失败，成员${userId}不存在于俱乐部${clubId}`);
        }
    }

    /**
     * 更新当前俱乐部成员的筹码信息
     * @param userId 用户ID
     * @param chipBalance 新的筹码余额
     */
    public updateChipBalance(userId: number, chipBalance: number): void {
        this.updateChipBalanceByCludIdAndUserId(this.curClubId, userId, chipBalance);
    }

    /**
     * 批量更新成员筹码信息
     * @param chipBalanceList 筹码信息列表
     */
    public updateChipBalanceList(chipBalanceList: okpb.ClubUserChipBalance[]): void {
        if (!Array.isArray(chipBalanceList) || chipBalanceList.length === 0) {
            console.warn(TAG, "筹码余额列表为空");
            return;
        }

        console.log(TAG, "开始批量更新筹码信息，数量:", chipBalanceList.length);
        chipBalanceList.forEach((chipBalance) => {
            this.updateChipBalance(chipBalance.userId, chipBalance.balance);
        });
    }

    /**
     * 更新成员备注信息
     * @param clubId 俱乐部ID
     * @param userId 用户ID
     * @param mark 备注
     * @param markDetail 备注详情
     */
    public updateMemberRemark(clubId: number, userId: number, mark: string, markDetail: string): void {
        const memberInfo = this.getMemberInfoByClubIdAndUserId(clubId, userId);
        if (memberInfo) {
            memberInfo.mark = mark;
            memberInfo.markDetail = markDetail;
            this.setMemberInfoByClubId(clubId, memberInfo);
        }
    }

    /**
     * 更新成员身份
     * @param clubId 俱乐部ID
     * @param userId 用户ID
     * @param identity 新身份
     */
    public updateMemberIdentity(clubId: number, userId: number, identity: okpb.Identity): void {
        const memberInfo = this.getMemberInfoByClubIdAndUserId(clubId, userId);
        if (memberInfo) {
            memberInfo.identity = identity;
            this.setMemberInfoByClubId(clubId, memberInfo);
        }
    }

    /**
     * 删除成员
     * @param clubId 俱乐部ID
     * @param userIdList 用户ID列表
     */
    public deleteMember(clubId: number, userIdList: number[]): void {
        userIdList.forEach((userId) => {
            this.clearClubMembersCache(clubId, userId);
        });
    }

    // ==================== 缓存管理 ====================

    /**
     * 清除俱乐部信息缓存
     * @param clubId 俱乐部ID，不传则清除所有缓存
     */
    public clearClubInfo(clubId?: number): void {
        if (clubId !== undefined) {
            const deleted = this.clubInfoCache.delete(clubId);
            console.log(TAG, `俱乐部${clubId}缓存清除${deleted ? "成功" : "失败"}`);
        } else {
            const cacheSize = this.clubInfoCache.size;
            this.clubInfoCache.clear();
            console.log(TAG, `已清除所有俱乐部缓存，原缓存数量: ${cacheSize}`);
        }
    }

    /**
     * 清除俱乐部成员缓存
     * @param clubId 俱乐部ID
     * @param userId 用户ID，不传则清除该俱乐部所有成员缓存
     */
    public clearClubMembersCache(clubId: number, userId?: number): void {
        const clubEntry = this.clubInfoCache.get(clubId);
        if (!clubEntry) {
            console.warn(TAG, `俱乐部${clubId}不存在，无法清除成员缓存`);
            return;
        }

        if (userId !== undefined) {
            const deleted = clubEntry.memberMap.delete(userId);
            console.log(TAG, `俱乐部${clubId}的成员${userId}缓存清除${deleted ? "成功" : "失败"}`);
        } else {
            const memberCount = clubEntry.memberMap.size;
            clubEntry.memberMap.clear();
            console.log(TAG, `俱乐部${clubId}所有成员缓存已清除，原成员数量: ${memberCount}`);
        }
    }

    // ==================== 观察者模式 ====================

    /**
     * 订阅俱乐部信息更新通知
     * @param callback 更新回调函数
     */
    public onClubInfoUpdate(callback: (clubInfo: IClubInfoEntry) => void): void {
        if (typeof callback !== "function") {
            console.warn(TAG, "回调函数无效");
            return;
        }

        this.updateCallbacks.push(callback);
        console.log(TAG, "新增订阅者，当前订阅者数量:", this.updateCallbacks.length);
    }

    /**
     * 取消订阅俱乐部信息更新通知
     * @param callback 要取消的回调函数
     */
    public offClubInfoUpdate(callback: (clubInfo: IClubInfoEntry) => void): void {
        const index = this.updateCallbacks.indexOf(callback);
        if (index > -1) {
            this.updateCallbacks.splice(index, 1);
            console.log(TAG, "取消订阅成功，当前订阅者数量:", this.updateCallbacks.length);
        } else {
            console.warn(TAG, "要取消的回调函数不存在");
        }
    }

    /**
     * 通知所有订阅者数据更新
     * @param clubInfo 更新的俱乐部信息
     */
    private notifyUpdate(clubInfo: IClubInfoEntry): void {
        if (this.updateCallbacks.length === 0) {
            return;
        }

        console.log(TAG, "通知数据更新，订阅者数量:", this.updateCallbacks.length);
        this.updateCallbacks.forEach((callback, index) => {
            try {
                callback(clubInfo);
            } catch (error) {
                console.error(TAG, `订阅者${index}回调执行错误:`, error);
            }
        });
    }

    /**
     * 通知成员数据订阅者
     * @param clubId 俱乐部ID
     * @param memberList 成员列表数据，null表示请求失败
     */
    private notifyMemberDataSubscribers(clubId: number, memberList: IClubMemberFullInfo[] | null): void {
        console.log(TAG, `通知成员数据订阅者，俱乐部ID: ${clubId}，订阅者数量: ${this.memberDataSubscribers.length}`);

        // 过滤出对应俱乐部的订阅者
        const targetSubscribers = this.memberDataSubscribers.filter((subscriber) => subscriber.clubId === clubId);

        // 通知所有订阅者
        targetSubscribers.forEach((subscriber, index) => {
            try {
                console.log(TAG, `通知第 ${index + 1} 个订阅者`);
                subscriber.callback(memberList);
            } catch (error) {
                console.error(TAG, "通知成员数据订阅者时发生错误:", error);
            }
        });

        // 清理已通知的订阅者
        this.clearMemberDataSubscribers(clubId);
    }

    /**
     * 清理指定俱乐部的成员数据订阅者
     * @param clubId 俱乐部ID
     */
    private clearMemberDataSubscribers(clubId: number): void {
        const originalCount = this.memberDataSubscribers.length;

        // 移除指定俱乐部的订阅者
        for (let i = this.memberDataSubscribers.length - 1; i >= 0; i--) {
            if (this.memberDataSubscribers[i].clubId === clubId) {
                this.memberDataSubscribers.splice(i, 1);
            }
        }

        const newCount = this.memberDataSubscribers.length;
        console.log(TAG, `清理成员数据订阅者，俱乐部ID: ${clubId}，清理前: ${originalCount}，清理后: ${newCount}`);
    }

    /**
     * 手动取消成员数据请求订阅（一般不需要手动调用，请求完成时会自动清理）
     * @param clubId 俱乐部ID，不传则清理所有订阅者
     * @param callback 指定要取消的回调函数，不传则清理指定俱乐部的所有订阅者
     */
    public cancelMemberDataSubscription(clubId?: number, callback?: (memberList: IClubMemberFullInfo[] | null) => void): void {
        const originalCount = this.memberDataSubscribers.length;

        if (clubId === undefined) {
            // 清理所有订阅者
            this.memberDataSubscribers.length = 0;
            console.log(TAG, `清理所有成员数据订阅者，清理前: ${originalCount}，清理后: 0`);
        } else if (callback === undefined) {
            // 清理指定俱乐部的所有订阅者
            this.clearMemberDataSubscribers(clubId);
        } else {
            // 清理指定的回调函数
            for (let i = this.memberDataSubscribers.length - 1; i >= 0; i--) {
                const subscriber = this.memberDataSubscribers[i];
                if (subscriber.clubId === clubId && subscriber.callback === callback) {
                    this.memberDataSubscribers.splice(i, 1);
                    break;
                }
            }
            const newCount = this.memberDataSubscribers.length;
            console.log(TAG, `取消特定成员数据订阅，俱乐部ID: ${clubId}，清理前: ${originalCount}，清理后: ${newCount}`);
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 验证俱乐部ID的有效性
     * @param clubId 俱乐部ID
     * @returns 是否有效
     */
    private validateClubId(clubId: number): boolean {
        if (!clubId || clubId <= 0) {
            console.error(TAG, "俱乐部ID无效:", clubId);
            return false;
        }
        return true;
    }

    /**
     * 从成员列表响应中提取用户总数
     * @param response 成员列表响应
     * @returns 用户总数
     */
    private extractUserCount(response: okpb.RspGetClubUserList): number {
        if (!response.data?.clubUserMap) {
            return 0;
        }

        let count = 0;
        Object.values(response.data.clubUserMap).forEach((userList) => {
            count += userList?.users?.length || 0;
        });
        return count;
    }

    // ==================== 调试和监控 ====================

    /**
     * 获取内存使用情况
     * @returns 缓存统计信息
     */
    public getMemoryInfo(): {
        cacheSize: number;
        currentClub: number;
        membersCacheSize: number;
        subscribersCount: number;
        memberDataSubscribersCount: number;
    } {
        let totalMembersCount = 0;
        this.clubInfoCache.forEach((clubEntry) => {
            totalMembersCount += clubEntry.memberMap.size;
        });

        return {
            cacheSize: this.clubInfoCache.size,
            currentClub: this.curClubId,
            membersCacheSize: totalMembersCount,
            subscribersCount: this.updateCallbacks.length,
            memberDataSubscribersCount: this.memberDataSubscribers.length,
        };
    }

    /**
     * 打印调试信息
     */
    public printDebugInfo(): void {
        const memInfo = this.getMemoryInfo();
        console.log(TAG, "=== ClubDataManager 调试信息 ===");
        console.log(TAG, "当前俱乐部ID:", memInfo.currentClub);
        console.log(TAG, "缓存的俱乐部数量:", memInfo.cacheSize);
        console.log(TAG, "缓存的成员总数:", memInfo.membersCacheSize);
        console.log(TAG, "俱乐部信息订阅者数量:", memInfo.subscribersCount);
        console.log(TAG, "成员数据订阅者数量:", memInfo.memberDataSubscribersCount);
        console.log(TAG, "请求状态:", this.isRequesting ? "进行中" : "空闲");
        console.log(TAG, "================================");
    }

    // ==================== 生命周期管理 ====================

    /**
     * 销毁单例实例（用于应用退出时清理）
     */
    public static destroy(): void {
        if (ClubDataManager.instance) {
            console.log(TAG, "开始销毁ClubDataManager实例");

            // 清理缓存
            ClubDataManager.instance.clubInfoCache.clear();
            ClubDataManager.instance.updateCallbacks.length = 0;
            ClubDataManager.instance.memberDataSubscribers.length = 0;

            // 重置状态
            ClubDataManager.instance.curClubId = 0;
            ClubDataManager.instance.isRequesting = false;

            // 销毁实例
            ClubDataManager.instance = null;

            console.log(TAG, "ClubDataManager实例已销毁");
        }
    }

    // ==================== 俱乐部个人信息更新 ====================
    public async requestClubUserSelfInfo() {
        let data: okpb.RspClubUserSelfInfo = await ClubAPI.getInstance().ReqClubUserSelfInfo();
        if (data && data.errorCode == okpb.ERET.OK) {
            let newClubInfo = this.getClubInfoById(data.selfInfo.clubId);
            if (newClubInfo) {
                newClubInfo.ClubUserSelfInfo = data.selfInfo;
                this.updateClubInfo(newClubInfo);
                //数据存储成功后，在相应的界面实时更新数据信息
                this.emitEvent(CLUB_EVENT.UPDATE_CLUB_USER_SELF_INFO);
            } else {
                console.warn(TAG, "更新俱乐部个人信息信息失败，俱乐部不存在:", data.selfInfo.clubId);
            }
        }
    }

    public getClubUserSelfInfo(): okpb.ClubUserSelfInfo {
        if (this.curClubId <= 0) {
            console.warn(TAG, "当前俱乐部ID无效:", this.curClubId);
            return null;
        }
        let newClubInfo = this.getClubInfoById(this.curClubId);
        if (newClubInfo) {
            return newClubInfo.ClubUserSelfInfo;
        } else {
            console.warn(TAG, "获取俱乐部个人信息信息失败，俱乐部不存在:", this.curClubId);
        }
        return null;
    }

    // ==================== 筹码数据 ====================
    /**
     * 请求俱乐部柜台筹码信息
     * @description 在成员详细信息页面，我为创建者或者管理员，操作一个目标用户为代理的成员时，需要请求俱乐部柜台筹码信息
     */
    public async requestClubCounterInfo() {
        let clubInfo = this.getClubInfo();
        if (!clubInfo) return;

        //根据当前的身份信息来获取相应的柜台数据
        if (clubInfo.identity > okpb.Identity.IDE_AGENT) {
            //群主和管理员--- 获取柜台信息
            let data: okpb.RspGetClubCounterInfo = await ClubAPI.getInstance().ReqGetClubCounterInfo();
            if (data && data.errorCode == okpb.ERET.OK) {
                let newClubInfo = this.getClubInfoById(data.data.clubId);
                if (newClubInfo) {
                    newClubInfo.ClubCounterInfo = data.data;
                    this.updateClubInfo(newClubInfo);
                    //数据存储成功后，在相应的界面实时更新数据信息
                    this.emitEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO);
                } else {
                    console.warn(TAG, "更新俱乐部柜台筹码信息失败，俱乐部不存在:", data.data.clubId);
                }
            } else {
                console.error(TAG, `管理员 请求俱乐部柜台信息失败，错误码: ${data.errorCode}`);
            }
        } else if (clubInfo.identity == okpb.Identity.IDE_AGENT) {
            //代理 --- 获取柜台信息
            let data: okpb.RspGetClubAgentCounterInfo = await ClubAPI.getInstance().ReqGetClubAgentCounterInfo();
            if (data && data.errorCode == okpb.ERET.OK) {
                let newClubInfo = this.getClubInfoById(data.data.clubId);
                if (newClubInfo) {
                    newClubInfo.ClubAgentCounterInfo = data.data;
                    this.updateClubInfo(newClubInfo);
                    this.emitEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO);
                } else {
                    console.warn(TAG, "更新俱乐部柜台筹码信息失败，俱乐部不存在:", data.data.clubId);
                }
            } else {
                console.error(TAG, `代理 请求俱乐部柜台信息失败，错误码: ${data.errorCode}`);
            }
        }
    }

    /**
     * 请求俱乐部柜台筹码信息
     * @return identity:返回当前玩家的身份信息   data:根据身份信息返回对应的数据   okpb.ClubCounterInfo | okpb.ClubAgentCounterInfo
     * @description 在成员详细信息页面，我为创建者或者管理员，操作一个目标用户为代理的成员时，需要请求俱乐部柜台筹码信息
     */
    getClubCounterInfo(): { identity: okpb.Identity; data: any } {
        let clubInfo = this.getClubInfo();
        if (!clubInfo) return;
        let newClubInfo = this.getClubInfoById(clubInfo.clubId);
        if (!newClubInfo) {
            console.warn(TAG, "获取筹码信息失败:", clubInfo.clubId);
            return;
        }
        //根据当前的身份信息来获取相应的柜台数据
        if (clubInfo.identity > okpb.Identity.IDE_AGENT) {
            if (!newClubInfo.ClubCounterInfo) return null;
            return {
                identity: clubInfo.identity,
                data: newClubInfo.ClubCounterInfo,
            };
        } else if (clubInfo.identity == okpb.Identity.IDE_AGENT) {
            if (!newClubInfo.ClubAgentCounterInfo) return null;
            return {
                identity: clubInfo.identity,
                data: newClubInfo.ClubAgentCounterInfo,
            };
        }
        return null;
    }

    /**
     * 更新玩家列表的筹码信息
     * 更新成员列表里所有的筹码信息，并发送更新事件
     */
    public async requestClubAllUserChipBalance() {
        let memberList = this.getMemberList();
        if (memberList.length == 0) {
            return;
        }
        let userIds = [];
        for (let j = 0; j < memberList.length; j++) {
            userIds.push(memberList[j].userId);
        }
        let data: okpb.RspClubUserChipBalance = await ClubAPI.getInstance().ReqClubUserChipBalance(userIds);
        if (data && data.errorCode == okpb.ERET.OK) {
            //更新筹码后，再次重新获取最新数据
            this.updateChipBalanceList(data.balances);
            this.emitEvent(CLUB_EVENT.UPDATE_CLUB_USER_CHIP_BALANCE);
        }
    }

    // ==================== 数据转换 ====================

    /**
     * 将 IClubCompleteInfo 转换成 okpb.ClubResp
     * @param clubInfo 俱乐部信息
     * @returns okpb.ClubResp
     */
    public fullClubInfoToClubResp(clubInfo: IClubCompleteInfo): okpb.ClubResp {
        // IClubCompleteInfo 这个转换成  okpb.ClubResp
        const clubResp = {} as okpb.ClubResp;
        clubResp.clubId = clubInfo.clubId;
        clubResp.clubName = clubInfo.clubName;
        clubResp.icon = clubInfo.icon;
        clubResp.clubName = clubInfo.clubName;
        clubResp.peopleNum = clubInfo.peopleNum;
        clubResp.peopleLimit = clubInfo.peopleLimit;
        clubResp.isInClub = clubInfo.isInClub;
        clubResp.aliveRoom = clubInfo.aliveRoom;
        clubResp.identity = clubInfo.identity;
        clubResp.intro = clubInfo.intro;
        clubResp.clubNameModifyCount = clubInfo.clubNameModifyCount;
        clubResp.permissions = clubInfo.permissions;
        return clubResp;
    }

    // ==================== 成员备注信息 ====================

    /**
     * 请求成员备注列表
     * @param clubId 俱乐部ID
     * @param retryCount 重试次数: 确保失败后再重试1次
     */
    public requestMemberRemarkList(clubId: number, retryCount: number = 1): void {
        console.log(TAG, "请求成员备注列表:", clubId);
        ClubAPI.getInstance()
            .ReqGetClubUserRemarkList(clubId)
            .then((data) => {
                console.log(TAG, "成员备注列表:", data);
                if (data.errorCode == okpb.ERET.OK) {
                    this.updateMemberRemarkList(clubId, data);
                } else {
                    if (retryCount > 0) {
                        console.warn(TAG, `请求成员备注列表失败，错误码: ${data.errorCode}，剩余重试次数: ${retryCount}`);
                        this.requestMemberRemarkList(clubId, retryCount - 1);
                    } else {
                        console.error(TAG, `请求成员备注列表失败，错误码: ${data.errorCode}，重试次数已用完`);
                    }
                }
            })
            .catch((error) => {
                if (retryCount > 0) {
                    console.warn(TAG, `请求成员备注列表异常，剩余重试次数: ${retryCount}，错误:`, error);
                    this.requestMemberRemarkList(clubId, retryCount - 1);
                } else {
                    console.error(TAG, "请求成员备注列表失败，重试次数已用完:", error);
                }
            })
            .finally(() => {});
    }

    /**
     * 更新成员备注列表
     * @param clubId 俱乐部ID
     * @param data 成员备注列表
     */
    private updateMemberRemarkList(clubId: number, data: okpb.RspClubUserRemarkList): void {
        console.log(TAG, "更新成员备注列表:", data);
        const remarkList = data.remarkList;
        console.log(TAG, "添加备注前的成员:" + JSON.stringify(this.getMemberList()));
        remarkList.forEach((remark) => {
            const newMemberInfo = this.getMemberInfoByClubIdAndUserId(clubId, remark.friendId);
            if (newMemberInfo) {
                newMemberInfo.mark = remark.remark;
                newMemberInfo.markDetail = remark.detail;
                this.setMemberInfoByClubId(clubId, newMemberInfo);
            } else {
                console.warn(TAG, "成员备注更新失败，成员不存在:", remark.friendId);
            }
        });
        console.log(TAG, "更新成员备注列表完成,这个俱乐部的所有数据:" + JSON.stringify(this.getClubInfoWithMembers()));
        console.log(TAG, "添加完备注后所有的成员:" + JSON.stringify(this.getMemberList()));
    }
}
