/**
 * 消息
 */
export enum ComGameEvent {
    //聊天窗口关闭
    GameChatViewClose = "ComGameEvent.GameChatViewClose",
    //更新游戏背景
    UpdateGameBg = "ComGameEvent.UpdateGameBg"
}
/**
 * 存储Key值
 */
export enum ComGameKey {
    //游戏背景样式
    GameBgStyle = "ComGameKey.GameBgStyle"
}

/**
 * UI界面ID
 */
export enum COMGAME_UIID {
    //聊天界面
    GameChat = "view/chat/GameChatView",
    //消息界面
    GameMsg = "view/msg/GameMsgView",
    //表情界面
    GameEmoji = `view/emoji/GameEmojiView`,
    //表情界面 播放动画
    GameEmojiAni = `view/emoji/GameEmojiAniView`,
    //菜单界面
    GameMenu = `view/menu/GameMenuView`,
    //用户信息界面
    GameUserSelf = `view/userInfo/GameUserSelfView`,
    GameUserOther = `view/userInfo/GameUserOtherView`,
}

/**
 * 房间状态  1 关闭 ，2进行中 3 等待 4销毁 5暂停
 */
export enum ROOM_STATUS {
    kClose = 1,
    kRunning = 2,
    kWait = 3,
    kDestroy = 4,
    kPause = 5
}

/**
 *  gameStatus 用户状态
 *  1等待, 2游戏中, 4弃牌,  5 输光  101:等待补充,
 *  102 等待房主同意 ，103留座离桌  104 托管中
 *  105 保座离桌
 * 等待补充,带入审核,留座离桌
 */
export enum GAME_STATUS {
    kWait = 1,
    kGaming = 2,
    kFold = 3,
    kLost = 4,
    kWaitBuyIn = 101,
    kWaitAgree = 102,
    kLeaveForStayTable = 103,
    kHosting = 103,
    kLeaveForSaveTable = 105
}


/**
 * 表情类型
 */
export enum ImEmojiType {
    kEmoji = 0,
    kEachProp,
}

/**
 * 聊天类型
 */
export enum ImChatMsgType {
    text = 0,
}
