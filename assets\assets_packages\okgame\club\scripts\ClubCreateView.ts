import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import { okpb } from "../../proto/proto_msg";
import OKGameManager from "../../public/OKGameManager";
import ClubIconSelectView from "./ClubIconSelectView";
import OKClubAPI from "./OKClubAPI";
import UIClubCreateView_auto from "./UIClubCreateView_auto";

const { ccclass, property } = cc._decorator;
const TAG = "[ClubCreateView]";

/**
 * 俱乐部创建界面
 */
@ccclass
export default class ClubCreateView extends UBaseDialog {
    @property(cc.Prefab)
    protected itemAvatorPrefab: cc.Prefab = null;

    protected ui: UIClubCreateView_auto = null;

    /**
     * 选择图片脚本
     */
    protected choiceImgScript: ClubIconSelectView = null;
    /**
     * 图片url或id
     */
    private clubIconUrl: string = "";
    private clubName: string = "";

    private static readonly MIN_CLUB_NAME_LENGTH = 6;
    private static readonly MAX_CLUB_NAME_LENGTH = 20;

    onUILoad(): void {
        this.ui = new UIClubCreateView_auto(this.node);
        this.initUI();
        this.initListener();
    }

    public onShow(): void { }

    private initUI(): void {
        const choiceImageView =
            this.ui.IconNode.getComponent(UPrefabContainer).getNodeComponent("ClubIconSelectView");
        if (choiceImageView) {
            this.choiceImgScript = choiceImageView.getComponent("ClubIconSelectView");
            console.log(TAG, "choiceImgScript", this.choiceImgScript);
        } else {
            console.error(TAG, "choiceImageView is null");
        }

        // 设置EditBox的触摸事件为passive模式： 解决警告：WebEditBoxImpl.js:701 [Violation]Added non-passive event listener to a scroll-blocking 'touchstart' event.
        if (!cc.sys.isNative) {
            const editBox = this.ui.clubNameEditBox.getComponent(cc.EditBox);
            if (editBox && (editBox as any)._impl && (editBox as any)._impl._elem) {
                (editBox as any)._impl._elem.addEventListener("touchstart", () => { }, {
                    passive: true,
                });
            }
        }
    }

    private initListener(): void {
        this.onRegisterEvent(this.ui.closeBtn, this.closeDialog.bind(this));
        this.onRegisterEvent(this.ui.btnConfirm, this.toCreateClub.bind(this));
        this.onRegisterEvent(this.ui.panelMask, this.closeDialog.bind(this));
    }

    /**
     * 创建俱乐部按钮点击事件
     */
    private toCreateClub(): void {
        this.clubName = this.ui.clubNameEditBox.getComponent(cc.EditBox).string;
        if (!this.validateClubName(this.clubName)) {
            return;
        }
        // 获得选择的图片的id或者url
        this.clubIconUrl = this.choiceImgScript.getSelectedAvatarUrl();
        console.log(TAG, "Req创建俱乐部", this.clubName, this.clubIconUrl);
        this.showLoading();
        OKClubAPI.getInstance()
            .ReqCreateClub(this.clubName, this.clubIconUrl, "")
            .then((rsp: okpb.RspJoinClub) => {
                this.handleCreateClubResponse(rsp);
            })
            .catch((err) => {
                console.error(TAG, "创建俱乐部失败", err);
                this.onCreateClubFailed(1010, err.message);
            })
            .finally(() => {
                this.dismissLoading();
            });
    }

    /**
     * 验证俱乐部名称
     * @param clubName 俱乐部名称
     * @returns 是否有效
     */
    private validateClubName(clubName: string): boolean {
        if (clubName.length === 0) {
            this.showThisToast("club.clubNameEmpty");
            return false;
        }

        if (clubName.length < ClubCreateView.MIN_CLUB_NAME_LENGTH) {
            this.showThisToast("club.clubNameTooShort");
            return false;
        }

        if (clubName.length > ClubCreateView.MAX_CLUB_NAME_LENGTH) {
            this.showThisToast("club.clubNameTooLong");
            return false;
        }
        return true;
    }

    /**
     * 处理创建俱乐部响应
     * @param res 响应数据
     */
    private handleCreateClubResponse(rsp: okpb.RspJoinClub): void {
        console.log(TAG, "Rsp: 创建俱乐部结果: ", JSON.stringify(rsp));
        if (rsp.errorCode === 0) {
            const clubInfo: okpb.ClubResp = rsp?.data ?? null;
            if (!clubInfo) {
                console.log(TAG, "Rsp: 创建俱乐部失败,toast 1010信息 ");
                this.onCreateClubFailed(1010, "RspJoinClub clubInfo is null");
                return;
            }
            OKGameManager.getInstance().showClubScene(clubInfo, true);
            this.closeDialog();
        } else {
            this.onCreateClubFailed(rsp.errorCode, rsp.errMsg);
        }
    }

    /**
     * 创建俱乐部失败
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     */
    private onCreateClubFailed(errorCode: number, errorMsg: string): void {
        this.showThisToast("errorCode." + errorCode);
    }

    private showThisToast(msgKey: string): void {
        OKGameManager.getInstance().showToastById(msgKey);
    }

    private showLoading(): void {
        OKGameManager.getInstance().showLoading();
    }

    private dismissLoading(): void {
        OKGameManager.getInstance().hideLoading();
    }
}
