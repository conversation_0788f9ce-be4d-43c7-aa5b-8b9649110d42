import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import OKExtManager from "../../../okgame/public/OKExtManager";
import ComGameWs from "../../network/ComGameWs";
import UIGameEmojiView_auto from "./UIGameEmojiView_auto";
const { ccclass, property } = cc._decorator;
const MAX_EMOJI_NUM = 24;
@ccclass
export default class UIGameEmojiView extends UBaseDialog {
	protected ui: UIGameEmojiView_auto = null;
	onLoad(): void {
		this.ui = new UIGameEmojiView_auto(this.node);
		this.onRegisterEvent(this.ui.maskNode, this.playExitAnim.bind(this));
		this.initEmoji();
	}

	private initEmoji() {
		this.ui.emojiItem.active = false;
		for (let i = 1; i <= MAX_EMOJI_NUM; i++) {
			let id = i >= 10 ? '' + i : '0' + i
			//---
			let item = cc.instantiate(this.ui.emojiItem);
			item.parent = this.ui.content;
			item.active = true;
			OKExtManager.getInstance().loadEmojiIcon(id, (err: Error, spriteFrame: cc.SpriteFrame) => {
				if (!cc.isValid(item)) {
					return;
				}
				item.getComponent(cc.Sprite).spriteFrame = spriteFrame;
			})
			this.onRegisterEvent(item, () => {
				ComGameWs.getInstance().sendEmoji(id);
				this.closeDialog();
			})
		}
	}
}