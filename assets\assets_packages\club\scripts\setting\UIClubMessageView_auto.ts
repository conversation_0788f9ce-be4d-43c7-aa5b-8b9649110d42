export default class UIClubMessageView_auto {
    node:cc.Node = null;   
	ClubMessageView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	titleContainer: cc.Node;
	title: cc.Node;
	closedBtn: cc.Node;
	closedBtnBg: cc.Node;
	memberApply: cc.Node;
	memberApplyTitle: cc.Node;
	memberApplyToggle: cc.Node;
	memberApplyBackground: cc.Node;
	memberApplyCheckmark: cc.Node;
	memberExit: cc.Node;
	memberExitTitle: cc.Node;
	memberExitToggle: cc.Node;
	memberExitBackground: cc.Node;
	memberExitCheckmark: cc.Node;
	chipApply: cc.Node;
	chipApplyTitle: cc.Node;
	chipApplyToggle: cc.Node;
	chipApplyBackground: cc.Node;
	chipApplyCheckmark: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubMessageView = this.node;
		this.mask = this.ClubMessageView.getChildByName("mask");
		this.mainContainer = this.ClubMessageView.getChildByName("mainContainer");
		this.titleContainer = this.mainContainer.getChildByName("titleContainer");
		this.title = this.titleContainer.getChildByName("title");
		this.closedBtn = this.titleContainer.getChildByName("closedBtn");
		this.closedBtnBg = this.closedBtn.getChildByName("closedBtnBg");
		this.memberApply = this.mainContainer.getChildByName("memberApply");
		this.memberApplyTitle = this.memberApply.getChildByName("memberApplyTitle");
		this.memberApplyToggle = this.memberApply.getChildByName("memberApplyToggle");
		this.memberApplyBackground = this.memberApplyToggle.getChildByName("memberApplyBackground");
		this.memberApplyCheckmark = this.memberApplyToggle.getChildByName("memberApplyCheckmark");
		this.memberExit = this.mainContainer.getChildByName("memberExit");
		this.memberExitTitle = this.memberExit.getChildByName("memberExitTitle");
		this.memberExitToggle = this.memberExit.getChildByName("memberExitToggle");
		this.memberExitBackground = this.memberExitToggle.getChildByName("memberExitBackground");
		this.memberExitCheckmark = this.memberExitToggle.getChildByName("memberExitCheckmark");
		this.chipApply = this.mainContainer.getChildByName("chipApply");
		this.chipApplyTitle = this.chipApply.getChildByName("chipApplyTitle");
		this.chipApplyToggle = this.chipApply.getChildByName("chipApplyToggle");
		this.chipApplyBackground = this.chipApplyToggle.getChildByName("chipApplyBackground");
		this.chipApplyCheckmark = this.chipApplyToggle.getChildByName("chipApplyCheckmark");

    }
}
