import UBaseDialog from "../../../../framwork/widget/UBaseDialog"
import MessageView from "../../../../framwork/widget/UScrollView/MessageView"
import { OKGameMsgType } from "../../../okgame/network/OKGameMsgType"
import { WSMsgData } from "../../../okgame/network/OKGameWS"
import { okpb } from "../../../okgame/proto/proto_msg"
import { wspb } from "../../../okgame/proto/proto_ws_msg"
import OKGameManager from "../../../okgame/public/OKGameManager"
import ComGameAPI from "../../network/ComGameAPI"
import ComGameWs from "../../network/ComGameWs"
import { ComGameEvent } from "../../pubilc/ComGameConst"
import ComGameData from "../../pubilc/ComGameData"
import ComGameManager from "../../pubilc/ComGameManager"
import UIGameChatItem from "./UIGameChatItem"
import UIGameChatView_auto from "./UIGameChatView_auto"

const { ccclass, property, menu } = cc._decorator

@ccclass
@menu("comgame/UIGameChatView")
export default class UIGameChatView extends UBaseDialog {
    protected ui: UIGameChatView_auto = null;

    @property(cc.Label)
    label: cc.Label = null
    @property(cc.Prefab)
    chatItem: cc.Prefab = null

    //---------
    private _records: okpb.ChatRecordData[] = []

    onUILoad(): void {
        this.ui = new UIGameChatView_auto(this.node);

        this.onRegisterEvent(this.ui.sendBtn, this.sendTxtMsg.bind(this));
        //----注册协议-----
        this.registerProto(OKGameMsgType.imMsgNotify, this.onImMsgNotify.bind(this));

        //-----获取历史记录
        ComGameAPI.getInstance().ReqGetChatRecord(ComGameData.getInstance().getCurRoomId()).then((data: okpb.RspGetChatRecord) => {
            console.log(data)
            if (data.errorCode != 0) {
                return
            }
            this._records = data.data.reverse();
            this.initView();
        });
    }

    initView() {
        let messageView = this.node.getComponent(MessageView);
        messageView.setDelegate(this)
        messageView.initCell(this._records.length);
    }

    addCell(cellCount = 1) {
        this.node.getComponent(MessageView).addCell(cellCount);
    }

    cellAtIndex(idx: number): cc.Node {
        let msg = this._records[idx];
        if (!msg) return null;
        let node = cc.instantiate(this.chatItem);
        node.active = true;
        let view = node.getComponent(UIGameChatItem);
        view.setData(msg);
        return node;
    }

    private onImMsgNotify(data: WSMsgData) {
        let obj: wspb.ChatMsgPO = data.decode(wspb.ChatMsgPO);
        this._records.push({
            id: obj.id,
            userId: obj.userId,
            avatar: obj.avatar,
            nickname: obj.nickname,
            type: obj.type,
            content: obj.content,
            createTime: obj.createTime,
            second: obj.second,
            originOpt: "",
        });
        this.addCell(1);
    }

    onShow() {

    }

    onEnable(): void {
        this.updateView();
    }

    sendTxtMsg() {
        let chatStr: string = this.ui.chatEditBox.getComponent(cc.EditBox).string;
        //没有任何信息，不会发送成功
        if (chatStr === '') return;
        //聊天字段不能超过100
        if (chatStr.length > 100) chatStr = chatStr.substring(0, 100);

        //去掉前后的空白字符终止符等
        let emptystr = chatStr.trim() === '';
        if (emptystr) {
            OKGameManager.getInstance().showToastById("comGame.chat.sendChatMsgTips1");
            return;
        }

        //去掉所有的空字符
        let msg = chatStr;
        msg = msg.replace(/\s/g, "");
        if (msg.length == 0) return;

        //随机一个六位数的id
        let id = this.generate6ID();
        ComGameWs.getInstance().sendChatMsg(id, msg);

        //发送聊天信息后，清空当前输入框输入的文字信息
        this.ui.chatEditBox.getComponent(cc.EditBox).string = "";
    }

    generate6ID() {
        const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let id = '';
        const len = characters.length;
        for (let i = 0; i < 6; i++) {
            const randomIndex = Math.floor(Math.random() * len);
            id += characters[randomIndex];
        }
        return id;
    }

    onCloseBtn() {
        this.node.active = false
        this.emitEvent(ComGameEvent.GameChatViewClose)
    }

    private updateView() {
        let layouts = this.ui.content.getComponentsInChildren(cc.Layout)
        if (layouts) {
            layouts.forEach((layout) => {
                layout.updateLayout()
            })
        }
        this.node.getComponent(MessageView).initCell(this._records.length);
    }
}
