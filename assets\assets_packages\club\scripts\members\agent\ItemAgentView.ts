import UBaseView from "../../../../../framwork/widget/UBaseView";
import UIconSprite from "../../../../../framwork/widget/UIconSprite";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameUtils from "../../../../okgame/public/OKGameUtils";
import ClubManger from "../../ClubManger";
import { IClubCompleteInfo, IClubMemberFullInfo } from "../MemberTypes";

const { ccclass, property } = cc._decorator;
/**
 * 代理列表的itemView
 */
@ccclass
export default class ItemAgentView extends UBaseView {
    @property(cc.Node)
    agentInfoNode: cc.Node = null;

    @property(cc.Sprite)
    avatorImg: cc.Sprite = null;

    @property(cc.Label)
    accountNameTxt: cc.Label = null;

    @property(cc.Label)
    userIDTxt: cc.Label = null;

    @property(cc.Label)
    nickNameTxt: cc.Label = null;

    // 下线信息
    @property(cc.Node)
    membersInfoNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "下线数量" })
    subLineCountTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "下线筹码值" })
    downlineChipsValueTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "信用余额值" })
    CreditBalanceValueTxt: cc.Label = null;

    // 表格信息
    @property(cc.Node)
    descNode: cc.Node = null;

    @property(cc.Label)
    thisWeekProfitLossTxt: cc.Label = null;

    @property(cc.Label)
    thisWeekServiceFeeTxt: cc.Label = null;

    @property(cc.Label)
    lastWeekProfitLossTxt: cc.Label = null;

    @property(cc.Label)
    lastWeekServiceFeeTxt: cc.Label = null;

    @property(cc.Label)
    totalProfitLossTxt: cc.Label = null;

    @property(cc.Label)
    totalServiceFeeTxt: cc.Label = null;

    private agentUserInfo: IClubMemberFullInfo = null;
    private clubInfo: IClubCompleteInfo = null;

    onUILoad(): void {
        this.initView();
    }

    initView(): void {
        this.onRegisterEvent(this.agentInfoNode, this.onAgentInfoNodeClick.bind(this));
        this.onRegisterEvent(this.descNode, this.onDescNodeClick.bind(this));
        this.onRegisterEvent(this.membersInfoNode, this.onMembersInfoNodeClick.bind(this));
    }

    /**
     * 设置代理用户信息
     */
    public setData(_agentUserInfo: IClubMemberFullInfo, clubInfo: IClubCompleteInfo): void {
        this.agentUserInfo = _agentUserInfo;
        this.clubInfo = clubInfo;
        const iconScript = this.avatorImg.getComponent(UIconSprite);
        if (iconScript) {
            iconScript.setIcon(_agentUserInfo.avatar);
        }
        this.accountNameTxt.string = OKGameUtils.getStringMaxBitWithCase(_agentUserInfo.mark ? _agentUserInfo.mark : _agentUserInfo.nickname, 32);
        this.userIDTxt.string = "ID: " + _agentUserInfo.userId;
        this.nickNameTxt.string = OKGameUtils.getStringMaxBitWithCase(_agentUserInfo.nickname, 32);

        this.subLineCountTxt.string = _agentUserInfo.subCount.toString();
        this.downlineChipsValueTxt.string = _agentUserInfo.subBalance.toString();
        this.CreditBalanceValueTxt.string = _agentUserInfo.creditChips.toString();

        this.thisWeekProfitLossTxt.string = _agentUserInfo.changeScoreThisWeek.toString();
        this.thisWeekServiceFeeTxt.string = _agentUserInfo.serviceFeeThisWeek.toString();

        this.lastWeekProfitLossTxt.string = _agentUserInfo.changeScoreLastWeek.toString();
        this.lastWeekServiceFeeTxt.string = _agentUserInfo.serviceFeeLastWeek.toString();

        this.totalProfitLossTxt.string = _agentUserInfo.changeScoreTotal.toString();
        this.totalServiceFeeTxt.string = _agentUserInfo.serviceFeeTotal.toString();
    }

    /**
     * 跳转成员详情
     */
    private onAgentInfoNodeClick(): void {
        ClubManger.getInstance().showMemberDetailView(this.agentUserInfo);
    }

    private onDescNodeClick(): void {
        // 点击查看说明
    }

    private onMembersInfoNodeClick(): void {
        ClubManger.getInstance().showDownlineManagementView(this.agentUserInfo.userId);
    }
}
