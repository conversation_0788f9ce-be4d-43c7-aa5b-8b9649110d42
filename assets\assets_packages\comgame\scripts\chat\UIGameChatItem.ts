import UBaseView from "../../../../framwork/widget/UBaseView";
import UIconSprite from "../../../../framwork/widget/UIconSprite";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameData from "../../../okgame/public/OKGameData";
import OKGameUtils from "../../../okgame/public/OKGameUtils";
import UIGameChatItem_auto from "./UIGameChatItem_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIGameChatItem extends UBaseView {
	protected ui: UIGameChatItem_auto = null;

	chatData: okpb.ChatRecordData = null;

	onUILoad(): void {
		this.ui = new UIGameChatItem_auto(this.node);
		this.updateUI();
	}

	setData(data: okpb.ChatRecordData) {
		this.chatData = data;
		// let myUserId = OKGameData.getInstance().getUserId();
		// //自己的聊天记录显示在右边
		// if (myUserId == this.chatData.userId) {
		// 	this.node.height = 172;
		// } else {
		// 	this.node.height = 192;
		// }
		this.updateUI();
	}

	updateUI() {
		if (!this.ui || !this.chatData) return;
		let myUserId = OKGameData.getInstance().getUserId();
		//自己的聊天记录显示在右边
		if (myUserId == this.chatData.userId) {
			this.ui.chatPersonInfoR.active = true;
			this.ui.richTextR.getComponent(cc.Label).string = "" + this.chatData.content;
			this.ui.avatarR.getComponent(UIconSprite).setIcon(this.chatData.avatar);
		} else {
			this.ui.chatPersonInfoL.active = true;
			this.ui.richTextL.getComponent(cc.Label).string = "" + this.chatData.content;
			this.ui.avatarL.getComponent(UIconSprite).setIcon(this.chatData.avatar);
			let name = this.chatData.nickname;
			this.ui.nicknameL.getComponent(cc.RichText).string = OKGameUtils.getStringMaxBit(name, 11, true);
		}
	}
}