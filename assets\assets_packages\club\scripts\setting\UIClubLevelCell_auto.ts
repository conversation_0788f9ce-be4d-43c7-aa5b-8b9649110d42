export default class UIClubLevelCell_auto {
    node: cc.Node = null;
    ClubLevelCell: cc.Node;
    levelIcon: cc.Node;
    levelIconNum: cc.Node;
    levelTitleNode: cc.Node;
    levelTitle: cc.Node;
    levelNum: cc.Node;
    levelDate: cc.Node;
    levelManage: cc.Node;
    manageIcon: cc.Node;
    manageNum: cc.Node;
    levelMember: cc.Node;
    memberIcon: cc.Node;
    memberNum: cc.Node;
    levelDiamond: cc.Node;
    diamondIcon: cc.Node;
    diamondNum: cc.Node;

    constructor(node: cc.Node) {
        this.node = node;
        this.ClubLevelCell = this.node;
        this.levelIcon = this.ClubLevelCell.getChildByName("levelIcon");
        this.levelIconNum = this.levelIcon.getChildByName("levelIconNum");
        this.levelTitleNode = this.ClubLevelCell.getChildByName("levelTitleNode");
        this.levelTitle = this.levelTitleNode.getChildByName("levelTitle");
        this.levelNum = this.levelTitleNode.getChildByName("levelNum");
        this.levelDate = this.levelTitleNode.getChildByName("levelDate");
        this.levelManage = this.ClubLevelCell.getChildByName("levelManage");
        this.manageIcon = this.levelManage.getChildByName("manageIcon");
        this.manageNum = this.levelManage.getChildByName("manageNum");
        this.levelMember = this.ClubLevelCell.getChildByName("levelMember");
        this.memberIcon = this.levelMember.getChildByName("memberIcon");
        this.memberNum = this.levelMember.getChildByName("memberNum");
        this.levelDiamond = this.ClubLevelCell.getChildByName("levelDiamond");
        this.diamondIcon = this.levelDiamond.getChildByName("diamondIcon");
        this.diamondNum = this.levelDiamond.getChildByName("diamondNum");
    }
}
