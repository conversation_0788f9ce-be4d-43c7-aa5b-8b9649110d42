export default class UIClubCashierAgentCreditItem_auto {
    node:cc.Node = null;   
	ClubTransactionItem: cc.Node;
	userHeadNode: cc.Node;
	headIcon: cc.Node;
	headFrameIcon: cc.Node;
	userIDLayer: cc.Node;
	userID: cc.Node;
	roleTypeImg: cc.Node;
	CurrencyBgImg: cc.Node;
	clubChipIcon: cc.Node;
	freeTxt: cc.Node;
	labelNoteName: cc.Node;
	labelName: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubTransactionItem = this.node;
		this.userHeadNode = this.ClubTransactionItem.getChildByName("userHeadNode");
		this.headIcon = this.userHeadNode.getChildByName("headIcon");
		this.headFrameIcon = this.userHeadNode.getChildByName("headFrameIcon");
		this.userIDLayer = this.ClubTransactionItem.getChildByName("userIDLayer");
		this.userID = this.userIDLayer.getChildByName("userID");
		this.roleTypeImg = this.userIDLayer.getChildByName("roleTypeImg");
		this.CurrencyBgImg = this.ClubTransactionItem.getChildByName("CurrencyBgImg");
		this.clubChipIcon = this.CurrencyBgImg.getChildByName("clubChipIcon");
		this.freeTxt = this.CurrencyBgImg.getChildByName("freeTxt");
		this.labelNoteName = this.ClubTransactionItem.getChildByName("labelNoteName");
		this.labelName = this.ClubTransactionItem.getChildByName("labelName");

    }
}
