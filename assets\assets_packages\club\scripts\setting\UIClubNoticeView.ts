import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UIClubNoticeView_auto from "./UIClubNoticeView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubNoticeView]";

/**
 * 俱乐部公告设置
 *
 * @export
 * @class UIClubNoticeView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIClubNoticeView")
export default class UIClubNoticeView extends UBaseDialog {
    //#region 属性
    protected ui: UIClubNoticeView_auto = null;
    private contentText: string = "";
    private pushText: string = "";

    private readonly EVENT_HANDLERS = {
        "text-changed": {
            contentEditBox: this.onContentTextChanged.bind(this),
            pushEditBox: this.onPushTextChanged.bind(this),
        },
        "editing-did-began": {
            contentEditBox: this.onContentEditBegan.bind(this),
            pushEditBox: this.onPushEditBegan.bind(this),
        },
        "editing-did-ended": {
            contentEditBox: this.onContentEditEnded.bind(this),
            pushEditBox: this.onPushEditEnded.bind(this),
        },
    };

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIClubNoticeView_auto(this.node);
        this.initEvent();
    }

    start() {}

    onDestroy() {
        super.onDestroy();
        this.removeAllEventListeners();
    }

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubNoticeView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 初始化事件
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private initEvent() {
        // 按钮事件
        this.onRegisterEvent(this.ui.closedBtn, this.onClickBackButton.bind(this));
        this.onRegisterEvent(this.ui.cancelButton, this.onClickCancelButton.bind(this));
        this.onRegisterEvent(this.ui.trueButton, this.onClickConfirmButton.bind(this));

        // 输入框事件
        this.initEditBoxEvents();
    }

    /**
     * 初始化输入框事件
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private initEditBoxEvents() {
        // 内容输入框事件
        const contentEditBox = this.ui.contentEditBox.getComponent(cc.EditBox);
        if (contentEditBox) {
            this.registerEditBoxEvents(contentEditBox, "contentEditBox");
        }

        // 推送输入框事件
        const pushEditBox = this.ui.pushEditBox.getComponent(cc.EditBox);
        if (pushEditBox) {
            this.registerEditBoxEvents(pushEditBox, "pushEditBox");
        }
    }

    /**
     * 注册输入框事件
     *
     * @private
     * @param {cc.EditBox} editBox
     * @param {string} editBoxName
     * @memberof UIClubNoticeView
     */
    private registerEditBoxEvents(editBox: cc.EditBox, editBoxName: string) {
        Object.keys(this.EVENT_HANDLERS).forEach((eventType) => {
            const handler = this.EVENT_HANDLERS[eventType][editBoxName];
            if (handler) {
                editBox.node.on(eventType, handler);
            }
        });
    }

    /**
     * 移除所有事件监听
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private removeAllEventListeners(): void {
        const { contentEditBox, pushEditBox } = this.ui;

        if (!contentEditBox || !pushEditBox) {
            return;
        }

        Object.entries(this.EVENT_HANDLERS).forEach(([eventName, handlers]) => {
            contentEditBox.off(eventName, handlers.contentEditBox);
            pushEditBox.off(eventName, handlers.pushEditBox);
        });
    }

    //#endregion

    //#region 事件处理
    /**
     * 点击返回按钮
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onClickBackButton() {
        this.log("点击返回按钮");
        this.closeDialog();
    }

    /**
     * 点击取消按钮
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onClickCancelButton() {
        this.log("点击取消按钮");
        this.closeDialog();
    }

    /**
     * 点击确认按钮
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onClickConfirmButton() {
        this.log("点击确认按钮");
        this.log(`内容: ${this.contentText}`);
        this.log(`推送: ${this.pushText}`);

        // TODO: 调用API保存公告信息
        this.saveNoticeInfo();
    }

    /**
     * 内容输入框开始编辑
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onContentEditBegan() {
        this.log("内容输入框开始编辑");
    }

    /**
     * 内容输入框结束编辑
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onContentEditEnded() {
        this.log("内容输入框结束编辑");
    }

    /**
     * 内容输入框文本变化
     *
     * @private
     * @param {cc.EditBox} editBox
     * @param {string} text
     * @memberof UIClubNoticeView
     */
    private onContentTextChanged(editBox: cc.EditBox, text: string) {
        this.contentText = text;
        this.log(`内容文本变化: ${text}`);
    }

    /**
     * 推送输入框开始编辑
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onPushEditBegan() {
        this.log("推送输入框开始编辑");
    }

    /**
     * 推送输入框结束编辑
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private onPushEditEnded() {
        this.log("推送输入框结束编辑");
    }

    /**
     * 推送输入框文本变化
     *
     * @private
     * @param {cc.EditBox} editBox
     * @param {string} text
     * @memberof UIClubNoticeView
     */
    private onPushTextChanged(editBox: cc.EditBox, text: string) {
        this.pushText = text;
        this.log(`推送文本变化: ${text}`);
    }

    /**
     * 保存公告信息
     *
     * @private
     * @memberof UIClubNoticeView
     */
    private saveNoticeInfo() {
        // TODO: 实现保存逻辑
        this.log("保存公告信息");
        this.closeDialog();
    }

    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    //#endregion
}
