<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>emoji13_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{0,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji13_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{125,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji13_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{250,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji13_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{375,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji13_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{500,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{625,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{750,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{875,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1000,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1125,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1250,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji14_07.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1375,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji15_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1500,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji15_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1625,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji16_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1750,0},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji16_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{0,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji16_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{125,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji16_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{250,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji16_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{375,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji16_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{500,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji17_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{625,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji17_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{750,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji17_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{875,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji17_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1000,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji17_05.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1125,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji17_06.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1250,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji18_01.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1375,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji18_02.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1500,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji18_03.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1625,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>emoji18_04.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{122,150}</string>
                <key>spriteSourceSize</key>
                <string>{122,150}</string>
                <key>textureRect</key>
                <string>{{1750,153},{122,150}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>emoji01.png</string>
            <key>size</key>
            <string>{1872,303}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:004872cf4661b28f26836574889bfae0:19342364bfe30b7259aeb8591d809ee7:982ffd599552d876c5e4e7d93f6f818a$</string>
            <key>textureFileName</key>
            <string>emoji01.png</string>
        </dict>
    </dict>
</plist>
