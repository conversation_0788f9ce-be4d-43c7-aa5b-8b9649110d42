export default class UIClubSettingFuncCell_auto {
    node: cc.Node = null;
    ClubSettingFuncCell: cc.Node;
    cellIcon: cc.Node;
    cellTitle: cc.Node;
    cellArrow: cc.Node;
    line: cc.Node;

    constructor(node: cc.Node) {
        this.node = node;
        this.ClubSettingFuncCell = this.node;
        this.cellIcon = this.ClubSettingFuncCell.getChildByName("cellIcon");
        this.cellTitle = this.ClubSettingFuncCell.getChildByName("cellTitle");
        this.cellArrow = this.ClubSettingFuncCell.getChildByName("cellArrow");
        this.line = this.ClubSettingFuncCell.getChildByName("line");
    }
}
