import UBaseView from "../../../../../framwork/widget/UBaseView";

const { ccclass, property } = cc._decorator;
const TAG = "[ItemPermissionView]";
/**
 * 权限项
 */
@ccclass
export default class ItemPermissionView extends UBaseView {
    @property(cc.Label)
    nameLabel: cc.Label = null;

    @property(cc.Sprite)
    switchSprite: cc.Sprite = null;

    @property(cc.SpriteFrame)
    openImg: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    closeImg: cc.SpriteFrame = null;

    private isOpen: boolean = false;
    private changeCallback: Function = null;

    onUILoad(): void {
        this.onRegisterEvent(this.switchSprite.node, this.onSwitchClick.bind(this));
    }

    setData(name: string, isOpen: boolean, changeCallback: Function) {
        console.log(TAG, "setData():权限名称:", name, ", 开关状态:", isOpen);
        this.nameLabel.string = name;
        this.isOpen = isOpen;
        this.switchSprite.spriteFrame = isOpen ? this.openImg : this.closeImg;
        this.changeCallback = changeCallback;
    }

    private onSwitchClick(): void {
        this.isOpen = !this.isOpen;
        this.switchSprite.spriteFrame = this.isOpen ? this.openImg : this.closeImg;
        this.changeCallback && this.changeCallback(this.isOpen);
    }

    /**
     * 刷新状态
     * @description 单个开关权限后,如果同步服务端失败，则将状态回退
     * @param targetName 目标权限名称
     * @param isOpen 是否开启
     */
    public backToLastStatus(targetName: string): void {
        if (targetName === this.nameLabel.string) {
            console.log(TAG, "backToLastStatus() 回退状态: 权限名称：", targetName, ",开关状态：", this.isOpen);
            this.isOpen = !this.isOpen;
            this.switchSprite.spriteFrame = this.isOpen ? this.openImg : this.closeImg;
        }
    }
}
