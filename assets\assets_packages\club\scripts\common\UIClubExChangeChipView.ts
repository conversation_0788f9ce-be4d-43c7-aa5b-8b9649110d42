import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UICurrencyDiam from "../../../okgame/common/scripts/UICurrencyDiam";
import { okpb } from "../../../okgame/proto/proto_msg";
import { OKGameEvent } from "../../../okgame/public/OKGameConst";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import ClubManger, { CLUB_EVENT } from "../ClubManger";
import UIClubExChangeChipView_auto from "./UIClubExChangeChipView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubExChangeChipView extends UBaseDialog {

	protected ui: UIClubExChangeChipView_auto = null;

	private diamondUnit: number = 0;
	private exchangeChips: number = 0;

	onUILoad(): void {
		this.ui = new UIClubExChangeChipView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.onRegisterEvent(this.ui.btnOK, this.onBtnOK.bind(this));
	}

	onShow(diamondUnit: number, exchangeChips: number) {
		this.diamondUnit = diamondUnit;
		this.exchangeChips = exchangeChips;
		this.ui.currencyDiamn.getComponent(UPrefabContainer).getNodeComponent(UICurrencyDiam).setAddBtnActive(false);
		this.ui.labelTextTips1.getComponent(cc.Label).string = ULanguage.getInstance().getLangByID("club.exchangeChipTips1", diamondUnit, exchangeChips);
	}

	onEditDiamondChange(editbox: cc.EditBox) {
		let text = editbox.string;
		if (text.length > 0 && !this.isOnlyNumber(text)) {
			this.ui.EditBoxDiamond.getComponent(cc.EditBox).string = "";
			this.ui.EditBoxDiamond.getComponent(cc.EditBox).placeholder = "0";
			this.ui.labelChip.getComponent(cc.Label).string = "0";
			return;
		}
		let num = Number(text);
		let userDiamond = this.ui.currencyDiamn.getComponent(UPrefabContainer).getNodeComponent(UICurrencyDiam).getDiamond();
		//钻石需要输入整百且大于等于最小的筹码数值
		if (num % 100 != 0 || num < this.diamondUnit || num > userDiamond) {
			this.ui.EditBoxDiamond.getComponent(cc.EditBox).string = "";
			this.ui.EditBoxDiamond.getComponent(cc.EditBox).placeholder = "0";
			this.ui.labelChip.getComponent(cc.Label).string = "0";
			return;
		}
		this.ui.labelChip.getComponent(cc.Label).string = "" + ((this.exchangeChips / this.diamondUnit) * num);
	}

	isOnlyNumber(str: string): boolean {
		return /^\d+$/.test(str);
	}

	async onBtnOK() {
		let edit = this.ui.EditBoxDiamond.getComponent(cc.EditBox).string;
		let diamond = Number(edit);
		let data: okpb.RspExchangeClubCounterChips = await ClubAPI.getInstance().ReqExchangeClubCounterChips(diamond);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode == okpb.ERET.OK) {
			OKGameManager.getInstance().showToast("兑换成功");
			this.emitEvent(OKGameEvent.UPDATE_USERDIAMOND);
			ClubDataManager.getInstance().requestClubCounterInfo();

		}
	}
}