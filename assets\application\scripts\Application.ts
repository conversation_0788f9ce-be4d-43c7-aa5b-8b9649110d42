import AppInfo from "./AppInfo";
import AppConfig from "./framwork/AppConfig";
import AppErrorCatch from "./framwork/AppErrorCatch";
import PlatUtils from "./framwork/platform/PlatUtils";
import { TextEncoder, TextDecoder } from 'text-encoding';
import ULanguageConfig from "./framwork/ULanguageConfig";

const { ccclass, property } = cc._decorator;
/**
 * 程序启动类
 */
@ccclass
export default class Application extends cc.Component {
    @property(cc.Node)
    view: cc.Node = null;
    onLoad() {
        //初始化平台
        cc.js.protobufJs = require("protobufjs/minimal");
        cc.js.md5 = require("md5");
        cc.js.cryptoJs = require("crypto-js");
        cc.js.fingerprintjs = require("fingerprintjs");
        cc.js.pako = require("pako");
        cc.js.qrcode = require("qrcode");
        cc.js.jsencrypt = require('jsencrypt');
        cc.js.textencoder = new TextEncoder();
        cc.js.textdecoder = new TextDecoder('utf-8');
        cc.debug.setDisplayStats(false);
        //---
        if (AppInfo.isOpen) {
            console.warn = () => { };
            console.log = () => { };
        }
        else {
            AppErrorCatch.getInstance()
        }
        //初始平台信息
        this.initAppInfo();
        AppConfig.getInstance().init(AppInfo)
    }

    fitNode(node: cc.Node) {
        if (cc.isValid(node)) {
            let ds: cc.Size = cc.view.getDesignResolutionSize();
            if (cc.winSize.width > ds.width) node.width = cc.winSize.width;
            if (cc.winSize.height > ds.height) node.height = cc.winSize.height;
        }
    }

    start() {
        this.startApp();
    }

    initAppInfo() {
        PlatUtils.init()
        //----
        if (!AppInfo.isOpen) {
            AppInfo.isDev = PlatUtils.isDev;
        }
        let deviceInfo = PlatUtils.deviceInfo;
        if (deviceInfo.version) {
            AppInfo.appVersion = deviceInfo.version;
        }
        if (deviceInfo.versionCode) {
            AppInfo.versionCode = deviceInfo.versionCode;
        }
        let appConfig = PlatUtils.appConfig;
        if (appConfig.channel) {
            AppInfo.channel = appConfig.channel;
        }
        ULanguageConfig.getInstance().init();
    }

    async startApp() {
        //加载配置
        if (cc.sys.isNative) {

            this.entryApp();
        } else {
            //进入游戏
            this.entryApp();
        }
    }

    async entryApp() {
        await new Promise((resolve) => {
            cc.assetManager.loadBundle('com', (err, bundle) => {
                resolve(true)
            });
        })
        await new Promise((resolve) => {
            cc.assetManager.loadBundle('okgame', (err, bundle) => {
                resolve(true)
            });
        })
        cc.director.loadScene('LoadingScene');
    }
}
