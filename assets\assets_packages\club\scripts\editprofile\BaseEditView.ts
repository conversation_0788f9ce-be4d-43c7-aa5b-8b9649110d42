import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";

const { ccclass, property } = cc._decorator;
const TAG = "[BaseEditView]";

@ccclass
export default class BaseEditView extends UBaseDialog {
    /**
     * 编辑俱乐部信息请求
     * @param clubInfo 俱乐部信息
     * @param updateCallback 更新回调
     */
    requestEditProfile(clubInfo: okpb.ClubResp, updateCallback: Function) {
        const clubId = clubInfo.clubId;
        const clubIconImageUrl = clubInfo.icon;
        const clubName = clubInfo.clubName;
        const announcement = clubInfo.intro;
        // 执行网络请求，更新俱乐部名称or俱乐部公告
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqClubEdit(clubId, clubIconImageUrl, clubName, announcement)
            .then((res: okpb.RspJoinClub) => {
                console.log(TAG, "RspClubEdit:返回了:  ", JSON.stringify(res));
                if (res.errorCode === 0) {
                    this.showThisToast("common.editSuccess");
                    const newClubInfo = !res?.data ? clubInfo : res.data;
                    if (updateCallback) {
                        updateCallback(newClubInfo);
                    }
                } else {
                    // TODO: 钻石不足的提示要做，并跳转到商城页面
                    this.showThisToast("errorCode." + res.errorCode);
                }
            })
            .catch((err) => {
                console.error(TAG, "ReqClubEdit error: ", err);
                this.showThisToast("errorCode.1010");
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }

    public showThisToast(msgKey: string) {
        OKGameManager.getInstance().showToastById(msgKey);
    }
}
