const TAG = "[ScrollViewHelper]";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ScrollViewHelper {
    @property({ type: cc.ScrollView })
    private scrollView: cc.ScrollView = null;

    /**
     * 防抖相关的变量
     */
    private isRequesting: boolean = false;
    private debounceTimer: NodeJS.Timeout | null = null;
    private readonly DEBOUNCE_DELAY: number = 300;

    /**
     * 分页相关的变量
     */
    private currentPage: number = 1;
    private readonly PAGE_SIZE: number = 10;
    private totalPage: number = 3;

    /**
     * 上拉更多、下拉刷新相关
     */
    private isScrollToTop: boolean = false;
    private isScrollToBottom: boolean = false;
    private scrollViewInitY: number = 0;

    /**
     * 回调函数
     */
    private onRefreshCallback: () => void = null;
    private onLoadMoreCallback: () => void = null;

    /**
     * 初始化
     * @param scrollView ScrollView组件
     * @param onRefresh 下拉刷新回调
     * @param onLoadMore 上拉加载更多回调
     */
    public init(scrollView: cc.ScrollView, onRefresh: () => void, onLoadMore: () => void): void {
        this.scrollView = scrollView;
        this.onRefreshCallback = onRefresh;
        this.onLoadMoreCallback = onLoadMore;

        // 确保 ScrollView 支持弹性滚动
        this.scrollView.elastic = true;
        this.scrollView.bounceDuration = 0.5;

        this.scrollViewInitY = this.scrollView.content.position.y;

        this.initEvent();
    }

    /**
     * 初始化事件监听
     */
    private initEvent(): void {
        this.scrollView.node.on("scroll-to-top", this.scrollToTop, this);
        this.scrollView.node.on("scroll-to-bottom", this.scrollToBottom, this);
        this.scrollView.node.on("scroll-ended", this.scrollEnded, this);
        this.scrollView.node.on("scrolling", this.scrolling, this);
    }

    /**
     * 清理事件监听
     */
    public destroy(): void {
        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        this.scrollView.node.off("scroll-to-top", this.scrollToTop, this);
        this.scrollView.node.off("scroll-to-bottom", this.scrollToBottom, this);
        this.scrollView.node.off("scroll-ended", this.scrollEnded, this);
        this.scrollView.node.off("scrolling", this.scrolling, this);
    }

    /**
     * 设置总页数
     */
    public setTotalPage(totalPage: number): void {
        this.totalPage = totalPage;
    }

    /**
     * 重置页码
     */
    public resetPage(): void {
        this.currentPage = 1;
    }

    /**
     * 获取当前页码
     */
    public getCurrentPage(): number {
        return this.currentPage;
    }

    /**
     * 设置是否正在请求
     */
    public setIsRequesting(isRequesting: boolean): void {
        this.isRequesting = isRequesting;
    }

    /**
     * 滑动中
     */
    private scrolling(event: cc.Event.EventTouch): void {
        // 滑动到顶部，再下滑一段距离后才认定是下拉刷新
        if (this.scrollView.content.position.y < this.scrollViewInitY - 200) {
            console.log(TAG, "现在才算下拉刷新");
            this.isScrollToTop = true;
        }
    }

    /**
     * 滑动到顶部
     */
    private scrollToTop(): void {
        console.log(TAG, "scrollToTop() ");
    }

    /**
     * 滑动到底部
     */
    private scrollToBottom(): void {
        console.log(TAG, "scrollToBottom() ");
        this.isScrollToBottom = true;
    }

    /**
     * 滚动结束
     */
    private scrollEnded(): void {
        console.log(TAG, "scrollEnded()");
        if (this.isScrollToTop) {
            console.log(TAG, "scrollEnded() 触发 下拉刷新");
            if (this.onRefreshCallback) {
                this.onRefreshCallback();
            }
        } else if (this.isScrollToBottom) {
            console.log(TAG, "scrollEnded() 触发 上拉加载更多");
            if (this.currentPage < this.totalPage && !this.isRequesting) {
                this.currentPage++;
                if (this.onLoadMoreCallback) {
                    this.onLoadMoreCallback();
                }
            }
        }
        this.isScrollToTop = false;
        this.isScrollToBottom = false;
    }
}
