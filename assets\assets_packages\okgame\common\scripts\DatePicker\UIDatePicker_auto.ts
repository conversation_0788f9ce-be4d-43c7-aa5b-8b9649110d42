export default class UIDatePicker_auto {
    node:cc.Node = null;   
	DatePicker: cc.Node;
	maskNode: cc.Node;
	contentNode: cc.Node;
	panelTitle: cc.Node;
	weekTextLayout: cc.Node;
	WeekText7: cc.Node;
	WeekText1: cc.Node;
	WeekText2: cc.Node;
	WeekText3: cc.Node;
	WeekText4: cc.Node;
	WeekText5: cc.Node;
	WeekText6: cc.Node;
	datePickerTitleNode: cc.Node;
	DatePickerTitleBg: cc.Node;
	dateTitle: cc.Node;
	DataScroll: cc.Node;
	DataScrollMask: cc.Node;
	DataScrollContnet: cc.Node;
	btnClose: cc.Node;
	btnCloseImage: cc.Node;
	btnConfim: cc.Node;
	btnConfimTexte: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.DatePicker = this.node;
		this.maskNode = this.DatePicker.getChildByName("maskNode");
		this.contentNode = this.DatePicker.getChildByName("contentNode");
		this.panelTitle = this.contentNode.getChildByName("panelTitle");
		this.weekTextLayout = this.contentNode.getChildByName("weekTextLayout");
		this.WeekText7 = this.weekTextLayout.getChildByName("WeekText7");
		this.WeekText1 = this.weekTextLayout.getChildByName("WeekText1");
		this.WeekText2 = this.weekTextLayout.getChildByName("WeekText2");
		this.WeekText3 = this.weekTextLayout.getChildByName("WeekText3");
		this.WeekText4 = this.weekTextLayout.getChildByName("WeekText4");
		this.WeekText5 = this.weekTextLayout.getChildByName("WeekText5");
		this.WeekText6 = this.weekTextLayout.getChildByName("WeekText6");
		this.datePickerTitleNode = this.contentNode.getChildByName("datePickerTitleNode");
		this.DatePickerTitleBg = this.datePickerTitleNode.getChildByName("DatePickerTitleBg");
		this.dateTitle = this.datePickerTitleNode.getChildByName("dateTitle");
		this.DataScroll = this.contentNode.getChildByName("DataScroll");
		this.DataScrollMask = this.DataScroll.getChildByName("DataScrollMask");
		this.DataScrollContnet = this.DataScrollMask.getChildByName("DataScrollContnet");
		this.btnClose = this.contentNode.getChildByName("btnClose");
		this.btnCloseImage = this.btnClose.getChildByName("btnCloseImage");
		this.btnConfim = this.contentNode.getChildByName("btnConfim");
		this.btnConfimTexte = this.btnConfim.getChildByName("btnConfimTexte");

    }
}
