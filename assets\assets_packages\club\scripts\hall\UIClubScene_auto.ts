export default class UIClubScene_auto {
    node:cc.Node = null;   
	Canvas: cc.Node;
	MainCamera: cc.Node;
	background: cc.Node;
	contentView: cc.Node;
	topView: cc.Node;
	topViewBg: cc.Node;
	closeBtn: cc.Node;
	closeImg: cc.Node;
	panelTitle: cc.Node;
	centerView: cc.Node;
	clubInfoNode: cc.Node;
	ClubTableView: cc.Node;
	clubTableBtn: cc.Node;
	clubTableIcon: cc.Node;
	menuView: cc.Node;
	menuBg: cc.Node;
	btnMessage: cc.Node;
	btnMessageIcon: cc.Node;
	btnMessageText: cc.Node;
	btnMember: cc.Node;
	btnMemberIcon: cc.Node;
	btnMemberText: cc.Node;
	redPotImg: cc.Node;
	btnCashier: cc.Node;
	btnCashierIcon: cc.Node;
	btnCashierText: cc.Node;
	btnData: cc.Node;
	btnDataIcon: cc.Node;
	btnDataText: cc.Node;
	btnAdmin: cc.Node;
	btnAdminIcon: cc.Node;
	btnAdminText: cc.Node;
	btnMenu: cc.Node;
	btnMenuIcon: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		let parent = this.node.getParent();
		this.Canvas = parent.getChildByName("Canvas");
		this.MainCamera = this.Canvas.getChildByName("MainCamera");
		this.background = this.Canvas.getChildByName("background");
		this.contentView = this.Canvas.getChildByName("contentView");
		this.topView = this.contentView.getChildByName("topView");
		this.topViewBg = this.topView.getChildByName("topViewBg");
		this.closeBtn = this.topView.getChildByName("closeBtn");
		this.closeImg = this.closeBtn.getChildByName("closeImg");
		this.panelTitle = this.topView.getChildByName("panelTitle");
		this.centerView = this.contentView.getChildByName("centerView");
		this.clubInfoNode = this.centerView.getChildByName("clubInfoNode");
		this.ClubTableView = this.centerView.getChildByName("ClubTableView");
		this.clubTableBtn = this.centerView.getChildByName("clubTableBtn");
		this.clubTableIcon = this.clubTableBtn.getChildByName("clubTableIcon");
		this.menuView = this.centerView.getChildByName("menuView");
		this.menuBg = this.menuView.getChildByName("menuBg");
		this.btnMessage = this.menuBg.getChildByName("btnMessage");
		this.btnMessageIcon = this.btnMessage.getChildByName("btnMessageIcon");
		this.btnMessageText = this.btnMessage.getChildByName("btnMessageText");
		this.btnMember = this.menuBg.getChildByName("btnMember");
		this.btnMemberIcon = this.btnMember.getChildByName("btnMemberIcon");
		this.btnMemberText = this.btnMember.getChildByName("btnMemberText");
		this.redPotImg = this.btnMember.getChildByName("redPotImg");
		this.btnCashier = this.menuBg.getChildByName("btnCashier");
		this.btnCashierIcon = this.btnCashier.getChildByName("btnCashierIcon");
		this.btnCashierText = this.btnCashier.getChildByName("btnCashierText");
		this.btnData = this.menuBg.getChildByName("btnData");
		this.btnDataIcon = this.btnData.getChildByName("btnDataIcon");
		this.btnDataText = this.btnData.getChildByName("btnDataText");
		this.btnAdmin = this.menuBg.getChildByName("btnAdmin");
		this.btnAdminIcon = this.btnAdmin.getChildByName("btnAdminIcon");
		this.btnAdminText = this.btnAdmin.getChildByName("btnAdminText");
		this.btnMenu = this.menuBg.getChildByName("btnMenu");
		this.btnMenuIcon = this.btnMenu.getChildByName("btnMenuIcon");

    }
}
