import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameData from "../../../okgame/public/OKGameData";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import AgentView from "./agent/AgentView";
import ApplyView from "./apply/ApplyView";
import ClubMessageManager from "./apply/ClubMessageManager";
import MemberView from "./member/MemberView";
import MemberConst from "./MemberConst";
import { IClubCompleteInfo } from "./MemberTypes";

const { ccclass, property } = cc._decorator;

const TAG = "[MembersMainView]";

/**
 * 标签页类型枚举
 */
enum TabType {
    MEMBER = "member",
    AGENT = "agent",
    APPLY = "apply",
}

/**
 * 成员-主界面
 */
@ccclass
export default class MembersMainView extends UBaseDialog {
    @property(cc.Sprite)
    backNode: cc.Sprite = null;

    @property({ type: cc.Node, tooltip: "成员View" })
    memberContainerNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "成员Label" })
    memberLabel: cc.Label = null;

    @property({ type: cc.Toggle, tooltip: "成员Toggle" })
    memberToggle: cc.Toggle = null;

    @property({ type: cc.Node, tooltip: "代理Toggle" })
    agentContainerNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "代理Label" })
    agentLabel: cc.Label = null;

    @property({ type: cc.Toggle, tooltip: "代理Toggle" })
    agentToggle: cc.Toggle = null;

    @property({ type: cc.Node, tooltip: "申请View" })
    applyContainerNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "申请Label" })
    applyLabel: cc.Label = null;

    @property({ type: cc.Toggle, tooltip: "申请Toggle" })
    applyToggle: cc.Toggle = null;

    @property({ type: cc.Label, tooltip: "在线人数" })
    onlineNumTxt: cc.Label = null;

    @property({ type: cc.Sprite, tooltip: "申请红点" })
    applyRedDotImg: cc.Sprite = null;
    /**
     * 排序选中图片
     */
    @property({ type: cc.SpriteFrame, tooltip: "排序选中图片" })
    sortUpImg: cc.SpriteFrame = null;
    /**
     * 排序向下图片
     */
    @property({ type: cc.SpriteFrame, tooltip: "排序向下图片" })
    sortDownImg: cc.SpriteFrame = null;
    /**
     * 排序灰色图片
     */
    @property({ type: cc.SpriteFrame, tooltip: "排序灰色图片" })
    sortGrayImg: cc.SpriteFrame = null;
    /**
     * 删除按钮
     */
    @property({ type: cc.Node, tooltip: "删除按钮" })
    deleteBtnNode: cc.Node = null;
    /**
     * 俱乐部信息
     */
    private clubInfo: IClubCompleteInfo = null;

    /**
     * 标签页配置: 成员、代理、消息
     */
    private tabConfig: {
        [key in TabType]: {
            container: cc.Node;
            toggle: cc.Toggle;
            label: cc.Label;
        };
    };

    onUILoad(): void {
        console.log(TAG, "onUILoad()");
        // 初始化标签页配置
        this.tabConfig = {
            [TabType.MEMBER]: {
                container: this.memberContainerNode,
                toggle: this.memberToggle,
                label: this.memberLabel,
            },
            [TabType.AGENT]: {
                container: this.agentContainerNode,
                toggle: this.agentToggle,
                label: this.agentLabel,
            },
            [TabType.APPLY]: {
                container: this.applyContainerNode,
                toggle: this.applyToggle,
                label: this.applyLabel,
            },
        };

        this.initView();
        this.initEvent();
    }

    onUIDestroy(): void {
        ClubMessageManager.getInstance().unsubscribe(this.messageCountChangeCallback.bind(this));
        super.onUIDestroy();
    }

    onEnable(): void {
        console.log(TAG, "onEnable()");
    }

    onShow(): void {
        this.clubInfo = ClubDataManager.getInstance().getClubInfo();
        console.log(TAG, "onShow() 成员界面初始化，俱乐部信息:", JSON.stringify(this.clubInfo));

        // 初始化消息模块，用于获取申请消息，如果有则展示小红点
        this.initApplyMsg();

        this.showTabMember();
    }

    initView(): void {
        const count = ClubMessageManager.getInstance().getMessageCount();
        this.applyRedDotImg.node.active = count > 0;
    }

    initEvent(): void {
        this.onRegisterEvent(this.memberToggle.node, this.onMembersTabClick.bind(this));
        this.onRegisterEvent(this.agentToggle.node, this.onAgentTabClick.bind(this));
        this.onRegisterEvent(this.applyToggle.node, this.onApplyTabClick.bind(this));
        this.onRegisterEvent(this.backNode.node, this.onBackBtnClick.bind(this));
    }

    private onBackBtnClick(): void {
        OKGameManager.getInstance().showClubScene(ClubDataManager.getInstance().fullClubInfoToClubResp(this.clubInfo));
    }

    private onMembersTabClick(): void {
        this.showTabMember();
    }

    private onAgentTabClick(): void {
        this.showTabAgent();
    }

    private onApplyTabClick(): void {
        this.showTabApply();
    }
    /**
     * 切换标签页
     * @param tabType 要切换到的标签页类型
     */
    private switchTab(tabType: TabType): void {
        if (!this.tabConfig) {
            console.warn(TAG, "tabConfig 未初始化");
            return;
        }

        Object.entries(this.tabConfig).forEach(([type, config]) => {
            if (!config || !config.container || !config.toggle || !config.label) {
                console.warn(TAG, `标签页 ${type} 配置不完整`);
                return;
            }
            const isActive = type === tabType;
            config.container.active = isActive;
            config.toggle.isChecked = isActive;
            config.label.node.color = isActive ? MemberConst.COLOR_WHITE : MemberConst.COLOR_GRAY;
        });
    }

    private showTabMember(): void {
        this.switchTab(TabType.MEMBER);
        const memberScript = this.memberContainerNode.getChildByName("MemberView").getComponent(MemberView);
        if (memberScript) {
            memberScript.showMemberView((onlineCount) => {
                this.onlineNumTxt.string = onlineCount.toString();
            });
        }
    }

    private showTabAgent(): void {
        this.switchTab(TabType.AGENT);
        const agentScript = this.agentContainerNode.getChildByName("AgentView").getComponent(AgentView);
        if (agentScript) {
            agentScript.showAgentView();
        }
    }

    private showTabApply(): void {
        this.switchTab(TabType.APPLY);
        const applyScript = this.applyContainerNode.getChildByName("ApplyView").getComponent(ApplyView);
        if (applyScript) {
            applyScript.showApplyView();
        }
    }

    /**
     * 获取申请消息，如果有则展示小红点
     */
    private initApplyMsg(): void {
        if (!this.clubInfo) {
            console.log(TAG, "clubInfo为空,无法获取申请消息");
            return;
        }
        ClubMessageManager.getInstance().subscribe(this.messageCountChangeCallback.bind(this));

        const FIRST_PAGE = 1;
        ClubMessageManager.getInstance()
            .fetchMessageList(this.clubInfo.clubId, FIRST_PAGE, true)
            .then((messageList) => {});
    }

    /**
     * 消息数量变化回调
     * @param count 消息数量
     * @param messageList 消息列表
     */
    private messageCountChangeCallback(count: number, messageList: okpb.JoinClubReqInfo[]): void {
        if (cc.isValid(this.node)) {
            if (count > 0) {
                this.applyRedDotImg.node.active = true;
            } else {
                this.applyRedDotImg.node.active = false;
            }
        }
    }
}
