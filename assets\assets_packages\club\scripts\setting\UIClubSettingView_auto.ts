export default class UIClubSettingView_auto {
    node:cc.Node = null;   
	ClubSettingView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	navContainer: cc.Node;
	titleLabel: cc.Node;
	backButton: cc.Node;
	backButtonBg: cc.Node;
	careerContainer: cc.Node;
	careerTopNode: cc.Node;
	careerDayLabel: cc.Node;
	careerWeekLabel: cc.Node;
	careerLastWeekLabel: cc.Node;
	careerTotalLabel: cc.Node;
	careerDataNode: cc.Node;
	funcContainer: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubSettingView = this.node;
		this.mask = this.ClubSettingView.getChildByName("mask");
		this.mainContainer = this.ClubSettingView.getChildByName("mainContainer");
		this.navContainer = this.mainContainer.getChildByName("navContainer");
		this.titleLabel = this.navContainer.getChildByName("titleLabel");
		this.backButton = this.navContainer.getChildByName("backButton");
		this.backButtonBg = this.backButton.getChildByName("backButtonBg");
		this.careerContainer = this.mainContainer.getChildByName("careerContainer");
		this.careerTopNode = this.careerContainer.getChildByName("careerTopNode");
		this.careerDayLabel = this.careerTopNode.getChildByName("careerDayLabel");
		this.careerWeekLabel = this.careerTopNode.getChildByName("careerWeekLabel");
		this.careerLastWeekLabel = this.careerTopNode.getChildByName("careerLastWeekLabel");
		this.careerTotalLabel = this.careerTopNode.getChildByName("careerTotalLabel");
		this.careerDataNode = this.careerContainer.getChildByName("careerDataNode");
		this.funcContainer = this.mainContainer.getChildByName("funcContainer");

    }
}
