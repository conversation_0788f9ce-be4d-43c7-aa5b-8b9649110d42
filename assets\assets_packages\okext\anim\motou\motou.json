{"skeleton": {"hash": "REsbw+cuzN6gshsX6VbNEOxyQkk", "spine": "3.6.38", "width": 393, "height": 264, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 24.32, "y": 147.91}, {"name": "bone2", "parent": "bone", "length": 96.77, "rotation": -136.67, "x": 147.55, "y": 126.53}, {"name": "bone3", "parent": "bone2", "length": 130.04, "rotation": -20.3, "x": 120.85, "y": 7.64}, {"name": "bone4", "parent": "bone3", "length": 66.17, "rotation": 21.21, "x": 10.15, "y": -44.95}, {"name": "bone5", "parent": "bone4", "length": 75.4, "rotation": -8.77, "x": 66.17}, {"name": "bone6", "parent": "bone3", "length": 57.52, "rotation": 5.73, "x": 135.03, "y": -17.61}, {"name": "bone7", "parent": "bone6", "length": 92.45, "rotation": 11.93, "x": 57.52}], "slots": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "root", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "damuz<PERSON>", "bone": "root", "attachment": "damuz<PERSON>"}], "skins": {"default": {"damuzhi": {"damuzhi": {"type": "mesh", "uvs": [0.36467, 0.23383, 0.49615, 0.0975, 0.66519, 0.00145, 0.85067, 0, 0.96806, 0.04793, 1, 0.16257, 1, 0.34227, 0.89528, 0.55915, 0.74502, 0.71407, 0.52667, 0.78843, 0.38111, 0.92476, 0.17919, 0.99999, 0, 1, 0, 0.73576, 0.13224, 0.58084, 0.24728, 0.42593, 0.77494, 0.25348, 0.56078, 0.42306, 0.38945, 0.62442, 0.21277, 0.79047], "triangles": [16, 2, 3, 5, 16, 3, 1, 2, 16, 16, 5, 6, 0, 1, 17, 5, 3, 4, 18, 15, 17, 14, 15, 18, 9, 18, 17, 19, 14, 18, 10, 19, 18, 13, 14, 19, 9, 10, 18, 19, 12, 13, 11, 19, 10, 11, 12, 19, 17, 1, 16, 15, 0, 17, 7, 16, 6, 17, 16, 7, 8, 17, 7, 8, 9, 17], "vertices": [3, 4, 61.69, -39.29, 0.01949, 5, 1.57, -39.52, 0.31385, 2, 177.09, -76.35, 0.66667, 1, 2, 150.28, -73.93, 1, 1, 2, 122.44, -63.78, 1, 1, 2, 100.6, -43.42, 1, 1, 2, 90.86, -26.19, 1, 1, 2, 96.72, -12.49, 1, 3, 4, -2.37, 41.54, 0.32798, 5, -74.07, 30.6, 0.00535, 2, 111.76, 3.46, 0.66667, 3, 4, 28.17, 48.74, 0.59477, 5, -44.99, 42.37, 0.0719, 2, 142.18, 11.14, 0.33333, 2, 4, 58.69, 45.4, 0.64773, 5, -14.32, 43.73, 0.35227, 2, 4, 90.21, 27.38, 0.31975, 5, 19.58, 30.73, 0.68025, 2, 4, 118.6, 22.95, 0.05296, 5, 48.32, 30.68, 0.94704, 1, 5, 80.12, 19.3, 1, 1, 5, 103.62, 2.56, 1, 1, 5, 84.92, -23.7, 1, 2, 4, 118.04, -35.06, 0.01949, 5, 56.61, -26.74, 0.98051, 3, 4, 91.58, -35.69, 0.01949, 5, 30.56, -31.39, 0.64718, 2, 206.92, -72.27, 0.33333, 1, 4, 16.04, 8.5, 1, 1, 4, 55.18, -0.73, 1, 1, 5, 25.97, 1.61, 1, 1, 5, 60.89, 1.61, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 16, 18], "width": 161, "height": 122}}, "shouzhang": {"shouzhang": {"type": "mesh", "uvs": [0.74143, 0, 0.85362, 1e-05, 0.9556, 0.08166, 0.99999, 0.19362, 0.99999, 0.30559, 0.92147, 0.42939, 0.81652, 0.55052, 0.75801, 0.67185, 0.67259, 0.76863, 0.54384, 0.81987, 0.4342, 0.83695, 0.2774, 1, 0.04539, 1, 0, 0.93943, 1e-05, 0.75535, 0.06196, 0.60163, 0.141, 0.48397, 0.23533, 0.3796, 0.36536, 0.31318, 0.51069, 0.23537, 0.63307, 0.15757, 0.73493, 0.24923, 0.80447, 0.3748, 0.81895, 0.13387, 0.89408, 0.26587, 0.65068, 0.41328, 0.35388, 0.62045, 0.5226, 0.51962, 0.19713, 0.78261], "triangles": [28, 15, 26, 14, 15, 28, 13, 14, 28, 12, 13, 28, 11, 28, 10, 12, 28, 11, 26, 17, 18, 26, 18, 27, 16, 17, 26, 26, 15, 16, 10, 28, 26, 18, 19, 27, 27, 19, 25, 7, 25, 6, 27, 25, 7, 8, 27, 7, 9, 27, 8, 26, 27, 9, 9, 10, 26, 23, 0, 1, 23, 1, 2, 20, 0, 23, 21, 20, 23, 3, 24, 23, 3, 23, 2, 21, 23, 24, 4, 24, 3, 22, 21, 24, 21, 19, 20, 25, 21, 22, 25, 19, 21, 5, 24, 4, 22, 24, 5, 6, 22, 5, 25, 22, 6], "vertices": [2, 2, 22.47, -67.33, 0.99998, 6, -208.96, -66.3, 2e-05, 1, 2, -9.6, -37.08, 1, 1, 2, -23.97, 6.1, 1, 1, 2, -16.37, 39.58, 1, 2, 2, 3.91, 61.08, 0.99772, 3, -128.22, 9.55, 0.00228, 2, 2, 48.78, 63.68, 0.90564, 3, -87.04, 27.56, 0.09436, 2, 2, 100.73, 58.65, 0.3407, 3, -36.57, 40.85, 0.6593, 2, 2, 139.43, 66.17, 0.02486, 3, -2.88, 61.34, 0.97514, 1, 3, 38.01, 71.72, 1, 2, 3, 89.87, 64.38, 0.99999, 7, -74.44, 103.71, 1e-05, 3, 3, 131.28, 51.68, 0.82821, 6, 3.2, 69.32, 0.136, 7, -38.83, 79.05, 0.03579, 3, 3, 204.83, 67.19, 0.30499, 6, 77.93, 77.4, 0.08, 7, 35.96, 71.52, 0.61501, 1, 7, 105.1, 12.08, 1, 1, 7, 108.21, -11.68, 1, 2, 6, 142.42, -31.66, 0.02028, 7, 76.52, -48.53, 0.97972, 2, 6, 101.56, -55.53, 0.291, 7, 31.61, -63.43, 0.709, 2, 6, 59.38, -67.81, 0.73659, 7, -12.2, -66.73, 0.26341, 3, 2, 235.93, -130.9, 0.00096, 6, 13.62, -74.13, 0.96308, 7, -58.27, -63.46, 0.03596, 3, 2, 186.72, -108.59, 0.41664, 3, 102.1, -86.16, 0.16736, 6, -39.61, -64.92, 0.416, 3, 2, 131.08, -84.34, 0.66508, 3, 41.5, -82.73, 0.23479, 6, -99.56, -55.45, 0.10012, 3, 2, 81.99, -66.29, 0.93777, 3, -10.8, -82.82, 0.01937, 6, -151.61, -50.32, 0.04286, 2, 2, 69.48, -21.22, 0.99831, 6, -175.06, -9.85, 0.00169, 2, 2, 72.34, 21.65, 0.75773, 3, -50.36, -3.69, 0.24227, 1, 2, 24.56, -20.71, 1, 2, 2, 26.99, 24.89, 0.99106, 3, -94.02, -16.38, 0.00894, 3, 2, 123.28, -12.43, 0.57173, 3, 9.24, -17.98, 0.41431, 6, -125.2, 12.19, 0.01396, 1, 6, 3.37, 4.02, 1, 3, 2, 179.16, -26.54, 0.01675, 3, 66.54, -11.84, 0.91294, 6, -67.57, 12.58, 0.07031, 3, 3, 211.42, 2.03, 0.05034, 6, 77.97, 11.91, 0.12013, 7, 22.47, 7.43, 0.82954], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 8, 10, 10, 12, 40, 42, 12, 44, 44, 42, 34, 52, 52, 20, 24, 56, 56, 52, 54, 52, 50, 54, 20, 22, 22, 24], "width": 393, "height": 264}}}}, "animations": {"animation": {"slots": {"damuzhi": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffff28"}, {"time": 1, "color": "ffffffff"}]}, "shouzhang": {"color": [{"time": 0.0333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0333, "x": 0.263, "y": 0.263}, {"time": 0.8333, "x": 0.873, "y": 0.873}, {"time": 1, "x": 1, "y": 1}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}, {"time": 0.8333, "angle": -13.94}, {"time": 1, "angle": -16.85, "curve": [0.25, 0, 0.762, 0.66]}, {"time": 1.3333, "angle": -8.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -16.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -8.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": -16.85}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.71, 0.64]}, {"time": 1.3333, "x": -44.2, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -44.2, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 1, "x": 1, "y": 0.734}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}, {"time": 0.8333, "angle": -6}, {"time": 1, "angle": -6.33}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0}, {"time": 0.8333, "angle": 8.61}, {"time": 1, "angle": 19.08}, {"time": 1.3333, "angle": 25.65}, {"time": 1.6667, "angle": 19.08}, {"time": 2, "angle": 25.65}, {"time": 2.3333, "angle": 19.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}, {"time": 1, "angle": 6.18}, {"time": 1.3333, "angle": 11.58}, {"time": 1.6667, "angle": 6.91}, {"time": 2, "angle": 11.58}, {"time": 2.3333, "angle": 6.18}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.0333, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}, {"time": 1, "angle": 6.18}, {"time": 1.3333, "angle": 11.58}, {"time": 1.6667, "angle": 6.91}, {"time": 2, "angle": 11.58}, {"time": 2.3333, "angle": 6.18}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.0333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}}, "deform": {"default": {"damuzhi": {"damuzhi": [{"time": 0.8333}, {"time": 1, "vertices": [-0.84645, -2.52991, -0.91793, -2.50494, -0.87567, -1.75797, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.56981, 4.69162, 1.7021, 4.64511, 1.62396, 3.26001, 1.56981, 4.69162, 1.7021, 4.64511, 1.62396, 3.26001, 1.56981, 4.69162, 1.7021, 4.64511, 2.20477, 6.58917, 2.39048, 6.5238, 2.41635, 7.22168, 2.61995, 7.14996, 4.91459, 13.41214, 5.31944, 14.51678, 0, 0, -0.84634, -2.52994, -0.918, -2.505, -1.05801, -3.16238, -1.14744, -3.1312, -1.09457, -2.19743, 0, 0, 0, 0, -5e-05, -6e-05, 1.83576, 5.00977]}]}, "shouzhang": {"shouzhang": [{"time": 0, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "offset": 14, "vertices": [-2.50006, -5.01875, -0.60361, -7.5925, -3.05563, -6.13402, -0.73775, -9.27979, -4.44458, -8.92229, -1.07306, -13.49777, -1.00602, -12.65427, -1.0731, -13.49792, -5.11666, -12.53613, -0.46949, -5.9054, -1.05687, -5.82867, -2.23854, -5.48456, -0.55286, -6.95358, -0.82185, -6.96344, -1.87262, -6.87042, 7.26473, 14.37482, 2.55851, 6.26807, 0.75486, 4.16354, 1.59905, 3.91754, -1.20784, -6.66122, -2.55831, -6.26813, -1.20784, -6.66122, -2.55831, -6.26813, -3.57022, -7.16702, -2.28272, -10.62033, -4.7282, -9.73608, -2.50009, -5.01877, -0.60363, -7.59256, -1.35884, -7.49393, -2.50009, -5.01877, -0.60363, -7.59256, -1.35884, -7.49393, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.88901, 7.80705, 0.93875, 11.81006, 2.11357, 11.65787, 1.73669, 9.57986, 1.94449, 3.90352, 0.46941, 5.90515, 1.05682, 5.82892, 0.95565, 12.02399, 2.15179, 11.86929, 4.55863, 11.16763]}]}}}}}}