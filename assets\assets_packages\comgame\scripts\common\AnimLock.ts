
interface animObj {
    animType: number,
    resolve: Function,
}

/**
 * 动画锁
 */
export default class AnimLock {
    private _animList: animObj[] = [];
    private _curAnim: animObj = null;

    //-----
    lock(animType: number): Promise<animObj> {
        return new Promise((resolve) => {
            if (this._curAnim == null) {
                this._curAnim = { animType, resolve };
                this._curAnim.resolve(this._curAnim)
            } else {
                this._animList.push({ animType, resolve });
            }
        })
    }

    unlock() {
        this._curAnim = null;
        if (this._animList.length == 0) {
            return;
        }
        this._curAnim = this._animList.shift();
        this._curAnim.resolve(this._curAnim)
    }

    check(): boolean {
        return this._curAnim != null;
    }

    has(animType: number): boolean {
        if (this._curAnim && this._curAnim.animType == animType) {
            return true;
        }
        for (let i = 0; i < this._animList.length; i++) {
            if (this._animList[i].animType == animType) {
                return true;
            }
        }
        return false;
    }

    remove(animType: number) {
        let animList = []
        for (let i = 0; i < this._animList.length; i++) {
            if (this._animList[i].animType != animType) {
                animList.push(this._animList[i]);
            }
        }
        this._animList = animList;
        if (this._curAnim && this._curAnim.animType == animType) {
            this.unlock();
        }
    }

    reset() {
        this._curAnim = null;
        this._animList = [];
    }
}
