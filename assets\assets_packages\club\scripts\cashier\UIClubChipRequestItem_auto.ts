export default class UIClubChipRequestItem_auto {
    node:cc.Node = null;   
	ClubChipRequestItem: cc.Node;
	userHeadNode: cc.Node;
	headIcon: cc.Node;
	headFrameIcon: cc.Node;
	userIDLayer: cc.Node;
	userID: cc.Node;
	roleTypeImg: cc.Node;
	CurrencyBgImg: cc.Node;
	clubChipIcon: cc.Node;
	freeTxt: cc.Node;
	labelNoteName: cc.Node;
	labelTime: cc.Node;
	btnOK: cc.Node;
	btnCancel: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubChipRequestItem = this.node;
		this.userHeadNode = this.ClubChipRequestItem.getChildByName("userHeadNode");
		this.headIcon = this.userHeadNode.getChildByName("headIcon");
		this.headFrameIcon = this.userHeadNode.getChildByName("headFrameIcon");
		this.userIDLayer = this.ClubChipRequestItem.getChildByName("userIDLayer");
		this.userID = this.userIDLayer.getChildByName("userID");
		this.roleTypeImg = this.userIDLayer.getChildByName("roleTypeImg");
		this.CurrencyBgImg = this.ClubChipRequestItem.getChildByName("CurrencyBgImg");
		this.clubChipIcon = this.CurrencyBgImg.getChildByName("clubChipIcon");
		this.freeTxt = this.CurrencyBgImg.getChildByName("freeTxt");
		this.labelNoteName = this.ClubChipRequestItem.getChildByName("labelNoteName");
		this.labelTime = this.ClubChipRequestItem.getChildByName("labelTime");
		this.btnOK = this.ClubChipRequestItem.getChildByName("btnOK");
		this.btnCancel = this.ClubChipRequestItem.getChildByName("btnCancel");

    }
}
