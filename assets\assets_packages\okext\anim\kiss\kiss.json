{"skeleton": {"hash": "77UJL77+q84MED7bcsIO66nX+Xk", "spine": "3.6.38", "width": 0, "height": 0, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 203.89, "rotation": 90.17, "y": 119.12}, {"name": "bone2", "parent": "bone", "x": 37.67, "y": -11.36}, {"name": "bone3", "parent": "bone", "x": -50.53, "y": -0.3}, {"name": "bone4", "parent": "root", "length": 88.2, "rotation": 90.38, "x": 331.06, "y": 140.83, "scaleX": 2, "scaleY": 2}, {"name": "bone5", "parent": "root", "length": 88.2, "rotation": 90.38, "x": 358.06, "y": 235.46, "scaleX": 2, "scaleY": 2, "color": "ffffffff"}, {"name": "bone6", "parent": "root", "length": 88.2, "rotation": 90.38, "x": 472.74, "y": 147.27, "scaleX": 2, "scaleY": 2}, {"name": "bone7", "parent": "root", "length": 88.2, "rotation": 90.38, "x": 552.15, "y": 213.03, "scaleX": 2, "scaleY": 2}], "slots": [{"name": "aixin3", "bone": "bone6", "color": "fafcffff"}, {"name": "zuiba2", "bone": "root", "attachment": "zuiba2"}, {"name": "zuiba1", "bone": "root", "attachment": "zuiba1"}, {"name": "aixin", "bone": "bone4", "color": "dee0e3ff"}, {"name": "aixin4", "bone": "bone7", "color": "fafcffff"}, {"name": "aixin2", "bone": "bone5", "color": "dee0e3ff"}], "skins": {"default": {"aixin": {"aixin": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.7, -74.62, -63.69, 76.38, 65.3, 75.52, 64.3, -75.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 129}}, "aixin2": {"aixin": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.7, -74.62, -63.69, 76.38, 65.3, 75.52, 64.3, -75.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 129}}, "aixin3": {"aixin": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.7, -74.62, -63.69, 76.38, 65.3, 75.52, 64.3, -75.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 129}}, "aixin4": {"aixin": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.7, -74.62, -63.69, 76.38, 65.3, 75.52, 64.3, -75.48], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 151, "height": 129}}, "zuiba1": {"zuiba1": {"type": "mesh", "uvs": [0.76141, 0.15678, 0.84341, 0.35436, 0.91641, 0.59656, 1, 0.74953, 1, 0.84832, 0.91046, 0.93405, 0.78626, 0.97715, 0.64259, 0.85757, 0.49635, 0.95721, 0.36153, 0.83557, 0.27587, 0.94125, 0.15832, 0.98519, 0.05439, 0.89425, 0, 0.89205, 0, 0.79096, 0.08398, 0.60931, 0.16198, 0.38623, 0.22798, 0.15359, 0.30041, 0, 0.39041, 0, 0.48041, 0.09304, 0.58341, 0, 0.67041, 0, 0.66975, 0.24634, 0.49516, 0.6905, 0.49464, 0.36201, 0.82111, 0.79388, 0.3017, 0.34694, 0.20488, 0.71451, 0.73092, 0.58799], "triangles": [11, 12, 10, 6, 26, 5, 6, 7, 26, 12, 28, 10, 10, 28, 9, 5, 26, 4, 28, 12, 14, 12, 13, 14, 26, 3, 4, 26, 2, 3, 14, 15, 28, 15, 16, 28, 29, 1, 2, 23, 0, 1, 16, 17, 27, 18, 27, 17, 20, 27, 19, 19, 27, 18, 7, 8, 24, 8, 9, 24, 7, 24, 29, 9, 28, 24, 24, 27, 25, 24, 28, 27, 28, 16, 27, 24, 25, 29, 25, 23, 29, 29, 23, 1, 23, 25, 20, 25, 27, 20, 7, 29, 26, 20, 21, 23, 23, 21, 22, 23, 22, 0, 26, 29, 2], "vertices": [2, 2, 36.25, -63.22, 0.688, 1, 73.91, -74.58, 0.312, 2, 2, 18.19, -86.95, 0.464, 1, 55.86, -98.31, 0.536, 2, 2, -3.91, -108.05, 0.2, 1, 33.76, -119.41, 0.8, 1, 1, 19.76, -143.61, 1, 1, 1, 10.77, -143.58, 1, 1, 1, 3.05, -117.59, 1, 2, 2, -38.43, -70.21, 0.288, 1, -0.76, -81.57, 0.712, 2, 2, -27.42, -28.58, 0.504, 1, 10.24, -39.93, 0.496, 2, 2, -36.36, 13.86, 0.504, 1, 1.3, 2.5, 0.496, 2, 2, -25.18, 52.93, 0.504, 1, 12.49, 41.57, 0.496, 2, 2, -34.72, 77.8, 0.288, 1, 2.95, 66.44, 0.712, 2, 2, -38.61, 111.9, 0.288, 1, -0.95, 100.54, 0.712, 1, 1, 7.42, 130.65, 1, 1, 1, 7.67, 146.43, 1, 1, 1, 16.86, 146.4, 1, 2, 2, -4.34, 133.35, 0.2, 1, 33.32, 121.99, 0.8, 2, 2, 15.89, 110.67, 0.464, 1, 53.55, 99.31, 0.536, 2, 2, 37, 91.47, 0.688, 1, 74.67, 80.11, 0.312, 2, 2, 50.91, 70.42, 0.536, 1, 88.58, 59.06, 0.464, 2, 2, 50.84, 44.32, 0.536, 1, 88.5, 32.96, 0.464, 2, 2, 42.29, 18.25, 0.536, 1, 79.96, 6.89, 0.464, 2, 2, 50.67, -11.65, 0.536, 1, 88.33, -23.01, 0.464, 2, 2, 50.59, -36.88, 0.536, 1, 88.26, -48.24, 0.464, 2, 2, 28.17, -36.62, 0.8072, 1, 65.84, -47.98, 0.1928, 2, 2, -12.09, 14.13, 0.888, 1, 25.57, 2.77, 0.112, 2, 2, 17.8, 14.19, 0.888, 1, 55.47, 2.84, 0.112, 2, 2, -21.78, -80.36, 0.648, 1, 15.88, -91.72, 0.352, 2, 2, 19.34, 70.14, 0.888, 1, 57.01, 58.78, 0.112, 2, 2, -14.02, 98.32, 0.664, 1, 23.64, 86.96, 0.336, 2, 2, -2.97, -54.26, 0.808, 1, 34.7, -65.62, 0.192], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 54, 56, 14, 16, 38, 54, 36, 54], "width": 290, "height": 91}}, "zuiba2": {"zuiba2": {"type": "mesh", "uvs": [0.31514, 0.09943, 0.45408, 0.10323, 0.57668, 0.10512, 0.69418, 0.10323, 0.81269, 0.09943, 0.94857, 0.1165, 1, 0.2303, 0.94653, 0.39909, 0.88727, 0.57736, 0.81984, 0.73478, 0.7306, 0.87019, 0.62948, 0.94608, 0.51058, 0.96839, 0.38593, 0.94618, 0.28173, 0.85022, 0.19624, 0.70264, 0.13804, 0.54057, 0.08237, 0.36442, 0.02163, 0.23289, 0.08628, 0.10512, 0.19969, 0.08995, 0.3566, 0.6505, 0.77082, 0.38808, 0.44434, 0.84921, 0.17243, 0.2393, 0.70281, 0.65645, 0.5243, 0.34862, 0.61166, 0.84417, 0.28359, 0.39056, 0.52513, 0.61158, 0.88524, 0.24249, 0.33066, 0.22814, 0.52713, 0.22254, 0.73092, 0.21489], "triangles": [33, 3, 4, 32, 1, 2, 31, 0, 1, 31, 1, 32, 24, 19, 20, 18, 19, 24, 31, 24, 20, 30, 4, 5, 30, 5, 6, 33, 4, 30, 26, 31, 32, 17, 18, 24, 22, 33, 30, 31, 28, 24, 0, 31, 20, 17, 24, 28, 7, 30, 6, 22, 30, 7, 16, 17, 28, 8, 22, 7, 21, 16, 28, 2, 3, 33, 32, 2, 33, 26, 32, 33, 25, 22, 8, 15, 16, 21, 9, 25, 8, 9, 10, 25, 28, 31, 26, 22, 29, 26, 28, 26, 29, 33, 22, 26, 29, 21, 28, 25, 29, 22, 27, 29, 25, 10, 27, 25, 23, 21, 29, 23, 29, 27, 14, 15, 21, 14, 21, 23, 11, 27, 10, 13, 14, 23, 12, 23, 27, 12, 27, 11, 13, 23, 12], "vertices": [1, 1, 50.55, 66.59, 1, 1, 1, 50.46, 21.71, 1, 1, 1, 50.64, -17.89, 1, 1, 1, 51.45, -55.83, 1, 1, 1, 37.45, -87.27, 1, 1, 1, 25.29, -137.95, 1, 1, 1, 10.89, -154.83, 1, 1, 1, -18.69, -137.93, 1, 2, 3, 0.58, -118.89, 0.192, 1, -49.95, -119.19, 0.808, 2, 3, -27.08, -97.46, 0.44, 1, -77.62, -97.76, 0.56, 2, 3, -51.01, -68.94, 0.592, 1, -101.54, -69.23, 0.408, 2, 3, -64.63, -36.44, 0.6, 1, -115.16, -36.74, 0.4, 2, 3, -69, 1.91, 0.6, 1, -119.53, 1.61, 0.4, 2, 3, -65.64, 42.22, 0.6, 1, -116.18, 41.92, 0.4, 2, 3, -49.37, 76.08, 0.592, 1, -99.91, 75.79, 0.408, 2, 3, -24.05, 104.02, 0.44, 1, -74.58, 103.72, 0.56, 2, 3, 3.91, 123.17, 0.192, 1, -46.62, 122.88, 0.808, 1, 1, -16.2, 141.25, 1, 1, 1, 6.43, 161.15, 1, 1, 1, 29.47, 140.26, 1, 1, 1, 47.74, 102.72, 1, 2, 3, -14.32, 52.34, 0.49995, 1, -64.85, 52.05, 0.50005, 2, 3, 33.03, -80.86, 0.62955, 1, -17.5, -81.16, 0.37045, 1, 3, -48.53, 23.56, 1, 2, 3, 56.47, 112.73, 0.33779, 1, 5.94, 112.44, 0.66221, 2, 3, -13.94, -59.49, 0.40912, 1, -64.47, -59.79, 0.59088, 2, 3, 38.89, -1.15, 0.75532, 1, -11.64, -1.45, 0.24468, 1, 3, -46.97, -30.46, 1, 2, 3, 30.61, 76.5, 0.69455, 1, -19.93, 76.2, 0.30545, 1, 3, -6.86, -2, 1, 2, 3, 58.84, -117.49, 0.29146, 1, 8.3, -117.79, 0.70854, 2, 3, 59.06, 61.65, 0.19618, 1, 8.53, 61.35, 0.80382, 2, 3, 60.84, -1.79, 0.0674, 1, 10.31, -2.09, 0.9326, 2, 3, 63.01, -67.59, 0.15414, 1, 12.47, -67.89, 0.84586], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 0], "width": 299, "height": 174}}}}, "animations": {"idle": {"slots": {"aixin": {"color": [{"time": 0.4, "color": "a1a1a100"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.3333, "color": "a1a1a100"}], "attachment": [{"time": 0.4333, "name": "aixin"}]}, "aixin2": {"color": [{"time": 0.5667, "color": "dfe0e300"}, {"time": 0.7, "color": "fafbffff", "curve": "stepped"}, {"time": 1.1, "color": "fafbffff"}, {"time": 1.3333, "color": "dfe0e300"}], "attachment": [{"time": 0.5667, "name": "aixin"}]}, "aixin3": {"color": [{"time": 0.5, "color": "dee0e300"}, {"time": 0.6, "color": "dee0e3ff", "curve": "stepped"}, {"time": 1.1333, "color": "dee0e3ff"}, {"time": 1.3, "color": "dee0e300"}], "attachment": [{"time": 0.5, "name": "aixin"}]}, "aixin4": {"color": [{"time": 0.6667, "color": "fafcff00"}, {"time": 0.8333, "color": "fafcffff", "curve": "stepped"}, {"time": 1.2, "color": "fafcffff"}, {"time": 1.4333, "color": "fafcff00"}], "attachment": [{"time": 0.6667, "name": "aixin"}]}, "zuiba1": {"color": [{"time": 1.4, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "zuiba2": {"color": [{"time": 1.4, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4333, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.0333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.15, 0.22, 0.75, 1]}, {"time": 0.2333, "x": 1.716, "y": 1.425, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1.716, "y": 1.62, "curve": [0.193, 0.3, 0.75, 1]}, {"time": 0.6, "x": 1.826, "y": 1.723, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1, "y": 1}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4333, "angle": 0, "curve": [0.174, 0.39, 0.75, 1]}, {"time": 0.8, "angle": 13.44, "curve": "stepped"}, {"time": 1.1667, "angle": 13.44}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2333, "x": -14.3, "y": 0.04, "curve": "stepped"}, {"time": 0.4333, "x": -14.3, "y": 0.04, "curve": [0.174, 0.39, 0.75, 1]}, {"time": 0.5667, "x": 36.06, "y": 7.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 11.52, "y": 12}, {"time": 1.1667, "x": -1.2, "y": 12.04}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8, "angle": 2.71, "curve": "stepped"}, {"time": 1.1667, "angle": 2.71}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2333, "x": 20.98, "y": -0.07, "curve": "stepped"}, {"time": 0.4333, "x": 20.98, "y": -0.07, "curve": [0.174, 0.39, 0.75, 1]}, {"time": 0.5667, "x": -8.14, "y": -0.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.1667, "x": 13.02, "y": -0.04}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "bone4": {"translate": [{"time": 0.4333, "x": 0, "y": 0}, {"time": 0.5667, "x": 20.32, "y": 42.86, "curve": [0.231, 0.16, 0.75, 1]}, {"time": 1.3333, "x": 131.75, "y": 150.16}], "scale": [{"time": 0.4333, "x": 0.034, "y": 0.034}, {"time": 0.5667, "x": 1.115, "y": 1.253, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1.135, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0.983, "y": 1.074, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1, "y": 1}]}, "bone5": {"rotate": [{"time": 0.5667, "angle": 0}], "translate": [{"time": 0.5667, "x": -74.52, "y": 0}, {"time": 1.3333, "x": 86.87, "y": 150.6}], "scale": [{"time": 0.5667, "x": 0.043, "y": 0.043}, {"time": 0.7, "x": 0.481, "y": 0.544}, {"time": 0.9, "x": 0.46, "y": 0.386}, {"time": 1.1333, "x": 0.434, "y": 0.497}, {"time": 1.3333, "x": 0.434, "y": 0.434}]}, "bone6": {"rotate": [{"time": 0.5, "angle": 0}], "translate": [{"time": 0.5, "x": -45.37, "y": 0}, {"time": 1.3, "x": 133.13, "y": 113.57}], "scale": [{"time": 0.5, "x": 0.017, "y": 0.017}, {"time": 0.6333, "x": 0.271, "y": 0.315}, {"time": 0.8333, "x": 0.262, "y": 0.232}, {"time": 1.0667, "x": 0.227, "y": 0.259}, {"time": 1.3, "x": 0.268, "y": 0.259}]}, "bone7": {"translate": [{"time": 0.6667, "x": 0, "y": 0}, {"time": 1.4333, "x": 168.77, "y": 157.69}], "scale": [{"time": 0.6667, "x": 0.067, "y": 0.067}, {"time": 0.8333, "x": 0.482, "y": 0.547}, {"time": 1.0333, "x": 0.566, "y": 0.421}, {"time": 1.2333, "x": 0.444, "y": 0.55}, {"time": 1.4333, "x": 0.43, "y": 0.43}]}}, "deform": {"default": {"zuiba1": {"zuiba1": [{"time": 0, "curve": "stepped"}, {"time": 0.1333}, {"time": 0.4333, "offset": 18, "vertices": [-1.88809, 0.00601, -1.88801, 0.00601, -6.60804, 0.02103, -6.60799, 0.02102, -4.24807, 0.01352, -4.24802, 0.01352, -10.22196, 0.03253, -10.2219, 0.03252, -4.24806, 0.01352, -4.24802, 0.01352, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -7.65324, 0.02435, -7.65319, 0.02435, 0, 0, 0, 0, -3.00677, 0.00957, -3.00674, 0.00957, -0.0125, -4.3803, -0.01242, -4.3803, -7.63976, 4.80285, -7.63964, 4.80285, -4.63299, 4.79329, -4.6329, 4.79329, -4.65897, -4.36552, -4.65887, -4.36552]}, {"time": 0.5, "offset": 22, "vertices": [-7.25755, 0.53916, -7.27752, 0.02315, -4.03198, 0.29954, -4.04307, 0.01286, -7.25755, 0.53916, -7.27752, 0.02315], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "vertices": [3.89591, -0.94408, 4.00877, -0.01252, 1.46095, -0.35402, 1.50325, -0.0047, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.7686, -1.88206, 7.99331, -0.02464, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49059, 2.02679, 0.00605, 2.08532, 2.35714, 0.29852, 2.22342, 0.83835, 3.89591, -0.94408, 4.00877, -0.01252, 3.89591, -0.94408, 4.00877, -0.01252, 8.04438, -1.94935, 8.27737, -0.02586, 3.89591, -0.94408, 4.00877, -0.01252, 3.89591, -0.94408, 4.00877, -0.01252, 0, 0, 0, 0, 4.10907, 1.55894, 3.63414, 2.47141, 4.10907, 1.55894, 3.63414, 2.47141, 1.17825, -2.83986, 1.80617, -2.48818, 2.20055, 1.38269, 1.81885, 1.85633, 2.20055, 1.38269, 1.81885, 1.85633, 1.17825, -2.83986, 1.80617, -2.48818]}, {"time": 1.1667, "offset": 8, "vertices": [-2.75081, -0.85887, -2.47583, -1.4748, -3.52206, -0.71591, -3.51408, -0.36297, -2.48771, -0.07976, 0, 0, 0, 0, -3.7515, 0.90856, -3.85995, 0.01159, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.3328, 0.50739, -2.2354, 0.49571, -2.24888, 1.022, -0.54767, 1.81085, -0.95365, 1.63394]}]}, "zuiba2": {"zuiba2": [{"time": 0, "curve": "stepped"}, {"time": 0.1333}, {"time": 0.4333, "offset": 16, "vertices": [8.00772, -0.23032, 8.01096, -0.02549, 8.00772, -0.23032, 8.01096, -0.02549, 12.45011, -0.22769, 12.45177, 0.09073, 15.8464, -0.32538, 15.84946, 0.07991, 15.8464, -0.32538, 15.84946, 0.07991, 15.8464, -0.32538, 15.84946, 0.07991, 12.9791, 1.30056, 12.94151, 1.632, 8.00772, -0.23032, 8.01096, -0.02549, 8.00772, -0.23032, 8.01096, -0.02549, 0, 0, 0, 0, -5.95476, -8.88127, -5.95476, -8.88127, 24.10206, -0.45314, 24.10802, -0.0767, 0, 0, 0, 0, 28.32827, -0.44431, 0, 0, 0, 0, 23.0699, -0.42346, 23.07546, -0.07342, 0, 0, 0, 0, 28.32827, -0.44431, 0, 0, 0, 0, 18.28709, -0.26303]}, {"time": 0.5, "vertices": [-7.63007, 9.32195, -8.94055, 16.43376, -12.15109, 13.15804, -12.15109, 13.15804, -5.15963, 4.93514, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -18.76385, 8.06299, -19.12357, 7.16847, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.32584, 12.05058, 3.75336, 12.02055, 0, 0, 0, 0, 0, 0, -11.44505, 1.12286, 0, 0, 0, 0, -7.18153, 9.67166, -7.63007, 9.32195, 9.12867, 7.15089, 8.78358, 7.07967, -6.30125, 7.65546, -6.65441, 7.12879], "curve": [0.28, 0, 0.623, 0.39]}, {"time": 0.5667, "vertices": [-7.63007, 9.32195, -9.23267, 16.43483, -11.88663, 13.15754, -11.88663, 13.15754, -4.06312, 4.93188, 0, 0, 0, 0, 0, 0, -0.30221, -0.18529, -0.29313, -0.19935, -0.30221, -0.18529, -0.29313, -0.19935, -0.48695, 0.02451, -0.48756, 0.0015, -0.48695, 0.02451, -0.48756, 0.0015, -0.48695, 0.02451, -0.48756, 0.0015, -0.48695, 0.02451, -0.48756, 0.0015, -0.8464, 0.6441, -0.87586, 0.60343, -0.55925, 0.52939, -0.58361, 0.50241, -0.2721, 0.41469, -0.29137, 0.40138, 0, 0, 0, 0, -0.28739, -0.07283, -0.28739, -0.07283, -5.86618, 1.24104, -5.91851, 1.05266, -16.6252, 7.05824, -16.93986, 6.26576, 0.94888, 0.15925, 0, 0, 0, 0, -5.63971, -0.69057, -5.6011, -0.87808, -2.60672, 12.03203, -3.17104, 11.79875, 0.94888, 0.15925, -6.30095, 1.25697, -6.35357, 1.05404, -15.58302, 1.25371, 0, 0, 0, 0, -7.18153, 9.67166, -7.63007, 9.32195, 7.68734, 7.14744, 7.34364, 7.08442, -5.36234, 7.57417, -5.71288, 7.12596], "curve": [0.326, 0.31, 0.757, 1]}, {"time": 0.8, "vertices": [-7.63007, 9.32195, -10.8342, 16.44069, -10.43671, 13.15479, -10.43671, 13.15479, 1.94855, 4.91402, 0, 0, 0, 0, 0, 0, -1.95906, -1.20114, -1.9002, -1.29226, -1.95906, -1.20114, -1.9002, -1.29226, -3.15664, 0.1589, -3.16062, 0.00974, -3.15664, 0.1589, -3.16062, 0.00974, -3.15664, 0.1589, -3.16062, 0.00974, -3.15664, 0.1589, -3.16062, 0.00974, -5.48683, 4.17537, -5.67776, 3.91177, -3.62534, 3.43181, -3.78326, 3.25689, -1.7639, 2.68823, -1.8888, 2.60198, 0, 0, 0, 0, -1.86301, -0.47211, -1.86301, -0.47211, 4.24698, 6.49611, 3.93568, 6.6893, -4.89997, 1.54966, -4.96764, 1.31668, 6.15113, 1.03233, 0, 0, 0, 0, 0.07843, -5.81902, 0.35298, -5.80884, 1.66002, 10.38139, 1.1683, 10.44817, 6.15113, 1.03233, 4.24698, 6.49611, 3.93568, 6.6893, -1.63139, 0.62864, 0, 0, 0, 0, -7.18153, 9.67166, -7.63007, 9.32195, -0.21476, 7.12852, -0.55089, 7.11044, -0.21476, 7.12852, -0.55089, 7.11044]}, {"time": 1.1667, "vertices": [-7.63007, 9.32195, -26.75364, 16.49042, -24.32746, 13.20113, -26.91044, 13.20889, -8.09618, 4.94733, -3.30416, -0.93744, -3.95565, -0.35903, -3.26945, 0.78955, -3.27971, 1.49698, -3.34674, 1.34053, -0.8396, -0.51477, -0.81437, -0.55383, -1.35285, 0.0681, -1.35455, 0.00418, -1.35285, 0.0681, -1.35455, 0.00418, -1.35285, 0.0681, -1.35455, 0.00418, -1.35285, 0.0681, -1.35455, 0.00418, -2.3515, 1.78945, -2.43333, 1.67647, -1.55372, 1.47078, -1.6214, 1.39581, -1.73909, -1.34447, -1.67371, -1.42505, -1.90003, -0.86109, -3.07577, 0.40114, -10.17688, -2.34529, -8.25835, -4.09821, 1.82014, 2.78404, 1.68672, 2.86684, -9.57184, 5.10852, -9.80236, 4.65109, 2.6362, 0.44243, -0.31208, 0.61402, -0.34071, 0.59861, 1.55145, -2.57033, 1.67102, -2.49425, -1.64391, 12.13181, -2.2148, 12.0407, 2.6362, 0.44243, 1.82014, 2.78404, 1.68672, 2.86684, -9.58261, 1.02911, -2.49159, -0.381, -2.47086, -0.49817, -7.18153, 9.67166, -7.63007, 9.32195, 0.20924, 7.10947, -0.12664, 7.11142, -2.35953, 7.23857, -2.69864, 7.11914]}]}}}}}}