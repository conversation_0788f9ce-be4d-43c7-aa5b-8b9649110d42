import UBaseView from "../../../../framwork/widget/UBaseView";
const TAG = "SearchView";
const { ccclass, property } = cc._decorator;
/**
 * 搜索框
 */
@ccclass
export default class SearchView extends UBaseView {
    @property(cc.EditBox)
    searchEditBox: cc.EditBox = null;

    @property(cc.Button)
    clearBtn: cc.Button = null;

    private searchCallBack: Function = null;
    private searchTimer: NodeJS.Timeout = null;
    private readonly DEBOUNCE_DELAY: number = 300; // 防抖延迟时间，单位毫秒

    onUILoad(): void {
        this.onRegisterEvent(this.clearBtn.node, this.clearContent.bind(this));
        this.initView();
    }

    onUIDestroy(): void {
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
            this.searchTimer = null;
        }
        super.onUIDestroy();
    }

    private initView(): void {
        this.clearBtn.node.active = false;
        this.searchEditBox.placeholder = "";
        this.searchEditBox.node.on("text-changed", this.onSearchEditBoxTextChanged, this);
    }

    /**
     * 初始化数据
     * @param placeholder 搜索框的placeholder
     * @param searchCallBack 搜索回调
     */
    public initData(placeholder: string, searchCallBack: Function): void {
        this.setPlaceholder(placeholder);
        this.searchCallBack = searchCallBack;
    }

    public setPlaceholder(placeholder: string): void {
        this.searchEditBox.placeholder = placeholder;
    }

    /**
     * 清空搜索内容
     */
    private clearContent(): void {
        this.searchEditBox.string = "";
        this.searchCallBack && this.searchCallBack("");
        this.clearBtn.node.active = false;
    }

    /**
     * 搜索框内容改变
     */
    private onSearchEditBoxTextChanged(): void {
        this.clearBtn.node.active = this.searchEditBox.string.length > 0;
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            // console.log(TAG, "发送搜索内容：", this.searchEditBox.string);
            this.searchCallBack && this.searchCallBack(this.searchEditBox.string);
            this.searchTimer = null;
        }, this.DEBOUNCE_DELAY);
    }
}
