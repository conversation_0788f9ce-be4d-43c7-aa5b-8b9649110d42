import PlatUtilsJS from "../js/PlatUtilsJS";
import { PhotoCallback } from "../js/SelectPhotoJS";

export default class PlatUtilsAnd {
    //-----
    private static _appConfig: any = null;
    private static _className: string = 'org/cocos2dx/javascript/AppActivity';
    //---------------------------------
    private static _selectPhotoCb: PhotoCallback = null
    private static _savePhotoCb = null
    private static _sysClipboardTextCb = null
    //-----
    private static initAppConfig() {
        let data = jsb.fileUtils.getStringFromFile('AppConfig.json');
        this._appConfig = PlatUtilsJS.decryptConfig(data);
    }

    private static initBridges() {
        cc.okb = cc.okb || {}
        //选择图片返回
        cc.okb.a = (data: string) => {
            let obj = PlatUtilsJS.decryptConfigBase64(data);
            this._selectPhotoCb && this._selectPhotoCb(obj.code, obj.base64)
            this._selectPhotoCb = null;
        }
        //保存图片返回
        cc.okb.b = (code: number) => {
            this._savePhotoCb && this._savePhotoCb(code)
            this._savePhotoCb = null;
        }
        //获取粘贴板内容
        cc.okb.b = (text: string) => {
            this._sysClipboardTextCb && this._sysClipboardTextCb(text)
            this._sysClipboardTextCb = null;
        }
    }

    private static callMethod(method: string, methodSignature: string, ...parameters: any) {
            return jsb.reflection.callStaticMethod(this._className, method, methodSignature, ...parameters);
    }

    //*********************分平台接口********************/
    public static init() {
        console.log("PlatUtilsAnd Init");
        this.initAppConfig();
        this.initBridges();
    }

    /**
     * 应用配置
     */
    public static getAppConfig() {
        return this._appConfig;
    }

    /**
     * 设备信息
     */
    public static getDeviceInfo() {
        let data = this.callMethod('getDeviceInfo', "()Ljava/lang/String;");
        return PlatUtilsJS.decryptConfigBase64(data);
    }

    /**
     * 更新
     */
    public static appUpdate(param) {
        this.callMethod('appUpdate', "(Ljava/lang/String;)V", JSON.stringify(param));
    }

    /**
     * 设置场景信息
     * @param orientationH  屏幕方向
     * @param navigationBar 状态栏
     */
    public static setSceneInfo(orientation: number, navigationBar: boolean) {
        this.callMethod('setSceneInfo', "(ZZ)V", orientation == cc.macro.ORIENTATION_LANDSCAPE, navigationBar);
    }
    /**
     * 打开相册
     */
    public static openPhotoAlbum(limitSize: number, cb: PhotoCallback) {
        this._selectPhotoCb = cb;
        this.callMethod('openPhotoAlbum', "(Ljava/lang/String;I)V", 'cc.okb.a', limitSize);
    }

    /**
     * 打开摄像机
     */
    public static openCamera(limitSize: number, cb: PhotoCallback) {
        this._selectPhotoCb = cb;
        this.callMethod('openCamera', "(Ljava/lang/String;I)V", 'cc.okb.a', limitSize);
    }

    /**
     * 保存相册
     */
    public static savePhotoAlbum(path: string, cb: Function) {
        this._savePhotoCb = cb;
        this.callMethod('savePhotoAlbum', "(Ljava/lang/String;Ljava/lang/String;)V", 'cc.okb.b', path);
    }

    /**
     * 复制文字
     * @param text 
     * @param cb 
     */
    static setSysClipboardText(text: string, cb?: Function) {
        this.callMethod('setSysClipboardText', "(Ljava/lang/String;)V", text);
        cb && cb(0)
    }


    /**
     * 获取剪贴板文字
     */
    public static getSysClipboardText(cb: Function) {
        this._sysClipboardTextCb = cb;
        this.callMethod('getSysClipboardText', "(Ljava/lang/String;)V", 'cc.okb.c');
    }

    /**
     * 获取启动参数
     */
    public static getStartupParam(): any {
        let data = this.callMethod('getStartupParam', "()Ljava/lang/String;");
        return PlatUtilsJS.decryptConfigBase64(data);
    }

    /**
     * 上报
     */
    public static reportByParam(param) {
        this.callMethod('reportByParam', "(Ljava/lang/String;)V", JSON.stringify(param));
    }
}
