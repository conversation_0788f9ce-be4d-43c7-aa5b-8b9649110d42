import { ULanguage } from "../../../framwork/language/ULanguage";
import UBaseScene from "../../../framwork/widget/UBaseScene";
import { OKGameMsgType } from "../../okgame/network/OKGameMsgType";
import { WSMsgData } from "../../okgame/network/OKGameWS";
import { wspb } from "../../okgame/proto/proto_ws_msg";
import OKExtManager, { OKExtEvent } from "../../okgame/public/OKExtManager";
import OKGameManager from "../../okgame/public/OKGameManager";
import { EnterRoomData } from "../../okgame/room/scripts/OKRoomManger";
import { ComGameEvent, ImEmojiType } from "../pubilc/ComGameConst";
import ComGameManager from "../pubilc/ComGameManager";
import UIComPlayer from "./ui/UIComPlayer";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ComGameScene extends UBaseScene {
    @property(cc.Node)
    gameBg: cc.Node = null;
    @property(cc.Node)
    propAnimView: cc.Node = null;

    onUILoad(): void {
        //----注册协议-----
        this.registerProto(OKGameMsgType.imEmojiNotify, this.onImEmojiNotify.bind(this))
        this.registerProto(OKGameMsgType.addScoreStatusNotify, this.onAddScoreStatusNotify.bind(this))
        //----注册事件-----
        this.registerEvent(ComGameEvent.UpdateGameBg, this.updateGameBg.bind(this))
        //-----------------
    }

    onShow(data: EnterRoomData) {
        this.updateGameBg()
    }

    private onImEmojiNotify(data: WSMsgData) {
        let obj: wspb.ChatMsgPO = data.decode(wspb.ChatMsgPO)
        console.log('onImEmojiNotify', obj)
        switch (obj.type) {
            case ImEmojiType.kEmoji: {
                this.playEmojiAnim(obj)
                break;
            }
            case ImEmojiType.kEachProp: {
                this.playEachPropAnim(obj)
                break;
            }
        }
    }

    private onAddScoreStatusNotify(data: WSMsgData) {
        let obj: wspb.BattleAddScoreStatusResp = data.decode(wspb.BattleAddScoreStatusResp)
        console.log('onAddScoreStatusNotify', obj)

        //补充记分牌成功 1, 本手结束后添加2 ,申请被拒绝  3,等待审核 4
        switch (obj.status) {
            case 1: {
                if (obj.auditor && obj.auditor != '') {
                    OKGameManager.getInstance().showToastById('comGame.gameStatus.add_score_succeed1', obj.auditor, obj.score)
                } else {
                    OKGameManager.getInstance().showToastById('comGame.gameStatus.add_score_succeed1_ori')
                }
                break
            }
            case 2: {
                if (obj.auditor && obj.auditor != '') {
                    OKGameManager.getInstance().showToastById('comGame.gameStatus.add_score_succeed2', obj.auditor, obj.score)
                } else {
                    OKGameManager.getInstance().showToastById('comGame.gameStatus.add_score_succeed2_ori')
                }
                break
            }
            case 3: {
                OKGameManager.getInstance().showToastById('comGame.gameStatus.add_score_refuse')
                break
            }
            case 4: {
                OKGameManager.getInstance().showToastById('comGame.gameStatus.send_check')
                break
            }
        }
    }
    protected getPlayer(userId: number): any {
        return null;
    }

    protected getPlayerBySeatNum(seatNum: number): any {
        return null;
    }

    private updateGameBg() {
        if (!cc.isValid(this.gameBg)) {
            return;
        }
        let node = ComGameManager.getInstance().createGameBg();
        if (!node) {
            return;
        }
        node.parent = this.gameBg;
        node.active = true;
    }

    private playEmojiAnim(obj: wspb.ChatMsgPO) {
        console.log('playEmojiAnim')
        OKExtManager.getInstance().loadEmojiAnim(obj.content, (anim: cc.Node) => {
            if (!anim) {
                return;
            }
            let player: UIComPlayer = this.getPlayer(obj.userId)
            if (!player) {
                anim.destroy();
                return;
            }
            player.playEmojiAnim(anim)
        })
    }

    private playEachPropAnim(obj: wspb.ChatMsgPO) {
        console.log('playEachPropAnim')
        OKExtManager.getInstance().loadEachPropAnim(obj.content, (anim: cc.Node) => {
            if (!anim) {
                return;
            }
            let player: UIComPlayer = this.getPlayer(obj.userId)
            if (!player) {
                anim.destroy();
                return;
            }
            let playerT: UIComPlayer = this.getPlayer(obj.receiverId)
            if (!playerT) {
                anim.destroy();
                return;
            }
            console.log('AAAAAAAAAAAAAAA')
            anim.parent = this.propAnimView;
            anim.position = this.propAnimView.convertToNodeSpaceAR(player.getPos());
            let pose = this.propAnimView.convertToNodeSpaceAR(playerT.getPos());
            cc.tween(anim).to(0.2, {
                position: pose
            }).call(() => {
                //发送播放动画事件
                anim.emit(OKExtEvent.PLAY_EACH_PROP)
            }).start();
            anim.active = true;
        })
    }
}
