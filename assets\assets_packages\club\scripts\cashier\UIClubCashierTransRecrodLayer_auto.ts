export default class UIClubCashierTransRecrodLayer_auto {
    node:cc.Node = null;   
	ClubCashierTransRecrodLayer: cc.Node;
	SearchLayer: cc.Node;
	SelectLayer: cc.Node;
	selectBox: cc.Node;
	TransactionScrollView: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierTransRecrodLayer = this.node;
		this.SearchLayer = this.ClubCashierTransRecrodLayer.getChildByName("SearchLayer");
		this.SelectLayer = this.ClubCashierTransRecrodLayer.getChildByName("SelectLayer");
		this.selectBox = this.SelectLayer.getChildByName("selectBox");
		this.TransactionScrollView = this.ClubCashierTransRecrodLayer.getChildByName("TransactionScrollView");
		this.view = this.TransactionScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.TransactionScrollView.getChildByName("listEmpty");

    }
}
