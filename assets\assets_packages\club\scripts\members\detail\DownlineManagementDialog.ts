import { ULanguage } from "../../../../../framwork/language/ULanguage";
import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../../framwork/widget/UPrefabContainer";
import UISelectBox from "../../../../okgame/common/scripts/SelectBox/UISelectBox";
import { HORIZONTAL_ALIGN, OptionsItemMode, SORT_TYPE, VERTICAL_ALIGN } from "../../../../okgame/common/scripts/SelectBox/UISelectBoxOptions";
import UIListEmpty from "../../../../okgame/common/scripts/UIListEmpty";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameData from "../../../../okgame/public/OKGameData";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import ClubAPI from "../../ClubAPI";
import ClubDataManager from "../../ClubDataManager";
import { SortTypeEnum } from "../MemberConst";
import MemberHelper from "../MemberHelper";
import ItemMemberView from "../member/ItemMemberView";
import SearchView from "../SearchView";
import { IClubCompleteInfo, IClubMemberFullInfo } from "../MemberTypes";

const TAG = "[DownlineManagementDialog]";

const { ccclass, property } = cc._decorator;
/**
 * 下线管理弹框
 */
@ccclass
export default class DownlineManagementDialog extends UBaseDialog {
    @property(cc.Button)
    closeBtn: cc.Button = null;

    @property(cc.Toggle)
    addMemberToggle: cc.Toggle = null;

    @property(cc.Toggle)
    removeMemberToggle: cc.Toggle = null;

    @property(cc.Node)
    sortMenuNode: cc.Node = null;

    @property(cc.Node)
    SearchInputNode: cc.Node = null;

    @property(cc.Node)
    listContentNode: cc.Node = null;

    @property(cc.ScrollView)
    ScrollView: cc.ScrollView = null;

    @property(cc.Button)
    btnConfirm: cc.Button = null;

    @property(cc.Label)
    btnConfirmLabel: cc.Label = null;

    @property(cc.Prefab)
    ItemMemberView: cc.Prefab = null;

    @property(cc.Node)
    emptyNode: cc.Node = null;

    private clubInfo: IClubCompleteInfo = null;
    private myIdentity: okpb.Identity = okpb.Identity.IDE_GENERAL;
    // private myUserId: number = 0;
    private membersCount: number = 0;
    private targetId: number = 0;
    /**
     * 成员列表数据
     */
    private membersList: IClubMemberFullInfo[] = [];
    /**
     * 搜索结果列表
     */
    private searchResultList: IClubMemberFullInfo[] = [];
    /**
     * 选中的成员ids，用于设为下线功能
     */
    private selectedMemberIds: number[] = [];
    /**
     * 排序字段: 服务费、盈亏、手牌数、上次登录、上次玩牌
     */
    private sortField: SortTypeEnum = SortTypeEnum.ServiceFee;
    /**
     * 正序、倒叙
     */
    private sortDirection: SORT_TYPE = SORT_TYPE.DESC;
    /**
     * 防抖相关的变量
     */
    private isRequesting: boolean = false;
    private debounceTimer: NodeJS.Timeout | null = null;
    private readonly DEBOUNCE_DELAY: number = 300;

    onUILoad(): void {
        this.initView();
        this.initEvent();
    }

    onUIDestroy(): void {
        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
        super.onUIDestroy();
    }

    private initView(): void {
        this.sortMenuNode.active = true;
        this.SearchInputNode.active = true;
        if (this.getSearchScript()) {
            this.getSearchScript().initData("", (searchContent: string) => {
                console.log(TAG, "接收到搜索内容：", searchContent);
                this.runSearchMember(searchContent);
            });
        } else {
            console.error(TAG, "SearchView组件不存在");
        }
        this.addMemberToggle.isChecked = true;
        this.removeMemberToggle.isChecked = false;
        this.btnConfirm.node.getComponent(cc.Button).interactable = false;
        this.btnConfirm.node.getComponent(cc.Button).enableAutoGrayEffect = true;

        // 初始化排序菜单
        this.initSortMenu();
    }

    private initEvent(): void {
        this.onRegisterEvent(this.closeBtn.node, this.closeDialog.bind(this));
        this.onRegisterEvent(this.btnConfirm.node, this.onConfirmBtnClick.bind(this));
        this.onRegisterEvent(this.addMemberToggle.node, this.onToggleChange.bind(this));
        this.onRegisterEvent(this.removeMemberToggle.node, this.onToggleChange.bind(this));
    }

    /**
     * @param targetIdentity 被查看目标的角色
     */
    onShow(targetId: number, targetIdentity: okpb.Identity): void {
        this.targetId = targetId;
        this.clubInfo = ClubDataManager.getInstance().getClubInfo();
        this.myIdentity = this.clubInfo.identity;
        // 查看者为创建者/管理员，被查看对象为代理时才会展示下线管理模块
        if (this.myIdentity !== okpb.Identity.IDE_MASTER && this.myIdentity !== okpb.Identity.IDE_MANAGER) {
            console.error(TAG, "查看者不是创建者/管理员，不展示下线管理模块");
            return;
        }
        if (targetIdentity !== okpb.Identity.IDE_AGENT) {
            console.error(TAG, "被查看对象不是代理，不展示下线管理模块");
            return;
        }

        // this.myUserId = OKGameData.getInstance().getUserId();
        this.selectedMemberIds = []; // 重置选中的成员
        this.getMemberList();
    }

    /**
     * 初始化排序菜单的UI
     */
    private initSortMenu(): void {
        this.sortMenuNode.active = true;
        let options = [
            this.getLangByID("club.ServiceFee"),
            this.getLangByID("club.profitLoss"),
            this.getLangByID("club.handCount"),
            this.getLangByID("club.lastLoginTime"),
            this.getLangByID("club.lastPlay"),
        ];
        this.sortMenuNode
            .getComponent(UPrefabContainer)
            .getNodeComponent(UISelectBox)
            .init(
                options,
                options[0],
                (_sortFiled: number, value: number | string, _sortDirection: SORT_TYPE) => {
                    console.log(TAG, "排序说明:", value, ", 升序or降序:", _sortDirection);
                    this.sortField = _sortFiled;
                    this.sortDirection = _sortDirection;
                    this.sortMemberList();
                },
                OptionsItemMode.SORT,
                VERTICAL_ALIGN.DOWN,
                HORIZONTAL_ALIGN.RIGHT
            );
    }

    /**
     * 对成员列表进行排序
     */
    private sortMemberList(): void {
        if (this.membersList.length === 0) {
            return;
        }
        let sourceList = this.membersList;
        if (this.searchResultList.length > 0) {
            sourceList = this.searchResultList;
        }
        console.log(TAG, "排序前的成员列表:", sourceList);
        sourceList = MemberHelper.sortMemberList(sourceList, false, this.sortField, this.sortDirection);
        console.log(TAG, "排序后的成员列表:", sourceList);
        this.refreshListView(sourceList);
    }

    /**
     * 搜索成员
     * @param searchContent 搜索内容
     */
    private runSearchMember(searchContent: string): void {
        console.log(TAG, "runSearchMember:", searchContent);
        // 如果searchContent内容为空，则展示所有数据
        if (!searchContent) {
            this.searchResultList = [];
            const sortResult = MemberHelper.sortMemberList(this.membersList, false, this.sortField, this.sortDirection);
            this.refreshListView(sortResult);
            return;
        }
        // 通过搜索nickname、userId、mark、agentId、realname进行搜索
        this.searchResultList = this.membersList.filter((member) => {
            return (
                member.nickname.includes(searchContent) ||
                member.userId.toString().includes(searchContent) ||
                member.mark.includes(searchContent) ||
                member.agentId.toString().includes(searchContent) ||
                member.realname.includes(searchContent)
            );
        });

        if (this.searchResultList.length === 0) {
            this.setEmptyNodeTxt(this.getLangByID("club.searchNoMember"));
            this.ScrollView.content.removeAllChildren();
            this.emptyNode.active = true;
        } else {
            const sortResult = MemberHelper.sortMemberList(this.searchResultList, false, this.sortField, this.sortDirection);
            console.log(TAG, "搜索后+排序后的成员列表:", sortResult);
            this.emptyNode.active = false;
            this.refreshListView(sortResult);
        }
    }

    /**
     * 获取服务器成员列表
     */
    private getMemberList(): void {
        if (this.isRequesting) {
            console.log(TAG, "请求正在进行中，忽略本次请求");
            return;
        }

        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        console.log(TAG, "getMemberList() 开始请求成员列表");
        this.debounceTimer = setTimeout(() => {
            OKGameManager.getInstance().showLoading();
            this.isRequesting = true;

            ClubDataManager.getInstance().requestMembersAndAgetnAndMark(this.clubInfo.clubId, () => {
                console.log(TAG, "请求普通成员完成，准备进行筛选");
                this.isRequesting = false;
                OKGameManager.getInstance().hideLoading();
                if (!cc.isValid(this.node)) {
                    return;
                }

                this.membersList = ClubDataManager.getInstance()
                    .getAllMemberListByClubId(this.clubInfo.clubId)
                    .filter((user) => user.identity === okpb.Identity.IDE_GENERAL && user.agentId === 0);
                console.log(TAG, "筛选后的普通成员列表:", this.membersList);
                this.membersList = MemberHelper.sortMemberList(this.membersList, false, this.sortField, this.sortDirection);
                this.membersCount = this.membersList.length;

                if (this.membersCount === 0) {
                    this.emptyNode.active = true;
                    this.setEmptyNodeTxt(this.getLangByID("club.noMember"));
                } else {
                    this.emptyNode.active = false;
                }
                this.updateSearchPlaceholder();
                this.refreshListView(this.membersList);
            });
        }, this.DEBOUNCE_DELAY);
    }

    private getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id);
    }

    /**
     * 获取代理下级列表
     */
    private getDownlineList(): void {
        if (this.isRequesting) {
            console.log(TAG, "请求正在进行中，忽略本次请求");
            return;
        }

        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        console.log(TAG, "getDownlineList() 开始请求代理下级列表");
        this.debounceTimer = setTimeout(() => {
            OKGameManager.getInstance().showLoading();
            this.isRequesting = true;

            ClubAPI.getInstance()
                .ReqGetClubAgentSubList(this.clubInfo.clubId, this.targetId)
                .then((res: okpb.RspGetClubAgentSubList) => {
                    console.log(TAG, "Rsp:获取代理下级列表成功:", res);
                    if (cc.isValid(this.node)) {
                        this.handleDownlineData(res);
                    }
                })
                .catch((err: any) => {
                    console.error(TAG, "获取代理下级列表失败:", err);
                    if (cc.isValid(this.node) && err.message) {
                        OKGameManager.getInstance().showToast(err.message);
                    }
                })
                .finally(() => {
                    console.log(TAG, "获取代理下级列表完成");
                    this.isRequesting = false;
                    OKGameManager.getInstance().hideLoading();
                });
        }, this.DEBOUNCE_DELAY);
    }

    /**
     * 处理服务器返回的代理下级列表数据
     */
    private handleDownlineData(rsp: okpb.RspGetClubAgentSubList): void {
        if (!cc.isValid(this.node)) {
            return;
        }
        this.membersList = [];
        this.membersCount = 0;
        this.emptyNode.active = true;
        this.ScrollView.content.removeAllChildren();

        if (rsp?.errorCode === 0) {
            const subList = rsp.subList;
            if (subList && subList.users) {
                this.membersList = subList.users.map((user) => ClubDataManager.getInstance().createMemberInfoWithAgent(user));
                this.membersCount = this.membersList.length;
                console.log(TAG, "代理下级总人数:", this.membersCount, ",", this.membersList);
            }
            if (this.membersCount === 0) {
                this.emptyNode.active = true;
                this.setEmptyNodeTxt(this.getLangByID("club.noDownline"));
            } else {
                this.emptyNode.active = false;
            }
        } else {
            console.error(TAG, "Error!!! 获取代理下级列表失败:", rsp);
            OKGameManager.getInstance().showToastById("errorCode." + rsp.errorCode);
            this.emptyNode.active = true;
            this.setEmptyNodeTxt(this.getLangByID("club.getDownlineListFailed"));
        }
        this.updateSearchPlaceholder();
        if (this.membersCount > 0) {
            // 应用排序后刷新列表
            const sortedList = MemberHelper.sortMemberList(this.membersList, false, this.sortField, this.sortDirection);
            this.refreshListView(sortedList);
        } else {
            this.emptyNode.active = true;
            this.setEmptyNodeTxt(this.getLangByID("club.noDownline"));
        }
    }

    /**
     * 更新搜索框的placeholder
     */
    private updateSearchPlaceholder(): void {
        if (this.getSearchScript()) {
            const placHolderTxt = ULanguage.getInstance().getLangByID("club.searchMember") + "  (" + this.membersCount + ")";
            this.getSearchScript().setPlaceholder(placHolderTxt);
        }
    }

    private getSearchScript(): SearchView {
        return this.SearchInputNode.getChildByName("SearchView").getComponent(SearchView);
    }

    /**
     * 刷新列表显示
     */
    private refreshListView(sortList: IClubMemberFullInfo[]): void {
        console.log(TAG, "refreshListView() 刷新列表显示");
        this.ScrollView.content.removeAllChildren();
        this.emptyNode.active = sortList?.length === 0;
        sortList.forEach((member) => {
            const itemNode = cc.instantiate(this.ItemMemberView);
            // 这里需要根据具体的ItemMemberView组件来设置数据
            const itemScript = itemNode.getComponent(ItemMemberView);
            itemScript.setData(member, this.myIdentity, this.checkBoxCallback.bind(this));
            itemScript.showDiffView(this.sortField);
            itemScript.setEditMarkVisible(true);
            this.ScrollView.content.addChild(itemNode);
        });
    }

    /**
     * 选择成员回调处理
     * @param userId 成员ID
     * @param isChecked 是否选中
     */
    private checkBoxCallback(userId: string, isChecked: boolean): void {
        console.log(TAG, "checkBoxCallback", userId, isChecked);
        const userIdNum = parseInt(userId);
        if (isChecked) {
            // 添加到选中列表
            if (!this.selectedMemberIds.includes(userIdNum)) {
                this.selectedMemberIds.push(userIdNum);
            }
        } else {
            // 从选中列表中移除
            this.selectedMemberIds = this.selectedMemberIds.filter((id) => id !== userIdNum);
        }

        this.changeConfirmBtnTxt(this.selectedMemberIds.length);
        this.btnConfirm.node.getComponent(cc.Button).interactable = this.selectedMemberIds.length > 0;

        console.log(TAG, "选中的成员数组:", this.selectedMemberIds);
    }

    /**
     * 切换选择成员、已有下线
     */
    private onToggleChange(): void {
        this.changeConfirmBtnTxt(0);
        console.log(TAG, "onToggleChange:", "添加下线:", this.addMemberToggle.isChecked, ",移除下线:", this.removeMemberToggle.isChecked);
        this.selectedMemberIds = [];
        this.searchResultList = []; // 清空搜索结果
        // 清空搜索框内容
        if (this.getSearchScript()) {
            this.getSearchScript().searchEditBox.string = "";
        }
        if (this.addMemberToggle.isChecked) {
            this.getMemberList();
        } else if (this.removeMemberToggle.isChecked) {
            // 获得已有下线的数据
            this.getDownlineList();
        }
    }

    private changeConfirmBtnTxt(count: number): void {
        if (count === 0) {
            // this.btnConfirm.interactable = false;
            // 按钮置灰
            this.btnConfirm.node.getComponent(cc.Button).interactable = false;
        } else {
            // this.btnConfirm.interactable = true;
            this.btnConfirm.node.getComponent(cc.Button).interactable = true;
        }
        if (this.addMemberToggle.isChecked) {
            this.btnConfirmLabel.string = this.getLangByID("club.addDownline") + (count === 0 ? "" : "(" + count + ")");
        } else if (this.removeMemberToggle.isChecked) {
            this.btnConfirmLabel.string = this.getLangByID("club.removeDownline") + (count === 0 ? "" : "(" + count + ")");
        }
    }

    /**
     * 确认按钮点击事件
     */
    private async onConfirmBtnClick(): Promise<void> {
        if (this.selectedMemberIds.length === 0) {
            return;
        }
        console.log(TAG, "onConfirmBtnClick 选中的成员ID:", this.selectedMemberIds + ",操作者我的userID:", OKGameData.getInstance().getUserId());
        let targetAgentID = 0;
        let operationDesc = "";
        if (this.addMemberToggle.isChecked) {
            // 设为下线模式：将选中的成员设为当前用户（代理）的下线
            targetAgentID = this.targetId;
            operationDesc = "设为下线";
        } else if (this.removeMemberToggle.isChecked) {
            // 取消下线模式：取消选中成员的上级关系
            targetAgentID = 0;
            operationDesc = "移除下线";
        }

        try {
            OKGameManager.getInstance().showLoading();
            const response: okpb.RspSetClubUserToAgent = await ClubAPI.getInstance().ReqSetClubUserToAgent(
                this.clubInfo.clubId,
                this.selectedMemberIds,
                targetAgentID
            );

            if (response.errorCode === 0) {
                console.log(TAG, `${operationDesc}成功`);
                OKGameManager.getInstance().showToast(this.getLangByID("club.downlineSaved"));
                // 清空选中状态
                this.selectedMemberIds = [];
                this.changeConfirmBtnTxt(0);
                this.btnConfirm.interactable = false;
                // 更新缓存中代理的下线数量
                const newSubCount = response.subCount;
                const targetUserInfo = ClubDataManager.getInstance().getMemberInfoByClubIdAndUserId(this.clubInfo.clubId, targetAgentID);
                targetUserInfo.subCount = newSubCount;
                ClubDataManager.getInstance().setMemberInfoByClubId(this.clubInfo.clubId, targetUserInfo);
                console.log(TAG, "更新后的目标用户信息:", targetUserInfo);
                // 重新获取对应模式的数据
                if (this.addMemberToggle.isChecked) {
                    this.getMemberList();
                } else if (this.removeMemberToggle.isChecked) {
                    this.getDownlineList();
                }
            } else {
                console.error(TAG, `${operationDesc}失败:`, response.errMsg);
                OKGameManager.getInstance().showToastById("errorCode." + response.errorCode);
            }
        } catch (error) {
            console.error(TAG, `${operationDesc}请求异常:`, error);
            OKGameManager.getInstance().showToast(error.message);
        } finally {
            OKGameManager.getInstance().hideLoading();
        }
    }

    /**
     * 显示无成员的默认node
     */
    private setEmptyNodeTxt(defaultTxt: string = ""): void {
        const emptyScript = this.emptyNode.getComponent(UPrefabContainer).getNodeComponent(UIListEmpty);
        if (emptyScript) {
            emptyScript.setTitle(defaultTxt);
        } else {
            console.error(TAG, "UIListEmpty组件不存在");
        }
    }
}
