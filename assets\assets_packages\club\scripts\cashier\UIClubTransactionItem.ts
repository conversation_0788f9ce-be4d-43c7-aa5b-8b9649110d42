import UBaseView from "../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UIIdentityIcon from "../../../okgame/common/scripts/UIIdentityIcon";
import { okpb } from "../../../okgame/proto/proto_msg";
import UIClubCashierTransactionLayer, { ClubSelectUserItemData } from "./UIClubCashierTransactionLayer";
import UIClubTransactionItem_auto from "./UIClubTransactionItem_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubTransactionItem extends UBaseView {

	protected ui: UIClubTransactionItem_auto = null;
	private _curData: ClubSelectUserItemData = null;
	private father: UIClubCashierTransactionLayer = null;

	onUILoad(): void {
		this.ui = new UIClubTransactionItem_auto(this.node);
	}

	onEnable(): void {
		this.updateUI();
	}

	setData(father: UIClubCashierTransactionLayer, data: ClubSelectUserItemData) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;
		this.father = father;
		this.updateUI();
	}

	updateUI() {
		if (!cc.isValid(this.ui) || !this._curData) return;
		this.ui.toggleSelect.getComponent(cc.Toggle).isChecked = this._curData.isSelect ? true : false;
		this.ui.labelNoteName.getComponent(cc.Label).string = "" + this._curData.data.nickname;
		this.ui.freeTxt.getComponent(cc.Label).string = "" + this._curData.data.chipBalance;
		this.ui.userID.getComponent(cc.Label).string = "ID:" + this._curData.data.userId;
		this.ui.roleTypeImg.getComponent(UPrefabContainer).getNodeComponent(UIIdentityIcon).setIcon(this._curData.data.identity);
		this.ui.toggleSelect.getComponent(cc.Toggle).isChecked = false;
	}

	onToggleSelect(toggle: cc.Toggle) {
		let isCheck = toggle.isChecked;
		this._curData.isSelect = isCheck;
	}
}