export default class UIGameChatItem_auto {
    node:cc.Node = null;   
	GameChatItem: cc.Node;
	chatPersonInfoL: cc.Node;
	avatarMaskL: cc.Node;
	avatarL: cc.Node;
	avatarCircleL: cc.Node;
	avatarFrameL: cc.Node;
	chatInfoL: cc.Node;
	chatArrowL: cc.Node;
	chatBgL: cc.Node;
	audioTimeL: cc.Node;
	redPointL: cc.Node;
	chatBgTextL: cc.Node;
	richTextL: cc.Node;
	lblSimpleTextL: cc.Node;
	nicknameL: cc.Node;
	chatPersonInfoR: cc.Node;
	avatarMaskR: cc.Node;
	avatarR: cc.Node;
	avatarFrameR: cc.Node;
	chatInfoR: cc.Node;
	chatArrowR: cc.Node;
	TextContainerR: cc.Node;
	chatBgR: cc.Node;
	chatArrow2R: cc.Node;
	audioTimeR: cc.Node;
	chatBgTextR: cc.Node;
	richTextR: cc.Node;
	lblSimpleTextR: cc.Node;
	chatTime: cc.Node;
	lblTime: cc.Node;
	sysMsgBox: cc.Node;
	sysMsgContent: cc.Node;
	commentMsgBox: cc.Node;
	commentMsgContent: cc.Node;
	line: cc.Node;
	showOrhideNode: cc.Node;
	lblshowOrHide: cc.Node;
	icon: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.GameChatItem = this.node;
		this.chatPersonInfoL = this.GameChatItem.getChildByName("chatPersonInfoL");
		this.avatarMaskL = this.chatPersonInfoL.getChildByName("avatarMaskL");
		this.avatarL = this.avatarMaskL.getChildByName("avatarL");
		this.avatarCircleL = this.avatarMaskL.getChildByName("avatarCircleL");
		this.avatarFrameL = this.chatPersonInfoL.getChildByName("avatarFrameL");
		this.chatInfoL = this.chatPersonInfoL.getChildByName("chatInfoL");
		this.chatArrowL = this.chatInfoL.getChildByName("chatArrowL");
		this.chatBgL = this.chatInfoL.getChildByName("chatBgL");
		this.audioTimeL = this.chatBgL.getChildByName("audioTimeL");
		this.redPointL = this.chatBgL.getChildByName("redPointL");
		this.chatBgTextL = this.chatInfoL.getChildByName("chatBgTextL");
		this.richTextL = this.chatBgTextL.getChildByName("richTextL");
		this.lblSimpleTextL = this.chatBgTextL.getChildByName("lblSimpleTextL");
		this.nicknameL = this.chatPersonInfoL.getChildByName("nicknameL");
		this.chatPersonInfoR = this.GameChatItem.getChildByName("chatPersonInfoR");
		this.avatarMaskR = this.chatPersonInfoR.getChildByName("avatarMaskR");
		this.avatarR = this.avatarMaskR.getChildByName("avatarR");
		this.avatarFrameR = this.chatPersonInfoR.getChildByName("avatarFrameR");
		this.chatInfoR = this.chatPersonInfoR.getChildByName("chatInfoR");
		this.chatArrowR = this.chatInfoR.getChildByName("chatArrowR");
		this.TextContainerR = this.chatInfoR.getChildByName("TextContainerR");
		this.chatBgR = this.TextContainerR.getChildByName("chatBgR");
		this.chatArrow2R = this.chatBgR.getChildByName("chatArrow2R");
		this.audioTimeR = this.chatBgR.getChildByName("audioTimeR");
		this.chatBgTextR = this.TextContainerR.getChildByName("chatBgTextR");
		this.richTextR = this.chatBgTextR.getChildByName("richTextR");
		this.lblSimpleTextR = this.chatBgTextR.getChildByName("lblSimpleTextR");
		this.chatTime = this.GameChatItem.getChildByName("chatTime");
		this.lblTime = this.chatTime.getChildByName("lblTime");
		this.sysMsgBox = this.GameChatItem.getChildByName("sysMsgBox");
		this.sysMsgContent = this.sysMsgBox.getChildByName("sysMsgContent");
		this.commentMsgBox = this.GameChatItem.getChildByName("commentMsgBox");
		this.commentMsgContent = this.commentMsgBox.getChildByName("commentMsgContent");
		this.line = this.commentMsgBox.getChildByName("line");
		this.showOrhideNode = this.commentMsgBox.getChildByName("showOrhideNode");
		this.lblshowOrHide = this.showOrhideNode.getChildByName("lblshowOrHide");
		this.icon = this.showOrhideNode.getChildByName("icon");

    }
}
