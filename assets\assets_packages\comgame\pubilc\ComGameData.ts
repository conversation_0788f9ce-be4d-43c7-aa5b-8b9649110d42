import UEventHandle from "../../../framwork/utils/UEventHandle";
import { okpb } from "../../okgame/proto/proto_msg";

/**
 * 游戏通用模块数据类
 */
export default class ComGameData extends UEventHandle {
    private static _instance = null;
    public static getInstance(): ComGameData {
        if (this._instance == null) {
            this._instance = new ComGameData();
        }
        return this._instance;
    }
    //------------
    // 进入房间数据
    private _enterData: okpb.RspCreateRoom = null;

    //------------
    constructor() {
        super();
    }

    /**
     * 获取当前房间ID
     * @returns 
     */
    getCurRoomId(): number {
        if (!this._enterData) {
            return 0;
        }
        return this._enterData.roomId;
    }

    /**
     * 进入房间
     */
    onEnterRoom(data: okpb.RspCreateRoom) {
        this._enterData = data;
    }


}