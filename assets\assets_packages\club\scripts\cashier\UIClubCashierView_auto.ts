export default class UIClubCashierView_auto {
    node:cc.Node = null;   
	ClubCashierView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	TitleBG: cc.Node;
	labelTitle: cc.Node;
	btnBack: cc.Node;
	SelectTimeLayer: cc.Node;
	toggle1: cc.Node;
	labelTransaction: cc.Node;
	BackTransaction: cc.Node;
	labelTransaction2: cc.Node;
	toggle2: cc.Node;
	labelTransactions: cc.Node;
	BackTransactions: cc.Node;
	labelTransactions2: cc.Node;
	toggle3: cc.Node;
	labelChipRequest: cc.Node;
	BackChipRequest: cc.Node;
	labelChipRequest2: cc.Node;
	CashierTransactionLayer: cc.Node;
	CashierTransRecrodLayer: cc.Node;
	CashierChipRequestLayer: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierView = this.node;
		this.Mask = this.ClubCashierView.getChildByName("Mask");
		this.View = this.ClubCashierView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.TitleBG = this.View.getChildByName("TitleBG");
		this.labelTitle = this.TitleBG.getChildByName("labelTitle");
		this.btnBack = this.TitleBG.getChildByName("btnBack");
		this.SelectTimeLayer = this.View.getChildByName("SelectTimeLayer");
		this.toggle1 = this.SelectTimeLayer.getChildByName("toggle1");
		this.labelTransaction = this.toggle1.getChildByName("labelTransaction");
		this.BackTransaction = this.toggle1.getChildByName("BackTransaction");
		this.labelTransaction2 = this.BackTransaction.getChildByName("labelTransaction2");
		this.toggle2 = this.SelectTimeLayer.getChildByName("toggle2");
		this.labelTransactions = this.toggle2.getChildByName("labelTransactions");
		this.BackTransactions = this.toggle2.getChildByName("BackTransactions");
		this.labelTransactions2 = this.BackTransactions.getChildByName("labelTransactions2");
		this.toggle3 = this.SelectTimeLayer.getChildByName("toggle3");
		this.labelChipRequest = this.toggle3.getChildByName("labelChipRequest");
		this.BackChipRequest = this.toggle3.getChildByName("BackChipRequest");
		this.labelChipRequest2 = this.BackChipRequest.getChildByName("labelChipRequest2");
		this.CashierTransactionLayer = this.View.getChildByName("CashierTransactionLayer");
		this.CashierTransRecrodLayer = this.View.getChildByName("CashierTransRecrodLayer");
		this.CashierChipRequestLayer = this.View.getChildByName("CashierChipRequestLayer");

    }
}
