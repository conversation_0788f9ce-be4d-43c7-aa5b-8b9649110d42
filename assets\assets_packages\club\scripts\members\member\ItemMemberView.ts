import { ULanguage } from "../../../../../framwork/language/ULanguage";
import UBaseView from "../../../../../framwork/widget/UBaseView";
import UIconSprite from "../../../../../framwork/widget/UIconSprite";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameUtils from "../../../../okgame/public/OKGameUtils";
import ClubDataManager from "../../ClubDataManager";
import ClubManger from "../../ClubManger";
import { PermissionUtils } from "../PermissionUtils";
import MemberConst, { SortTypeEnum } from "../MemberConst";
import MemberHelper from "../MemberHelper";
import { IClubMemberFullInfo } from "../MemberTypes";

const TAG = "[ItemMemberView]";

const { ccclass, property } = cc._decorator;

/**
 * 成员-ItemView
 */
@ccclass
export default class ItemMemberView extends UBaseView {
    @property(cc.Toggle)
    toggle: cc.Toggle = null;

    @property(cc.Sprite)
    avatorImg: cc.Sprite = null;

    @property(cc.Sprite)
    permissionLockImg: cc.Sprite = null;

    @property(cc.Label)
    accountNameTxt: cc.Label = null;

    @property(cc.Label)
    userIDTxt: cc.Label = null;

    @property(cc.Sprite)
    roleTypeIcon: cc.Sprite = null;

    @property(cc.Label)
    nickName: cc.Label = null;

    @property(cc.Node)
    serviceFeeNode: cc.Node = null;

    @property(cc.Label)
    freeChipTxt: cc.Label = null;

    @property(cc.Label)
    normalTxt: cc.Label = null;

    @property(cc.Label)
    DownlineTxt: cc.Label = null;

    @property(cc.SpriteFrame)
    roleCreater: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    roleAdmin: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    roleSuperAgent: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    roleAgent: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    roleTM: cc.SpriteFrame = null;

    @property({ type: cc.Button, tooltip: "编辑昵称按钮" })
    remarkBtn: cc.Button = null;

    private itemMemberInfo: IClubMemberFullInfo = null;
    /**
     * 点选成员的回调，用于删除成员功能
     */
    private checkCallback: Function = null;

    onUILoad() {
        // console.log(TAG, "onUILoad");
        this.toggle.isChecked = false;
        this.onRegisterEvent(this.toggle.node, this.onToggleClick.bind(this));
        this.onRegisterEvent(this.node, this.onItemClick.bind(this));
        this.onRegisterEvent(this.remarkBtn.node, this.onRemarkBtnClick.bind(this));

        this.remarkBtn.node.active = false;
    }

    /**
     * 点击查看成员详情
     */
    private onItemClick(): void {
        // 获得缓存的俱乐部信息
        const clubInfo = ClubDataManager.getInstance().getClubInfo();
        const myIdentity = clubInfo.identity;
        console.log(TAG, "点击成员，查看是否有权限：myIdentity:", myIdentity, "this.itemMemberInfo.identity:", this.itemMemberInfo.identity);
        if (MemberHelper.hasPermissionToEnterDetail(myIdentity, this.itemMemberInfo.identity)) {
            ClubManger.getInstance().showMemberDetailView(this.itemMemberInfo);
        } else {
            console.log(TAG, "没有权限查看成员详情");
        }
    }

    /**
     * 设置数据
     * @param itemMemberInfo 成员数据
     * @param userRole 用户当前身份
     * @param checkCallback 点选成员的回调，用于删除成员功能
     */
    public setData(itemMemberInfo: IClubMemberFullInfo, userRole: number, checkCallback: Function) {
        this.checkCallback = checkCallback;
        this.itemMemberInfo = itemMemberInfo;
        // 设置头像
        const iconScript = this.avatorImg.getComponent(UIconSprite);
        if (iconScript) {
            iconScript.setIcon(itemMemberInfo.avatar);
        }
        // 管理员和代理需要判断是否有全部的权限，如果有，则不展示🔒图标
        const permissions = itemMemberInfo.permissions;
        if (PermissionUtils.hasClosedPermission(permissions, itemMemberInfo.identity)) {
            console.log(TAG, "设置", itemMemberInfo.nickname + "🔒可见");
            this.permissionLockImg.node.active = true;
        } else {
            this.permissionLockImg.node.active = false;
        }
        // 备注or昵称： 如果有备注的话，优先显示备注
        if (itemMemberInfo.mark) {
            this.accountNameTxt.string = OKGameUtils.getStringMaxBitWithCase(itemMemberInfo.mark, 25);
        } else {
            this.accountNameTxt.string = OKGameUtils.getStringMaxBitWithCase(itemMemberInfo.nickname, 25);
        }

        // 用户ID
        this.userIDTxt.string = "ID:" + itemMemberInfo.userId.toString();
        // 角色类型
        this.setRoleTypeIcon(itemMemberInfo.identity);
        // 上线or昵称： 如果data存在部位空的upAgentId字段时，this.nickName优先显示
        // !注意，0708和kk确认，自己也是自己的代理，所以本身是代理的话，这里不会显示agentName和agentiD
        if (itemMemberInfo.identity === okpb.Identity.IDE_AGENT) {
            this.nickName.string = OKGameUtils.getStringMaxBitWithCase(itemMemberInfo.nickname, 28);
        } else {
            if (itemMemberInfo.agentId) {
                this.nickName.string =
                    this.getLangByID("club.agent") + ": " + itemMemberInfo.agentName + "(" + itemMemberInfo.agentId.toString() + ")";
                this.nickName.node.color = MemberConst.COLOR_YELLOW;
            } else {
                this.nickName.string = OKGameUtils.getStringMaxBitWithCase(itemMemberInfo.nickname, 28);
            }
        }
        // 服务费
        this.freeChipTxt.string = MemberHelper.formatServiceFee(itemMemberInfo?.serviceFee ?? 0);
        if (itemMemberInfo.identity === okpb.Identity.IDE_AGENT) {
            this.DownlineTxt.node.active = (itemMemberInfo?.subCount ?? 0) >= 0;
            this.DownlineTxt.string = this.getLangByID("club.subag") + ": " + itemMemberInfo.subCount.toString();
        } else {
            this.DownlineTxt.string = "";
            this.DownlineTxt.node.active = false;
        }

        this.changeToggleVisible(userRole, itemMemberInfo.identity);
    }

    private getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id);
    }

    /**
     * 设置角色类型图标
     * 	IDE_GENERAL = 0, // 普通成员
		IDE_TABLE_OPERATOR = 70, // 开桌员
		IDE_AGENT = 80, // 代理
		IDE_MANAGER = 90, // 管理员
		IDE_MASTER = 100, // 创建者
     * @param role 角色类型
     */
    private setRoleTypeIcon(role: number) {
        switch (role) {
            case okpb.Identity.IDE_MASTER:
                this.roleTypeIcon.spriteFrame = this.roleCreater;

                break;
            case okpb.Identity.IDE_MANAGER:
                this.roleTypeIcon.spriteFrame = this.roleAdmin;

                break;
            case okpb.Identity.IDE_AGENT:
                this.roleTypeIcon.spriteFrame = this.roleAgent;

                break;
            case okpb.Identity.IDE_TABLE_OPERATOR:
                this.roleTypeIcon.spriteFrame = this.roleTM;

                break;
            default:
                this.roleTypeIcon.node.active = false;
                break;
        }
    }

    /**
     * 根据当前用户身份和itemView身份，决定是否显示复选框，也就是决定是否能删除这个成员
     * @param curRole 当前用户身份
     * @param itemRole itemView身份
     */
    private changeToggleVisible(curRole: number, itemRole: number) {
        // 创建者永远不能被选中
        if (itemRole === okpb.Identity.IDE_MASTER) {
            this.toggle.node.active = false;
        }
        // 如果当前用户是管理员，并且这个itemView也是管理员，则不能复选
        if (curRole === okpb.Identity.IDE_MANAGER && itemRole === okpb.Identity.IDE_MANAGER) {
            this.toggle.node.active = false;
        }
        // 用户为代理
        if (curRole === okpb.Identity.IDE_AGENT) {
            this.toggle.node.active = false;
        }
    }

    /**
     * 点击Toggle
     */
    private onToggleClick() {
        console.log(TAG, "onToggleClick", this.toggle.isChecked);
        this.checkCallback && this.checkCallback(this.itemMemberInfo.userId, this.toggle.isChecked);
    }

    /**
     * 显示不同的视图
     * 需求：排序规则选择服务费/盈亏/手牌数时 ，代理用户信息块右下区域展示下线数量，展示格式为：“下线：xx”
     * @param dataType 数据类型
     */
    public showDiffView(dataType: SortTypeEnum) {
        // console.log(TAG, "showDiffView", dataType, "是否是服务费:", dataType === SortTypeEnum.ServiceFee);
        this.serviceFeeNode.active = dataType === SortTypeEnum.ServiceFee;
        this.normalTxt.node.active = dataType !== SortTypeEnum.ServiceFee;

        switch (dataType) {
            case SortTypeEnum.ServiceFee:
                this.DownlineTxt.node.active = true;

                break;
            case SortTypeEnum.Profit:
                // 盈亏金额
                const profitLoss = this.itemMemberInfo.winLose;
                if (profitLoss === 0) {
                    this.normalTxt.string = "0";
                    this.normalTxt.node.color = MemberConst.COLOR_GRAY;
                    return;
                } else if (profitLoss > 0) {
                    this.normalTxt.string = "+" + profitLoss.toString();
                    this.normalTxt.node.color = MemberConst.COLOR_PINK;
                } else {
                    this.normalTxt.string = profitLoss.toString();
                    this.normalTxt.node.color = MemberConst.COLOR_GREEN;
                }
                this.DownlineTxt.node.active = true;

                break;
            case SortTypeEnum.HandCardCount:
                // 手牌数
                this.normalTxt.node.color = MemberConst.COLOR_GRAY;
                this.normalTxt.string = this.itemMemberInfo.handCount.toString();
                this.DownlineTxt.node.active = true;

                break;
            case SortTypeEnum.LastLogin:
                // 上次登录时间
                this.normalTxt.node.color = MemberConst.COLOR_GRAY;
                this.normalTxt.string = MemberHelper.formatTime(this.itemMemberInfo.lastLogin);
                this.DownlineTxt.node.active = false;

                break;
            case SortTypeEnum.LastPlay:
                // 上次玩牌时间
                this.normalTxt.node.color = MemberConst.COLOR_GRAY;
                this.normalTxt.string = MemberHelper.formatTime(this.itemMemberInfo.lastPlay);
                this.DownlineTxt.node.active = false;

                break;
            default:
                // this.normalTxt.node.active = false;
                break;
        }
    }

    /**
     * 删除成员后，设置此view是否可见
     * @param deleteMemberIds 已删除的数组
     */
    public setDeleteMember(deleteMemberIds: string[]) {
        // console.log(TAG, "接收到删除成员id:", deleteMemberIds, "当前成员id:", this.itemMemberInfo.userId.toString());
        // 检查一下deleteMemberIds数组是什么类型的数组
        // console.log(TAG, "deleteMemberIds的类型:", typeof deleteMemberIds);
        if (deleteMemberIds.map(String).includes(this.itemMemberInfo.userId.toString())) {
            this.node.active = false;
            console.log(TAG, "当前成员被删除，隐藏节点");
        }
    }

    /**
     * 设置编辑备注按钮的可见性
     * @param isVisible 是否可见
     */
    public setEditMarkVisible(isVisible: boolean): void {
        this.remarkBtn.node.active = isVisible;
    }

    /**
     * 编辑昵称的点击
     */
    private onRemarkBtnClick(): void {
        console.log(TAG, "onRemarkBtnClick");
        ClubManger.getInstance().showMemberEditRemarksView(
            this.itemMemberInfo.userId,
            this.itemMemberInfo.mark,
            this.itemMemberInfo.markDetail,
            this.refreshMemberInfo.bind(this)
        );
    }

    private refreshMemberInfo(): void {
        const memberInfo = ClubDataManager.getInstance().getMemberInfoById(this.itemMemberInfo.userId);
        if (memberInfo) {
            this.setData(memberInfo, this.itemMemberInfo.identity, this.checkCallback);
        }
    }
}
