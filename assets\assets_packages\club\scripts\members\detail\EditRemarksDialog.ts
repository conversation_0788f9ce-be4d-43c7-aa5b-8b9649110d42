import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameData from "../../../../okgame/public/OKGameData";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import ClubAPI from "../../ClubAPI";
import ClubDataManager from "../../ClubDataManager";

const TAG = "[EditRemarksDialog]";

const { ccclass, property } = cc._decorator;
/**
 * 添加备注和备注详情弹框
 */
@ccclass
export default class EditRemarksDialog extends UBaseDialog {
    @property(cc.Label)
    titleTxt: cc.Label = null;

    @property(cc.Button)
    closeBtn: cc.Button = null;

    @property(cc.EditBox)
    remarkEditBox: cc.EditBox = null;

    @property(cc.EditBox)
    remarkDetailEditBox: cc.EditBox = null;

    @property(cc.Button)
    btnSave: cc.Button = null;

    private clubId: number = 0;
    private myUserId: number = 0;
    private targetUserId: number = 0;
    private isRequesting: boolean = false;
    private refreshMemberInfo: () => void = null;
    onUILoad(): void {
        this.onRegisterEvent(this.closeBtn.node, this.closeDialog.bind(this));
        this.onRegisterEvent(this.btnSave.node, this.onSaveBtnClick.bind(this));

        this.clubId = ClubDataManager.getInstance().getClubId();

        this.myUserId = OKGameData.getInstance().getUserId();
    }

    onShow(targetUserId: number, mark: string, markDetail: string, refreshMemberInfo: () => void): void {
        console.log(TAG, "显示编辑备注", mark, markDetail, targetUserId);
        this.targetUserId = targetUserId;
        this.remarkEditBox.string = mark;
        this.remarkDetailEditBox.string = markDetail;
        this.refreshMemberInfo = refreshMemberInfo;
    }

    onSaveBtnClick(): void {
        // 防抖处理：如果正在请求中，直接返回
        if (this.isRequesting) {
            return;
        }

        const mark = this.remarkEditBox.string;
        const markDetail = this.remarkDetailEditBox.string;

        if (mark.length === 0) {
            return;
        }

        // 检查俱乐部ID是否有效
        if (!this.clubId) {
            console.error("[EditRemarksDialog] 俱乐部ID无效，无法保存备注");
            OKGameManager.getInstance().showToast("俱乐部信息异常，请重新进入");
            return;
        }

        // 设置请求状态为进行中
        this.isRequesting = true;
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqMarkClubUser(this.clubId, this.myUserId, this.targetUserId, mark, markDetail)
            .then((res: okpb.RspCommonMsg) => {
                if (cc.isValid(this.node)) {
                    if (res.errorCode === 0) {
                        OKGameManager.getInstance().showToastById("club.editRemarksSuccess");
                        // 更新缓存的备注信息
                        ClubDataManager.getInstance().updateMemberRemark(this.clubId, this.targetUserId, mark, markDetail);
                        this.refreshMemberInfo();
                        this.closeDialog();
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    OKGameManager.getInstance().showToastById("club.editRemarksFailed");
                }
            })
            .finally(() => {
                if (cc.isValid(this.node)) {
                    OKGameManager.getInstance().hideLoading();
                }
                // 重置请求状态
                this.isRequesting = false;
            });
    }
}
