export default class UIClubCashierSelectItem_auto {
    node:cc.Node = null;   
	ClubCashierSelectItem: cc.Node;
	toggleSelect: cc.Node;
	Background: cc.Node;
	checkmark: cc.Node;
	userHeadNode: cc.Node;
	headIcon: cc.Node;
	headFrameIcon: cc.Node;
	userIDLayer: cc.Node;
	userID: cc.Node;
	roleTypeImg: cc.Node;
	CurrencyBgImg: cc.Node;
	clubChipIcon: cc.Node;
	freeTxt: cc.Node;
	labelNoteName: cc.Node;
	labelName: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierSelectItem = this.node;
		this.toggleSelect = this.ClubCashierSelectItem.getChildByName("toggleSelect");
		this.Background = this.toggleSelect.getChildByName("Background");
		this.checkmark = this.toggleSelect.getChildByName("checkmark");
		this.userHeadNode = this.ClubCashierSelectItem.getChildByName("userHeadNode");
		this.headIcon = this.userHeadNode.getChildByName("headIcon");
		this.headFrameIcon = this.userHeadNode.getChildByName("headFrameIcon");
		this.userIDLayer = this.ClubCashierSelectItem.getChildByName("userIDLayer");
		this.userID = this.userIDLayer.getChildByName("userID");
		this.roleTypeImg = this.userIDLayer.getChildByName("roleTypeImg");
		this.CurrencyBgImg = this.ClubCashierSelectItem.getChildByName("CurrencyBgImg");
		this.clubChipIcon = this.CurrencyBgImg.getChildByName("clubChipIcon");
		this.freeTxt = this.CurrencyBgImg.getChildByName("freeTxt");
		this.labelNoteName = this.ClubCashierSelectItem.getChildByName("labelNoteName");
		this.labelName = this.ClubCashierSelectItem.getChildByName("labelName");

    }
}
