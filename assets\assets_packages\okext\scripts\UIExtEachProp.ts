const { ccclass, menu, property } = cc._decorator;

@ccclass
@menu('okext/UIExtEachProp')
export default class UIExtEachProp extends cc.Component {
    @property(cc.Node)
    icon: cc.Node = null;
    @property(dragonBones.ArmatureDisplay)
    anim: dragonBones.ArmatureDisplay = null;
    @property(sp.Skeleton)
    anim2: sp.Skeleton = null;

    protected onLoad(): void {
        if (this.anim) {
            this.anim.addEventListener(dragonBones.EventObject.COMPLETE, () => {
                this.node.removeFromParent(true)
                this.node.destroy();
            }, this);
        }

        if (this.anim2) {
            this.anim2.setCompleteListener(() => {
                this.node.removeFromParent(true)
                this.node.destroy();
            });
        }
        this.node.on('PLAY_EACH_PROP', this.playAnim.bind(this), this)
    }

    start() {
        // this.playAnim();
    }

    playAnim() {
        this.icon.active = false;
        //播放龙骨动画
        if (this.anim) {
            this.anim.node.active = true;
            let armatures = this.anim.getArmatureNames()
            let animations = this.anim.getAnimationNames(armatures[0])
            this.anim.playAnimation(animations[0], 1)
        }
        //播放spine动画
        if (this.anim2) {
            this.anim2.node.active = true;
        }
    }
}
