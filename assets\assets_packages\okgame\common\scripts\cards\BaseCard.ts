const { ccclass, menu, property } = cc._decorator;

@ccclass
@menu("cards/BaseCard")
export default class BaseCard extends cc.Component {
    @property(cc.Sprite)
    spCardValue: cc.Sprite = null;
    @property(cc.Sprite)
    spCardBg: cc.Sprite = null;
    //释放函数
    releaseCard: Function = null
    //---
    protected _spriteAtlas: cc.SpriteAtlas = null
    protected _cardValue: number = -1;
    //----
    set cardValue(card: number) {
        this._cardValue = card;
        this.updateView();
    }
    get cardValue(): number {
        return this._cardValue;
    }

    get isValidCard(): boolean{
        if (this._cardValue >= 101 && this._cardValue <= 113) {
            return true;
        }
        if (this._cardValue >= 201 && this._cardValue <= 213) {
            return true;
        }
        if (this._cardValue >= 301 && this._cardValue <= 313) {
            return true;
        }
        if (this._cardValue >= 401 && this._cardValue <= 413) {
            return true;
        }
        return false;
    }

    onDestroy() {
        this.releaseCard && this.releaseCard(this)
    }

    setCardStyle(spriteAtlas: cc.SpriteAtlas) {
        this._spriteAtlas = spriteAtlas;
        this.updateView();
    }

    getCardValueName(): string {
        if(this.isValidCard){
            return this._cardValue.toString();
        }
        return '-1'
    }

    updateView() {
        if (!this.spCardValue || !this._spriteAtlas) {
            return;
        }
        let name = this.getCardValueName();
        this.spCardValue.spriteFrame = this._spriteAtlas.getSpriteFrame(name);
    }

    setCardBgStyle(spriteFrame: cc.SpriteFrame) {
        if (!this.spCardBg) {
            return
        }
        this.spCardBg.spriteFrame = spriteFrame;
    }
}
