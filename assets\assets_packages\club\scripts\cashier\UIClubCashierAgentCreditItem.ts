import UBaseView from "../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UIIdentityIcon from "../../../okgame/common/scripts/UIIdentityIcon";
import { okpb } from "../../../okgame/proto/proto_msg";
import UIClubCashierAgentCreditItem_auto from "./UIClubCashierAgentCreditItem_auto";
import UIClubCashierAgentCreditView from "./UIClubCashierAgentCreditView";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierAgentCreditItem extends UBaseView {

	protected ui: UIClubCashierAgentCreditItem_auto = null;
	private _curData: okpb.ClubUserPO = null;
	private father: UIClubCashierAgentCreditView = null;

	onUILoad(): void {
		this.ui = new UIClubCashierAgentCreditItem_auto(this.node);
	}

	onEnable(): void {
		this.updateUI();
	}

	setData(father: UIClubCashierAgentCreditView, data: okpb.ClubUserPO) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;
		this.father = father;
		this.updateUI();
	}

	updateUI() {
		if (!cc.isValid(this.ui) || !this._curData) return;
		this.ui.labelNoteName.getComponent(cc.Label).string = "" + this._curData.nickname;
		this.ui.freeTxt.getComponent(cc.Label).string = "" + this._curData.chipBalance;
		this.ui.userID.getComponent(cc.Label).string = "ID:" + this._curData.userId;
		this.ui.roleTypeImg.getComponent(UPrefabContainer).getNodeComponent(UIIdentityIcon).setIcon(this._curData.identity);
	}

}