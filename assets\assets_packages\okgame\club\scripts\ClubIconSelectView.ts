import UBaseView from "../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import OKGameManager from "../../public/OKGameManager";
import ItemClubAvator from "./ItemClubAvator";
import OKClubManger from "./OKClubManger";

const { ccclass, property } = cc._decorator;
const TAG = "[ClubIconSelectView]";

/**
 * 选择头像的公共View
 * @description 创建俱乐部 or 编辑俱乐部信息
 *
 */
@ccclass
export default class ClubIconSelectView extends UBaseView {
    @property({ type: cc.Node, tooltip: "俱乐部头像容器" })
    private clubAvatorContainer: cc.Node = null;

    @property({ type: cc.Prefab, tooltip: "头像预制体" })
    private itemAvatorPrefab: cc.Prefab = null;

    @property(cc.ScrollView)
    private scrollView: cc.ScrollView = null;

    @property({ type: cc.Node, tooltip: "内容容器" })
    private content: cc.Node = null;

    @property({ type: cc.Node, tooltip: "头像遮罩" })
    private AvatorMask: cc.Node = null;

    @property({ type: cc.Node, tooltip: "小头像根节点" })
    private smallRootNode: cc.Node = null;

    @property({ type: cc.Node, tooltip: "小头像容器" })
    private smallAvatorContainer: cc.Node = null;
    /**
     * 大头像
     */
    private clubAvatorScript: ItemClubAvator = null;
    /**
     * 小头像
     */
    private smallAvatorScript: ItemClubAvator = null;
    /**
     * 最后选中的头像
     */
    private lastChoiceItemView: cc.Node = null;
    /**
     * 当前选择的头像:
     * !注意有可能是1、2、3，有可能是http开头的网址
     */
    private selectedClubUrl: string = "0";
    /**
     * 是否显示小头像
     */
    private isShowSmallAvator: boolean = false;
    /**
     * 脚本名称
     */
    private readonly scriptName: string = "ItemClubAvator";
    /**
     * 系统头像数量
     */
    private readonly MAX_AVATAR_COUNT: number = 7;
    /**
     * 头像选中回调
     */
    public onSelect: (avatarId: string) => void = null;

    onUILoad() {
        this.initView();
        this.initSystemIcons();
    }

    private initView(): void {
        this.initAvatarScript(this.clubAvatorContainer, "clubAvatorScript");
        this.initAvatarScript(this.smallAvatorContainer, "smallAvatorScript");

        this.clubAvatorContainer.active = true;
        this.AvatorMask.active = true;
        this.smallRootNode.active = false;
    }

    private initAvatarScript(container: cc.Node, scriptProperty: string): void {
        if (!container) {
            // cc.warn(TAG, `${scriptProperty} 容器为空`);
            return;
        }

        const prefabContainer = container.getComponent(UPrefabContainer);
        if (!prefabContainer) {
            // cc.warn(TAG, `${scriptProperty} 缺少 UPrefabContainer 组件`);
            return;
        }

        const script = prefabContainer.getNodeComponent(this.scriptName);
        if (!script) {
            // cc.warn(TAG, `${scriptProperty} 缺少 ${this.scriptName} 组件`);
            return;
        }

        script.setClickEnable(false);
        script.setAvatorId(0, null);
        this[scriptProperty] = script;
    }

    /**
     * 初始化系统头像
     */
    private initSystemIcons(): void {
        if (!this.content) {
            // cc.warn(TAG, "内容容器为空");
            return;
        }

        this.content.removeAllChildren();

        for (let i = 0; i < this.MAX_AVATAR_COUNT; i++) {
            const item = this.createAvatarItem(i);
            if (i === 0) {
                this.lastChoiceItemView = item;
                this.selectedClubUrl = i + "";
                item.getComponent(this.scriptName).setSelectedState(true);
            }
        }
    }

    /**
     * 动物ItemView
     * @param index 头像索引
     * @returns 头像节点
     */
    private createAvatarItem(index: number): cc.Node {
        const item = cc.instantiate(this.itemAvatorPrefab);
        const itemClubAvatar = item.getComponent(this.scriptName);
        itemClubAvatar.setAvatorId(index + "", this.handleAvatarSelection.bind(this, item, `${index}`));
        this.content.addChild(item);
        return item;
    }

    /**
     * 设置头像选中回调
     * @param callback 回调函数
     */
    public setSelectedCallback(callback: (avatarUrl: string) => void): void {
        this.onSelect = callback;
    }

    /**
     * 选择图片
     */
    public toSelectImage(): void {
        OKGameManager.getInstance().openCamera(500, (imageUrl: string) => {
            if (imageUrl) {
                console.log(TAG, "选择图片: ", imageUrl);
                this.selectedClubUrl = imageUrl;
                this.updateLastSelectedItem(null, imageUrl);
                this.updateAvatarDisplay(imageUrl);
                this.notifySelection(imageUrl);
            } else {
                console.log(TAG, "选择图片失败");
            }
        });
    }

    /**
     * 显示选中的小头像
     * @description 用在编辑俱乐部资料时
     * @param isShow 是否显示
     */
    public showSmallAvator(isShow: boolean): void {
        this.isShowSmallAvator = isShow;
        this.smallRootNode.active = isShow;
        this.clubAvatorContainer.active = !isShow;
        this.AvatorMask.active = !isShow;
        // 小头像要显示边框
        if (this.smallAvatorScript) {
            this.smallAvatorScript.showNormalMask(isShow);
        }
    }

    /**
     * 点选了某个动物头像
     * @param item 头像节点
     * @param avatorIconID 头像ID
     */
    private handleAvatarSelection(item: cc.Node, avatorIconID: string): void {
        if (!item) {
            // cc.warn(TAG, "头像节点为空");
            return;
        }

        this.updateLastSelectedItem(item, avatorIconID);
        this.updateAvatarDisplay(avatorIconID);
        this.notifySelection(avatorIconID);
    }

    /**
     * 更新最后选中的头像
     * @description 主要是处理Item头像的选中状态
     * @param item 头像节点
     * @param avatorIconID 头像ID
     */
    private updateLastSelectedItem(item: cc.Node, avatorIconID: string): void {
        // console.log(TAG, "更新最后选中的头像: ", item, avatorIconID);
        if (this.lastChoiceItemView) {
            this.lastChoiceItemView.getComponent(this.scriptName).setSelectedState(false);
        }
        this.lastChoiceItemView = item;
        this.selectedClubUrl = avatorIconID;
        if (item !== null) {
            item.getComponent(this.scriptName).setSelectedState(true);
        }
    }

    /**
     * 更新选中头像的显示
     * @param avatorIconID 头像ID
     */
    private updateAvatarDisplay(avatorIconID: string): void {
        console.log(TAG, "更新选中头像的显示: ", avatorIconID);
        if (this.clubAvatorScript) {
            this.clubAvatorScript.setAvatorId(avatorIconID, null);
        }
        if (this.smallAvatorScript) {
            this.smallAvatorScript.setAvatorId(avatorIconID, null);
        }
    }

    /**
     * 通知头像选择变化
     * @param avatorIconID 头像ID
     */
    private notifySelection(avatorIconID: string): void {
        if (this.onSelect) {
            this.onSelect(avatorIconID);
        }
    }

    /**
     * 对外：获取当前选中的头像ID
     */
    public getSelectedAvatarUrl(): string {
        return this.selectedClubUrl;
    }

    /**
     * 对外：设置选中头像
     * @param iconUrlOrId 头像URL或ID
     */
    public setSelectedAvatarId(iconUrlOrId: string): void {
        // 设置选中头像的展示
        this.updateAvatarDisplay(iconUrlOrId);
        const isWebUrl = OKClubManger.getInstance().isWebUrl(iconUrlOrId);
        if (isWebUrl) {
            console.log(TAG, "设置头像为网图: ", iconUrlOrId);
            this.updateLastSelectedItem(null, iconUrlOrId);
            this.notifySelection(iconUrlOrId);
        } else {
            const children = this.content.children;
            console.log(TAG, "设置头像为系统动物图: ", iconUrlOrId, children.length);
            for (const item of children) {
                const comp = item.getComponent(this.scriptName);
                if (comp && String(comp.getClubUrl()) === String(this.getImgNum(iconUrlOrId))) {
                    console.log(TAG, "选择了系统头像: ", iconUrlOrId);
                    this.handleAvatarSelection(item, iconUrlOrId);
                    break;
                }
            }
            if (this.scrollView) {
                // 宽度为200，滑动到200的倍数
                const imgNum = this.getImgNum(iconUrlOrId);
                if (!isNaN(imgNum)) {
                    let offset = 0;
                    if (imgNum > 4) {
                        offset = (imgNum - 4) * 200 - 100;
                    }
                    console.log(TAG, "滑动到: ", offset);
                    this.scrollView.scrollTo(new cc.Vec2(offset, 0), 0.3);
                }
            }
        }
    }

    /**
     * 获取图片ID
     * @param iconUrlOrId 原始头像URL
     * @returns 图片ID
     */
    private getImgNum(iconUrlOrId: string): number {
        return Number(OKClubManger.getInstance().processAvatarUrl(iconUrlOrId));
    }
}
