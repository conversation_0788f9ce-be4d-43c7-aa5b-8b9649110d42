import UBaseView from "../../../../../framwork/widget/UBaseView";
import OKGameUtils from "../../../public/OKGameUtils";
import UICareerMainView_auto from "./UICareerMainView_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UICareerMainView]";

/** toggle类型枚举 */
enum ToggleType {
    YESTERDAY = 0,
    LAST_SEVEN_DAYS = 1,
    SELECT_DATE = 2,
}

/** toggle配置接口 */
interface ToggleConfig {
    toggleNode: cc.Node | null;
    labelNode: cc.Node | null;
    checkMarkNode: cc.Node | null;
    iconNode: cc.Node | null;
}

/**
 * 生涯主界面
 *
 * @export
 * @class UICareerMainView
 * @extends {UBaseView}
 */
@ccclass
@menu("okgame/UICareerMainView")
export default class UICareerMainView extends UBaseView {
    //#region 属性
    protected ui: UICareerMainView_auto = null;

    // 当前选中的toggle索引
    private currentSelectedIndex: number = 0;

    // 颜色常量
    private readonly SELECTED_COLOR: cc.Color = OKGameUtils.hexToColor("#FFFFFF");
    private readonly UNSELECTED_COLOR: cc.Color = OKGameUtils.hexToColor("#9399c7");

    // toggle配置数组
    private TOGGLE_CONFIG: ToggleConfig[] = [];

    // 缓存的Label组件
    private labelComponents: cc.Label[] = [];

    //#endregion

    //#region 生命周期
    onUILoad(): void {
        this.ui = new UICareerMainView_auto(this.node);
        this.initUI();
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 初始化UI
     *
     * @private
     * @memberof UICareerMainView
     */
    private initUI() {
        this.initToggleConfig();
        this.cacheComponents();
        this.registerEvents();
        this.updateToggleStates();
        this.updateCareerData();
    }

    /**
     * 初始化toggle配置
     * @private
     * @memberof UICareerMainView
     */
    private initToggleConfig() {
        this.TOGGLE_CONFIG = [
            {
                toggleNode: this.ui.toggleYesterday,
                labelNode: this.ui.yesterday,
                checkMarkNode: this.ui.yesterdayCheckMark,
                iconNode: null,
            },
            {
                toggleNode: this.ui.toggleLastSeven,
                labelNode: this.ui.lastSevenDay,
                checkMarkNode: this.ui.lastCheckMark,
                iconNode: null,
            },
            {
                toggleNode: this.ui.toggleSelect,
                labelNode: this.ui.selectDate,
                checkMarkNode: this.ui.selectCheckMark,
                iconNode: this.ui.selectIcon,
            },
        ];
    }

    /**
     * 缓存常用组件
     *
     * @private
     * @memberof UICareerMainView
     */
    private cacheComponents() {
        this.TOGGLE_CONFIG.forEach((config) => {
            if (config.labelNode) {
                const comp = config.labelNode.getComponent(cc.Label);
                if (comp) this.labelComponents.push(comp);
            }
        });
    }

    /**
     * 注册事件
     *
     * @private
     * @memberof UICareerMainView
     */
    private registerEvents() {
        // 注册按钮事件
        this.onRegisterEvent(this.ui.backButton, this.onBackButtonClick.bind(this));
        this.onRegisterEvent(this.ui.favButton, this.onCreateButtonClick.bind(this));

        // 注册toggle事件
        const toggleHandlers = [this.onYesterdayToggleClick.bind(this), this.onLastSevenToggleClick.bind(this), this.onSelectToggleClick.bind(this)];
        this.TOGGLE_CONFIG.forEach((config, index) => {
            if (config.toggleNode) {
                this.onRegisterEvent(config.toggleNode, toggleHandlers[index]);
            }
        });
    }

    /**
     * 更新所有toggle的选中状态
     * 会根据currentSelectedIndex自动设置正确的选中状态
     * 并触发相关UI更新（颜色、图标等）
     *
     * @private
     * @memberof UICareerMainView
     */
    private updateToggleStates() {
        for (let i = 0; i < this.TOGGLE_CONFIG.length; i++) {
            this.updateToggleState(i, i === this.currentSelectedIndex);
        }
    }

    /**
     * 更新单个toggle状态
     *
     * @private
     * @param {number} index toggle索引
     * @param {boolean} isSelected 是否选中
     * @memberof UICareerMainView
     */
    private updateToggleState(index: number, isSelected: boolean) {
        const config = this.TOGGLE_CONFIG[index];
        if (!config) return;

        // 更新label颜色
        if (this.labelComponents[index]) {
            this.labelComponents[index].node.color = isSelected ? this.SELECTED_COLOR : this.UNSELECTED_COLOR;
        }

        // 更新checkmark显示状态
        if (config.checkMarkNode) {
            config.checkMarkNode.active = isSelected;
        }

        // 更新箭头方向（仅针对select toggle）
        if (index === ToggleType.SELECT_DATE && config.iconNode) {
            this.updateArrowDirection(config.iconNode, isSelected);
        }
    }

    /**
     * 更新箭头方向
     *
     * @private
     * @param {cc.Node} iconNode 箭头节点
     * @param {boolean} isSelected 是否选中
     * @memberof UICareerMainView
     */
    private updateArrowDirection(iconNode: cc.Node, isSelected: boolean) {
        if (!iconNode) return;

        // 选中时箭头向下（270度），未选中时箭头向上（90度）
        const targetAngle = isSelected ? 270 : 90;

        // 使用动画更新角度
        cc.tween(iconNode).to(0.3, { angle: targetAngle }, { easing: "backOut" }).start();

        this.log(`更新箭头方向 - 选中: ${isSelected}, 角度: ${targetAngle}`);
    }

    /**
     * 更新生涯数据
     *
     * @private
     * @memberof UICareerMainView
     */
    private updateCareerData() {
        // TODO: 根据当前选中的toggle更新生涯数据显示
        this.log(`更新生涯数据 - 当前选中索引: ${this.currentSelectedIndex}`);
    }

    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UICareerMainView
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }
    //#endregion

    //#region 事件处理
    /**
     * 返回按钮点击事件
     *
     * @private
     * @memberof UICareerMainView
     */
    private onBackButtonClick() {
        this.log("返回按钮点击");
        // TODO: 处理返回逻辑
    }

    /**
     * 创建按钮点击事件
     *
     * @private
     * @memberof UICareerMainView
     */
    private onCreateButtonClick() {
        this.log("创建按钮点击");
        // TODO: 处理创建逻辑
    }

    /**
     * 昨天toggle点击事件
     *
     * @private
     * @memberof UICareerMainView
     */
    private onYesterdayToggleClick() {
        this.handleToggleClick(ToggleType.YESTERDAY);
    }

    /**
     * 最近7天toggle点击事件
     *
     * @private
     * @memberof UICareerMainView
     */
    private onLastSevenToggleClick() {
        this.handleToggleClick(ToggleType.LAST_SEVEN_DAYS);
    }

    /**
     * 选择日期toggle点击事件
     *
     * @private
     * @memberof UICareerMainView
     */
    private onSelectToggleClick() {
        this.handleToggleClick(ToggleType.SELECT_DATE);
    }

    /**
     * 处理toggle点击事件
     *
     * @private
     * @param {number} index toggle索引
     * @memberof UICareerMainView
     */
    private handleToggleClick(index: number) {
        if (this.currentSelectedIndex === index) {
            return;
        }

        this.currentSelectedIndex = index;
        this.updateToggleStates();
        this.updateCareerData();
    }
    //#endregion

    //#region getter/setter
    /**
     * 获取当前选中的索引
     *
     * @readonly
     * @memberof UICareerMainView
     */
    get selectedIndex(): number {
        return this.currentSelectedIndex;
    }
    //#endregion

    //#region 对外公开的方法
    /**
     * 刷新界面数据
     *
     * @memberof UICareerMainView
     */
    refreshData() {
        this.updateCareerData();
    }

    /**
     * 设置选中的toggle
     *
     * @param {number} index toggle索引 (0: 昨天, 1: 最近7天, 2: 选择日期)
     * @memberof UICareerMainView
     */
    setSelectedToggle(index: number) {
        if (index >= 0 && index < this.TOGGLE_CONFIG.length) {
            this.currentSelectedIndex = index;
            this.updateToggleStates();
            this.updateCareerData();
        } else {
            this.log(`无效的toggle索引: ${index}`);
        }
    }
    //#endregion
}
