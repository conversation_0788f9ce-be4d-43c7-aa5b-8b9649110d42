import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import SearchView from "../members/SearchView";
import UIClubExportDataView_auto from "./UIClubExportDataView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubExportDataView extends UBaseDialog {

	protected ui: UIClubExportDataView_auto = null;
	private searchContainer: SearchView = null;

	onUILoad(): void {
		this.ui = new UIClubExportDataView_auto(this.node);
		//id搜索
		this.searchContainer = this.ui.SearchLayer.getComponent(UPrefabContainer).getNodeComponent(SearchView);
		this.onRegisterEvent(this.ui.btnQuestion, this.onBtnQuestion.bind(this));
		this.onRegisterEvent(this.ui.btnTime, this.onBtnTime.bind(this));
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.onRegisterEvent(this.ui.btnExport, this.onBtnExport.bind(this));

	}

	updateLabelTimes() {
		this.ui.labelTimeStart.getComponent(cc.Label).string = "";
		this.ui.labelTimeEnd.getComponent(cc.Label).string = "";
	}

	updateClubLabel() {
		this.ui.labelClubData.getComponent(cc.Label).string = "";
		this.ui.labelTransactionData.getComponent(cc.Label).string = "";
		this.ui.labelDiamondData.getComponent(cc.Label).string = "";
		this.ui.labelTotalNum.getComponent(cc.Label).string = "";
	}

	//打开选择时间按钮
	onBtnTime() {

	}

	//点击问题按钮
	onBtnQuestion() {

	}

	//导出数据
	onBtnExport() {

	}

	onToggleSelect() {

	}
}