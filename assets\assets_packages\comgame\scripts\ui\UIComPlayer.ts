import UBaseView from "../../../../framwork/widget/UBaseView"

const { ccclass, property, menu } = cc._decorator

@ccclass
export default class UIComPlayer extends UBaseView {
    private _emojiView: cc.Node = null

    
    getPos(): cc.Vec3 {
        return this.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
    }

    initComView(emojiView: cc.Node) {
        this._emojiView = emojiView
    }

    clearComData() {
        this._emojiView && this._emojiView.removeAllChildren(true);
    }

    playEmojiAnim(anim: cc.Node) {
        if (this._emojiView) {
            anim.position = cc.Vec3.ZERO;
            anim.parent = this._emojiView;
            anim.active = true;
        } else {
            anim.destroy();
        }
    }
}
