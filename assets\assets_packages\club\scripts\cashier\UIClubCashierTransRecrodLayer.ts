import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubAPI from "../ClubAPI";
import SearchView from "../members/SearchView";
import UIClubCashierTransRecrodLayer_auto from "./UIClubCashierTransRecrodLayer_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierTransRecrodLayer extends UBaseSFixedDialog {

	protected ui: UIClubCashierTransRecrodLayer_auto = null;
	private searchContainer: SearchView = null;
	/**
	 * 俱乐部信息
	*/
	private clubInfo: okpb.ClubResp = null;

	onUILoad(): void {
		super.onUILoad();
		this.ui = new UIClubCashierTransRecrodLayer_auto(this.node);
		//id搜索
		this.searchContainer = this.ui.SearchLayer.getComponent(UPrefabContainer).getNodeComponent(SearchView);
		//缺省页
		this.initListEmpty("room.noTables");
		//初始场景，当前层隐藏
		this.node.parent.active = false;
	}

	onEnable(): void {
		this.reqData();
	}

	async reqData() {
		let createTime = new Date().getTime();
		if (this.listData && this.listData.length) {
			createTime = this.listData[this.listData.length - 1].createTime;
		}
		let data = await ClubAPI.getInstance().ReqGetClubChipTransactionList(createTime, this.pageLimit, okpb.ClubChipType.CCT_ALL);
		this.setListData(data, data.data);
	}
}