export default class UIClubCareerView_auto {
    node:cc.Node = null;   
	ClubCareerView: cc.Node;
	mask: cc.Node;
	mainContainer: cc.Node;
	navContainer: cc.Node;
	titleLabel: cc.Node;
	backButton: cc.Node;
	backButtonBg: cc.Node;
	detailContainer: cc.Node;
	dateNode: cc.Node;
	arrowLeft: cc.Node;
	dateLabel: cc.Node;
	arrowRight: cc.Node;
	dateToggleContainer: cc.Node;
	toggleYesterday: cc.Node;
	Background: cc.Node;
	checkmark: cc.Node;
	yesterday: cc.Node;
	toggleLastSeven: cc.Node;
	lastSevenDay: cc.Node;
	toggleSelect: cc.Node;
	selectDate: cc.Node;
	feeNode: cc.Node;
	profitNode: cc.Node;
	profitValue: cc.Node;
	profitTitle: cc.Node;
	line: cc.Node;
	serviceNode: cc.Node;
	serviceValue: cc.Node;
	serviceTitle: cc.Node;
	dataList: cc.Node;
	scrollBar: cc.Node;
	bar: cc.Node;
	view: cc.Node;
	content: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCareerView = this.node;
		this.mask = this.ClubCareerView.getChildByName("mask");
		this.mainContainer = this.ClubCareerView.getChildByName("mainContainer");
		this.navContainer = this.mainContainer.getChildByName("navContainer");
		this.titleLabel = this.navContainer.getChildByName("titleLabel");
		this.backButton = this.navContainer.getChildByName("backButton");
		this.backButtonBg = this.backButton.getChildByName("backButtonBg");
		this.detailContainer = this.mainContainer.getChildByName("detailContainer");
		this.dateNode = this.detailContainer.getChildByName("dateNode");
		this.arrowLeft = this.dateNode.getChildByName("arrowLeft");
		this.dateLabel = this.dateNode.getChildByName("dateLabel");
		this.arrowRight = this.dateNode.getChildByName("arrowRight");
		this.dateToggleContainer = this.detailContainer.getChildByName("dateToggleContainer");
		this.toggleYesterday = this.dateToggleContainer.getChildByName("toggleYesterday");
		this.Background = this.toggleYesterday.getChildByName("Background");
		this.checkmark = this.toggleYesterday.getChildByName("checkmark");
		this.yesterday = this.toggleYesterday.getChildByName("yesterday");
		this.toggleLastSeven = this.dateToggleContainer.getChildByName("toggleLastSeven");
		this.lastSevenDay = this.toggleLastSeven.getChildByName("lastSevenDay");
		this.toggleSelect = this.dateToggleContainer.getChildByName("toggleSelect");
		this.selectDate = this.toggleSelect.getChildByName("selectDate");
		this.feeNode = this.detailContainer.getChildByName("feeNode");
		this.profitNode = this.feeNode.getChildByName("profitNode");
		this.profitValue = this.profitNode.getChildByName("profitValue");
		this.profitTitle = this.profitNode.getChildByName("profitTitle");
		this.line = this.feeNode.getChildByName("line");
		this.serviceNode = this.feeNode.getChildByName("serviceNode");
		this.serviceValue = this.serviceNode.getChildByName("serviceValue");
		this.serviceTitle = this.serviceNode.getChildByName("serviceTitle");
		this.dataList = this.mainContainer.getChildByName("dataList");
		this.scrollBar = this.dataList.getChildByName("scrollBar");
		this.bar = this.scrollBar.getChildByName("bar");
		this.view = this.dataList.getChildByName("view");
		this.content = this.view.getChildByName("content");

    }
}
