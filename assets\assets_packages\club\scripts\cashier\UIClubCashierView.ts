import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import { CLUB_EVENT } from "../ClubManger";
import UIClubCashierChipRequestLayer from "./UIClubCashierChipRequestLayer";
import UIClubCashierTransactionLayer from "./UIClubCashierTransactionLayer";
import UIClub<PERSON>ashierView_auto from "./UIClubCashierView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierView extends UBaseDialog {

	protected ui: UIClubCashierView_auto = null;
	//当前layer选择
	private _cashierSelectIndex: number = 0;

	onUILoad(): void {
		this.ui = new UIClubCashierView_auto(this.node);
		this.onRegisterEvent(this.ui.btnBack, this.playExitAnim.bind(this));
	}

	onShow(): void {
		// ClubDataManager.getInstance().requestClubCounterInfo();
	}

	// 根据选择显示相应的层
	updateCashierLayer() {
		this.ui.CashierChipRequestLayer.active = this._cashierSelectIndex == 2;
		this.ui.CashierTransRecrodLayer.active = this._cashierSelectIndex == 1;
		this.ui.CashierTransactionLayer.active = this._cashierSelectIndex == 0;
	}

	onToggleSelect(toggle: cc.Toggle) {
		let index = Number(toggle.node.name.charAt(toggle.node.name.length - 1)) - 1;
		this._cashierSelectIndex = index;
		this.updateCashierLayer();
	}

}