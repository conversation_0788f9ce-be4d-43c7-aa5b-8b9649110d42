import UBaseView from "../../../../framwork/widget/UBaseView";
import UIClubDetailDataItem_auto from "./UIClubDetailDataItem_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubDetailDataItem extends UBaseView {

	protected ui: UIClubDetailDataItem_auto = null;
	private _curData = null;

	onUILoad(): void {
		this.ui = new UIClubDetailDataItem_auto(this.node);
	}

	setData(data: any) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;

		this.ui.labelTime.getComponent(cc.Label).string = "";
		this.ui.labelID.getComponent(cc.Label).string = "";
		this.ui.labelChip.getComponent(cc.Label).string = "";
		this.ui.labelNLH.getComponent(cc.Label).string = "";
		this.ui.labelserviceFee.getComponent(cc.Label).string = "";
	}
}