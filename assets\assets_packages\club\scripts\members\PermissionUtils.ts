import { ULanguage } from "../../../../framwork/language/ULanguage";
import { okpb } from "../../../okgame/proto/proto_msg";
import { IPermission } from "./MemberTypes";

/**
 * 权限枚举定义
 */
export enum PermissionType {
    /**
     * 柜台
     */
    COUNTER = 1,
    /**
     * 收回筹码
     */
    TAKE_CHIPS = 2,
    /**
     * 发放筹码
     */
    SEND_CHIPS = 4,
    /**
     * 创建牌桌
     */
    MAKE_DESK = 8,
    /**
     * 成员列表
     */
    USER_LIST = 16,
    /**
     * 新成员审核
     */
    NEW_AUDIT = 32,
    /**
     * 删除成员
     */
    DEL_USER = 64,
    /**
     * 更改角色
     */
    IDENTITY = 128,
    /**
     * 数据
     */
    READ_DATA = 256,
    /**
     * 修改俱乐部资料
     */
    EDIT_INFO = 512,
}

/**
 * 俱乐部权限常量定义
 */
export class ClubPermission {
    static readonly COUNTER = 1; // 柜台
    static readonly TAKE_CHIPS = 1 << 1; // 收回筹码
    static readonly SEND_CHIPS = 1 << 2; // 发放筹码
    static readonly MAKE_DESK = 1 << 3; // 创建牌桌
    static readonly USER_LIST = 1 << 4; // 成员列表
    static readonly NEW_AUDIT = 1 << 5; // 新成员审核
    static readonly DEL_USER = 1 << 6; // 删除成员
    static readonly IDENTITY = 1 << 7; // 更改角色
    static readonly READ_DATA = 1 << 8; // 数据
    static readonly EDIT_INFO = 1 << 9; // 修改俱乐部资料

    // 管理员全部权限
    static readonly ADMIN_ALL_PERMISSIONS =
        ClubPermission.COUNTER |
        ClubPermission.TAKE_CHIPS |
        ClubPermission.SEND_CHIPS |
        ClubPermission.MAKE_DESK |
        ClubPermission.USER_LIST |
        ClubPermission.NEW_AUDIT |
        ClubPermission.DEL_USER |
        ClubPermission.IDENTITY |
        ClubPermission.READ_DATA |
        ClubPermission.EDIT_INFO;

    // 代理全部权限
    static readonly AGENT_ALL_PERMISSIONS = ClubPermission.TAKE_CHIPS | ClubPermission.SEND_CHIPS | ClubPermission.READ_DATA;
}

/**
 * 权限工具类
 */
export class PermissionUtils {
    /**
     * 权限配置映射表
     */
    private static readonly PERMISSION_CONFIGS: Pick<IPermission, "value" | "langKey">[] = [
        // 柜台
        { value: ClubPermission.COUNTER, langKey: "club.permission.counter" },
        // 收回筹码
        { value: ClubPermission.TAKE_CHIPS, langKey: "club.permission.takeChips" },
        // 发放筹码
        { value: ClubPermission.SEND_CHIPS, langKey: "club.permission.sendChips" },
        // 创建牌桌
        { value: ClubPermission.MAKE_DESK, langKey: "club.permission.makeDesk" },
        // 成员列表
        { value: ClubPermission.USER_LIST, langKey: "club.permission.userList" },
        // 新成员审核
        { value: ClubPermission.NEW_AUDIT, langKey: "club.permission.newAudit" },
        // 删除成员
        { value: ClubPermission.DEL_USER, langKey: "club.permission.delUser" },
        // 更改职位
        { value: ClubPermission.IDENTITY, langKey: "club.permission.identity" },
        // 数据
        { value: ClubPermission.READ_DATA, langKey: "club.permission.readData" },
        // 修改俱乐部资料
        { value: ClubPermission.EDIT_INFO, langKey: "club.permission.editInfo" },
    ];

    /**
     * 权限位掩码=>IPermission数组
     * @description UI上展示权限列表
     */
    static parsePermissionsFromBits(permissionBits: number): IPermission[] {
        const permissions: IPermission[] = [];

        this.PERMISSION_CONFIGS.forEach((config) => {
            permissions.push({
                name: ULanguage.getInstance().getLangByID(config.langKey),
                isOpen: (permissionBits & config.value) !== 0,
                value: config.value,
                langKey: config.langKey,
            });
        });

        return permissions;
    }

    /**
     * IPermission数组=>权限位掩码
     * @description 用于同步到服务器
     */
    static convertPermissionsToBits(permissions: IPermission[]): number {
        let permissionBits = 0;

        permissions.forEach((permission) => {
            if (permission.isOpen) {
                // 优先使用permission.value，如果没有则通过langKey查找
                const permissionValue =
                    permission.value ||
                    this.PERMISSION_CONFIGS.find((c) => c.langKey === permission.langKey)?.value ||
                    this.PERMISSION_CONFIGS.find((c) => ULanguage.getInstance().getLangByID(c.langKey) === permission.name)?.value;

                if (permissionValue) {
                    permissionBits |= permissionValue;
                }
            }
        });

        return permissionBits;
    }

    /**
     * 检查是否有关闭的权限
     * @description 成员列表和代理列表中，有🔒的功能展示，需要检查权限位掩码内是否有关闭的权限
     * @param permissionBits 权限位掩码
     * @returns 是否有关闭的权限
     */
    static hasClosedPermission(permissionBits: number, userIdentity: okpb.Identity): boolean {
        // 管理员
        if (userIdentity === okpb.Identity.IDE_MANAGER) {
            const result = (permissionBits & ClubPermission.ADMIN_ALL_PERMISSIONS) !== ClubPermission.ADMIN_ALL_PERMISSIONS;
            console.log("[PermissionUtils]", "管理员的权限位掩码： ", permissionBits, ",是否有完整的权限:", !result);
            return result;
        }
        // 代理
        if (userIdentity === okpb.Identity.IDE_AGENT) {
            const result = (permissionBits & ClubPermission.AGENT_ALL_PERMISSIONS) !== ClubPermission.AGENT_ALL_PERMISSIONS;
            console.log("[PermissionUtils]", "代理的权限位掩码：", permissionBits, ",是否有完整的权限:", !result);
            return result;
        }
        return false;
    }

    /**
     * 根据权限名称获取权限位值
     * @deprecated 建议使用 PermissionType 枚举代替
     * @param permissionName 权限名称，例如: ULanguage.getInstance().getLangByID("club.permission.counter")
     * @returns 权限位值，如果找不到返回0
     */
    static getPermissionBitByName(permissionName: string): number {
        const config = this.PERMISSION_CONFIGS.find((config) => ULanguage.getInstance().getLangByID(config.langKey) === permissionName);
        return config ? config.value : 0;
    }

    /**
     * 检查是否拥有所有指定权限
     * @param permissionBits 权限位掩码
     * @param permissions 要检查的权限枚举数组
     * @returns 是否拥有所有权限
     */
    static hasAllPermissions(permissionBits: number, permissions: PermissionType[]): boolean {
        return permissions.every((permission) => this.hasPermission(permissionBits, permission));
    }

    /**
     * 检查是否拥有指定权限
     * @param permissionBits 权限位掩码
     * @param permission 要检查的权限枚举
     * @returns 是否拥有该权限
     */
    static hasPermission(permissionBits: number, permission: PermissionType): boolean {
        return (permissionBits & permission) !== 0;
    }

    /**
     * 检查是否拥有多个权限中的任意一个
     * @param permissionBits 权限位掩码
     * @param permissions 要检查的权限枚举数组
     * @returns 是否拥有任意一个权限
     */
    static hasAnyPermission(permissionBits: number, permissions: PermissionType[]): boolean {
        return permissions.some((permission) => this.hasPermission(permissionBits, permission));
    }

    /**
     * 是否有柜台权限
     * @param permissionBits 权限位掩码
     * @returns 是否有柜台权限
     */
    static hasCounterPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.COUNTER);
    }

    /**
     * 检查是否有收回筹码功能
     * @param permissionBits 权限位掩码
     * @returns 是否有收回筹码权限
     */
    static hasTakeChipsPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.TAKE_CHIPS);
    }

    /**
     * 检查是否有发放筹码权限
     * @param permissionBits 权限位掩码
     * @returns 是否有发放筹码权限
     */
    static hasSendChipsPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.SEND_CHIPS);
    }

    /**
     * 是否有创建牌桌权限
     * @param permissionBits 权限位掩码
     * @returns 是否有创建牌桌权限
     */
    static hasMakeDeskPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.MAKE_DESK);
    }

    /**
     * 是否有成员列表权限
     * @param permissionBits 权限位掩码
     * @returns 是否有成员列表权限
     */
    static hasUserListPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.USER_LIST);
    }

    /**
     * 是否有新成员审核权限
     * @param permissionBits 权限位掩码
     * @returns 是否有新成员审核权限
     */
    static hasNewAuditPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.NEW_AUDIT);
    }

    /**
     * 是否有删除成员权限
     * @param permissionBits 权限位掩码
     * @returns 是否有删除成员权限
     */
    static hasDelUserPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.DEL_USER);
    }

    /**
     * 是否有更改角色权限
     * @param permissionBits 权限位掩码
     * @returns 是否有更改角色权限
     */
    static hasIdentityPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.IDENTITY);
    }

    /**
     * 是否有数据权限
     * @param permissionBits 权限位掩码
     * @returns 是否有数据权限
     */
    static hasReadDataPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.READ_DATA);
    }

    /**
     * 是否有修改俱乐部资料权限
     * @param permissionBits 权限位掩码
     * @returns 是否有修改俱乐部资料权限
     */
    static hasEditInfoPermission(permissionBits: number): boolean {
        return this.hasPermission(permissionBits, PermissionType.EDIT_INFO);
    }
}
