import { ULanguage } from "../../../../framwork/language/ULanguage";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubManger, { CLUB_UIID } from "../ClubManger";
import { AgentSortTypeEnum, SortTypeEnum, DetailFeatureEnum, IdentitySettingEnum } from "./MemberConst";
import { IClubMemberFullInfo, IPermissionConfig, IPermissionListConfigOptions } from "./MemberTypes";

const TAG = "[MemberHelper]";

/**
 * 成员模块帮助类
 * 用于存放成员模块中使用的公共方法
 */
export default class MemberHelper {
    /**
     * 是否有权限进入详情
     * @param myIdentity 当前用户角色
     * @param targetUserIdentity 目标用户角色
     * @returns 是否有权限
     */
    static hasPermissionToEnterDetail(myIdentity: okpb.Identity, targetUserIdentity: okpb.Identity): boolean {
        // 如果当前的角色是开房员或者普通成员的话，无权限
        if (myIdentity === okpb.Identity.IDE_TABLE_OPERATOR || myIdentity === okpb.Identity.IDE_GENERAL) {
            return false;
        }
        // 如果当前是代理，查看目标为创建者、管理员、开房员的话，无权限
        if (
            myIdentity === okpb.Identity.IDE_AGENT &&
            (targetUserIdentity === okpb.Identity.IDE_MASTER ||
                targetUserIdentity === okpb.Identity.IDE_MANAGER ||
                targetUserIdentity === okpb.Identity.IDE_TABLE_OPERATOR)
        ) {
            return false;
        }
        return true;
    }

    /**
     * 格式化服务费显示
     * @param fee 服务费数值
     * @returns 格式化后的服务费字符串
     */
    static formatServiceFee(fee: number): string {
        return fee.toFixed(2);
    }

    /**
     * 格式化盈亏显示
     * @param profit 盈亏数值
     * @returns 格式化后的盈亏字符串
     */
    static formatProfit(profit: number): string {
        return profit >= 0 ? `+${profit.toFixed(2)}` : profit.toFixed(2);
    }

    /**
     * 格式化时间显示
     * @param timestamp 时间戳
     * @returns 格式化后的时间字符串,精确到分钟，格式为：HH:mm : MM/dd
     */
    private static formatTimeBase(timestamp: number, separator: string): string {
        const date = new Date(timestamp);
        const time = `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
        const dateStr = `${String(date.getMonth() + 1).padStart(2, "0")}/${String(date.getDate()).padStart(2, "0")}`;
        return `${time}${separator}${dateStr}`;
    }

    static formatTime(timestamp: number): string {
        return this.formatTimeBase(timestamp, " \n");
    }

    static formatTime2(timestamp: number): string {
        return this.formatTimeBase(timestamp, " ");
    }

    /**
     * 获取上周的时间范围
     * @returns 上周一的00:00:00和上周日的23:59:59
     */
    static getLastWeekRange(): { mondayStart: number; sundayEnd: number } {
        const now = new Date();

        // 获取当前是星期几（0=周日，1=周一，...，6=周六）
        const currentDay = now.getDay() || 7; // 将周日（0）转换为7

        // 计算上周一的日期
        const lastMonday = new Date(now);
        lastMonday.setDate(now.getDate() - currentDay - 6);
        lastMonday.setHours(0, 0, 0, 0); // 设置为00:00:00

        // 计算上周日的日期
        const lastSunday = new Date(now);
        lastSunday.setDate(now.getDate() - currentDay); // 当前周一减1天
        lastSunday.setHours(23, 59, 59, 999); // 设置为23:59:59.999

        return {
            mondayStart: Math.floor(lastMonday.getTime()), // 转换为秒级时间戳
            sundayEnd: Math.floor(lastSunday.getTime()), // 转换为秒级时间戳
        };
    }

    /**
     * 获取最近7天的时间范围
     * @returns {startTime: number, endTime: number} 开始时间和结束时间的时间戳
     */
    static getLastSevenDaysRange(): { startTime: number; endTime: number } {
        const now = new Date();

        // 开始时间是7天前的00:00:00
        const startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        startDate.setHours(0, 0, 0, 0);
        const startTime = startDate.getTime();

        // 结束时间是昨天的23:59:59
        const endDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        endDate.setHours(23, 59, 59, 999);
        const endTime = endDate.getTime();

        return { startTime, endTime };
    }

    /**
     * 获取排序显示文本
     * @param sortType 排序类型
     * @returns 排序类型对应的显示文本
     */
    static getSortTypeText(sortType: number): string {
        const sortTypeMap = {
            0: ULanguage.getInstance().getLangByID("club.serviceFee"),
            1: ULanguage.getInstance().getLangByID("club.profitLoss"),
            2: ULanguage.getInstance().getLangByID("club.handCount"),
            3: ULanguage.getInstance().getLangByID("club.lastLogin"),
            4: ULanguage.getInstance().getLangByID("club.lastPlay"),
        };
        return sortTypeMap[sortType] || "";
    }

    // ======================== 成员列表排序相关方法 ========================

    /**
     * 排序成员列表
     * @param memberList 成员列表
     * @param isSortRole 是否按角色排序
     * @param sortType 排序类型
     * @param sortDirection 排序方向
     * @returns 排序后的成员列表
     */
    static sortMemberList(
        memberList: IClubMemberFullInfo[],
        isSortRole: boolean,
        sortField: SortTypeEnum,
        sortDirection: number
    ): IClubMemberFullInfo[] {
        console.log(TAG, "排序参数:", isSortRole, sortField, sortDirection);
        if (!memberList?.length) return [];

        if (!isSortRole) {
            return this.sortMember([...memberList], sortField, sortDirection);
        }

        // 使用 Map 存储不同角色的成员列表
        const roleGroups = new Map<okpb.Identity, IClubMemberFullInfo[]>();
        memberList.forEach((member) => {
            const group = roleGroups.get(member.identity) || [];
            group.push(member);
            roleGroups.set(member.identity, group);
        });

        // 按角色优先级排序
        const roleOrder = [
            okpb.Identity.IDE_MASTER,
            okpb.Identity.IDE_MANAGER,
            okpb.Identity.IDE_AGENT,
            okpb.Identity.IDE_TABLE_OPERATOR,
            okpb.Identity.IDE_GENERAL,
        ];

        return roleOrder.reduce((result: IClubMemberFullInfo[], role) => {
            const group = roleGroups.get(role);
            if (group?.length) {
                result.push(...this.sortMember(group, sortField, sortDirection));
            }
            return result;
        }, []);
    }

    /**
     * 排序成员列表
     * @param sourceList 源成员列表
     * @param sortType 排序类型
     * @param sortDirection 排序方向: 1: 升序, 2: 降序: 来自UISelectBoxOptions.SORT_TYPE
     * @returns 排序后的成员列表
     */
    public static sortMember(sourceList: IClubMemberFullInfo[], sortType: SortTypeEnum, sortDirection: number): IClubMemberFullInfo[] {
        if (!sourceList?.length) return [];
        // 定义排序键映射
        const sortKeyMap = {
            [SortTypeEnum.ServiceFee]: "serviceFee",
            [SortTypeEnum.Profit]: "winLose",
            [SortTypeEnum.HandCardCount]: "handCount",
            [SortTypeEnum.LastLogin]: "lastLogin",
            [SortTypeEnum.LastPlay]: "lastPlay",
            [SortTypeEnum.ChipBalance]: "chipBalance",
            [SortTypeEnum.JoinClubTime]: "joinClubTime",
        };

        const sortKey = sortKeyMap[sortType];
        if (!sortKey) return sourceList;

        return [...sourceList].sort((a, b) => {
            const valueA = a[sortKey];
            const valueB = b[sortKey];
            return sortDirection === 1 ? valueA - valueB : valueB - valueA;
        });
    }

    /**
     * 排序代理列表
     * @description 一期没有超级代理
     * @param sourceList 源代理列表
     * @param isRoleType 是否按角色排序: 代理的模块只有超级代理和普通代理，优先显示超级代理
     * @param sortField 排序字段
     * @param sortDirection 排序方向: 1: 升序, 2: 降序: 来自UISelectBoxOptions.SORT_TYPE
     * @returns 排序后的代理列表
     */
    static sortAgentList(
        sourceList: IClubMemberFullInfo[],
        isRoleType: boolean,
        sortField: AgentSortTypeEnum,
        sortDirection: number
    ): IClubMemberFullInfo[] {
        // 只根据sortField和sortDirection排序,暂时没有超级代理
        return this.sortAgent(sourceList, sortField, sortDirection);
    }

    /**
     * 排序代理列表
     * @param sourceList 源代理列表
     * @param sortField 排序字段
     * @param sortDirection 排序方向: 1: 升序, 2: 降序: 来自UISelectBoxOptions.SORT_TYPE
     * @returns 排序后的代理列表
     */
    static sortAgent(sourceList: IClubMemberFullInfo[], sortField: AgentSortTypeEnum, sortDirection: number): IClubMemberFullInfo[] {
        if (!sourceList?.length) return [];
        // 后面的几个字段是真实的字段
        const sortKeyMap = {
            [AgentSortTypeEnum.ThisWeekProfitLoss]: "changeScoreThisWeek",
            [AgentSortTypeEnum.ThisWeekServiceFee]: "serviceFeeThisWeek",
            [AgentSortTypeEnum.LastWeekProfitLoss]: "changeScoreLastWeek",
            [AgentSortTypeEnum.LastWeekServiceFee]: "serviceFeeLastWeek",
            [AgentSortTypeEnum.TotalProfitLoss]: "changeScoreTotal",
            [AgentSortTypeEnum.TotalServiceFee]: "serviceFeeTotal",
        };
        const sortKey = sortKeyMap[sortField];
        if (!sortKey) return sourceList;

        return [...sourceList].sort((a, b) => {
            const valueA = a[sortKey];
            const valueB = b[sortKey];
            return sortDirection === 1 ? valueA - valueB : valueB - valueA;
        });
    }

    // ======================== 权限管理相关方法 ========================

    /**
     * 根据当前用户角色和目标用户角色，获取可查看的详情功能配置项
     * @param currentUserRole 当前用户角色
     * @param targetUserRole 目标用户角色
     * @returns 可查看的详情功能列表
     */
    static getViewableFeatures(currentUserRole: okpb.Identity, targetUserRole: okpb.Identity): DetailFeatureEnum[] {
        const features: DetailFeatureEnum[] = [];

        // 创建者权限
        if (currentUserRole === okpb.Identity.IDE_MASTER) {
            switch (targetUserRole) {
                case okpb.Identity.IDE_MASTER: // 查看创建者
                    features.push(DetailFeatureEnum.BaseInfo);
                    break;
                case okpb.Identity.IDE_MANAGER: // 查看管理员
                    features.push(
                        DetailFeatureEnum.BaseInfo,
                        DetailFeatureEnum.RoleSetting,
                        DetailFeatureEnum.PermissionSetting,
                        DetailFeatureEnum.DeleteMember
                    );
                    break;
                case okpb.Identity.IDE_TABLE_OPERATOR: // 查看开房员
                    features.push(DetailFeatureEnum.BaseInfo, DetailFeatureEnum.RoleSetting, DetailFeatureEnum.DeleteMember);
                    break;
                case okpb.Identity.IDE_AGENT: // 查看代理
                    features.push(
                        DetailFeatureEnum.BaseInfo,
                        DetailFeatureEnum.RoleSetting,
                        DetailFeatureEnum.PermissionSetting,
                        DetailFeatureEnum.AgentInfo,
                        DetailFeatureEnum.DeleteMember
                    );
                    break;
                case okpb.Identity.IDE_GENERAL: // 查看普通成员
                    features.push(
                        DetailFeatureEnum.BaseInfo,
                        DetailFeatureEnum.RoleSetting,
                        DetailFeatureEnum.AgentInfo,
                        DetailFeatureEnum.DeleteMember
                    );
                    break;
            }
        }
        // 管理员权限
        else if (currentUserRole === okpb.Identity.IDE_MANAGER) {
            switch (targetUserRole) {
                case okpb.Identity.IDE_MASTER: // 查看创建者
                    features.push(DetailFeatureEnum.BaseInfo);
                    break;
                case okpb.Identity.IDE_MANAGER: // 查看管理员
                    features.push(DetailFeatureEnum.BaseInfo);
                    break;
                case okpb.Identity.IDE_TABLE_OPERATOR: // 查看开房员
                    features.push(DetailFeatureEnum.BaseInfo);
                    break;
                case okpb.Identity.IDE_AGENT: // 查看代理
                    features.push(
                        DetailFeatureEnum.BaseInfo,
                        DetailFeatureEnum.RoleSetting,
                        DetailFeatureEnum.PermissionSetting,
                        DetailFeatureEnum.DeleteMember
                    );
                    break;
                case okpb.Identity.IDE_GENERAL: // 查看普通成员
                    features.push(
                        DetailFeatureEnum.BaseInfo,
                        DetailFeatureEnum.RoleSetting,
                        DetailFeatureEnum.AgentInfo,
                        DetailFeatureEnum.DeleteMember
                    );
                    break;
            }
        }
        // 代理权限
        else if (currentUserRole === okpb.Identity.IDE_AGENT) {
            switch (targetUserRole) {
                case okpb.Identity.IDE_AGENT: // 查看代理（同级）
                    features.push(DetailFeatureEnum.BaseInfo);
                    break;
                case okpb.Identity.IDE_GENERAL:
                    features.push(DetailFeatureEnum.BaseInfo, DetailFeatureEnum.AgentInfo);
                    break;
            }
        }
        // 开房员权限
        else if (currentUserRole === okpb.Identity.IDE_TABLE_OPERATOR) {
            // nothing to do. 开房员啥子权限都没有
        }
        // 普通成员权限
        else if (currentUserRole === okpb.Identity.IDE_GENERAL) {
            // nothing to do. 普通成员啥子权限都没有
        }

        return features;
    }

    /**
     * 判断当前用户是否可以查看目标用户的指定功能
     * @param currentUserRole 当前用户角色
     * @param targetUserRole 目标用户角色
     * @param feature 要检查的功能
     * @returns 是否有权限查看
     */
    static canViewFeature(currentUserRole: okpb.Identity, targetUserRole: okpb.Identity, feature: DetailFeatureEnum): boolean {
        const viewableFeatures = this.getViewableFeatures(currentUserRole, targetUserRole);
        return viewableFeatures.includes(feature);
    }

    /**
     * 获取角色名称（用于调试和日志）
     * @param role 角色枚举值
     * @returns 角色名称
     */
    static getRoleName(role: okpb.Identity): string {
        switch (role) {
            case okpb.Identity.IDE_MASTER:
                return "创建者";
            case okpb.Identity.IDE_MANAGER:
                return "管理员";
            case okpb.Identity.IDE_TABLE_OPERATOR:
                return "开房员";
            case okpb.Identity.IDE_AGENT:
                return "代理";
            case okpb.Identity.IDE_GENERAL:
                return "普通成员";
            default:
                return "未知角色";
        }
    }

    /**
     * 获取功能名称（用于调试和日志）
     * @param feature 功能枚举值
     * @returns 功能名称
     */
    static getFeatureName(feature: DetailFeatureEnum): string {
        switch (feature) {
            case DetailFeatureEnum.BaseInfo:
                return "基础信息";
            case DetailFeatureEnum.RoleSetting:
                return "角色设置";
            case DetailFeatureEnum.PermissionSetting:
                return "权限设置";
            case DetailFeatureEnum.AgentInfo:
                return "代理信息";
            case DetailFeatureEnum.DeleteMember:
                return "删除成员";
            default:
                return "未知功能";
        }
    }

    /**
     * 获取功能名称数组（用于调试和日志）
     * @param features 功能枚举值数组
     * @returns 功能名称数组
     */
    static getFeatureNames(features: DetailFeatureEnum[]): string[] {
        if (!features || features.length === 0) {
            return [];
        }
        return features.map((feature) => this.getFeatureName(feature));
    }

    /**
     * 获取身份设置名称（用于调试和日志）
     * @param identitySetting 身份设置枚举值
     * @returns 身份设置名称
     */
    static getIdentitySettingName(identitySetting: IdentitySettingEnum): string {
        switch (identitySetting) {
            case IdentitySettingEnum.AdminAndTableOperator:
                return "管理员和开桌员";
            case IdentitySettingEnum.SuperAgent:
                return "超级代理";
            case IdentitySettingEnum.Agent:
                return "代理";
            case IdentitySettingEnum.Member:
                return "普通成员";
            default:
                return "未知身份";
        }
    }

    /**
     * 获取身份设置名称数组（用于调试和日志）
     * @param identitySettings 身份设置枚举值数组
     * @returns 身份设置名称数组
     */
    static getIdentitySettingNames(identitySettings: IdentitySettingEnum[]): string[] {
        if (!identitySettings || identitySettings.length === 0) {
            return [];
        }
        return identitySettings.map((setting) => this.getIdentitySettingName(setting));
    }

    // ======================== 成员详情中，底部权限列表配置管理相关方法 ========================

    private static getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id);
    }

    /**
     * 权限配置工厂类
     */
    private static createPermissionConfigs(agentCreditImg: cc.SpriteFrame, peopleImg: cc.SpriteFrame, lockImg: cc.SpriteFrame) {
        return {
            // 代理授信
            agentCredit: {
                featureName: this.getLangByID("club.agentCredit"),
                icon: agentCreditImg,
                value: "",
                showDialogPath: "chipsHistory",
            },
            // 权限限制
            permissionLimit: {
                featureName: this.getLangByID("club.permissionLimit"),
                icon: lockImg,
                value: "",
                showDialogPath: "permissionLimit",
            },
            // 删除成员
            deleteMember: {
                featureName: this.getLangByID("club.deleteMember"),
                icon: null,
                value: "",
                showDialogPath: "deleteMember",
            },
            // 下线管理
            downlineManage: {
                featureName: this.getLangByID("club.downlineManagement"),
                icon: peopleImg,
                value: "",
                showDialogPath: "downlineManage",
            },
            // 代理数据
            agentData: {
                featureName: this.getLangByID("club.agentData"),
                icon: null,
                value: "",
                showDialogPath: "agentDataDialog",
            },
            // 所属代理
            ownAgent: {
                featureName: this.getLangByID("club.ownAgent"),
                icon: null,
                value: "",
                showDialogPath: "",
            },
        };
    }

    /**
     * 权限配置上下文映射表
     * !来自案子中的需求：
     * 1. 查看者为创建者，仅被查看对象为管理员或代理时才会展示权限限制模块,即配置的·permissionLimit·
     */
    private static readonly PERMISSION_CONTEXT_MAP = {
        // 创建者可以查看的角色对应功能列表:
        [okpb.Identity.IDE_MASTER]: {
            [okpb.Identity.IDE_MANAGER]: ["permissionLimit", "deleteMember"],
            [okpb.Identity.IDE_TABLE_OPERATOR]: ["deleteMember"],
            [okpb.Identity.IDE_AGENT]: ["permissionLimit", "agentCredit", "downlineManage", "agentData", "deleteMember"],
            [okpb.Identity.IDE_GENERAL]: ["ownAgent", "deleteMember"],
        },
        // 管理员可以查看的角色对应功能列表
        [okpb.Identity.IDE_MANAGER]: {
            [okpb.Identity.IDE_AGENT]: ["permissionLimit", "agentCredit", "downlineManage", "agentData", "deleteMember"],
            [okpb.Identity.IDE_GENERAL]: ["ownAgent", "deleteMember"],
        },
        // 代理可以查看的角色对应功能列表
        [okpb.Identity.IDE_AGENT]: {
            [okpb.Identity.IDE_GENERAL]: ["ownAgent"],
        },
    };

    /**
     * 获取权限列表配置
     * @param options 权限列表配置参数
     * @returns 权限配置列表
     */
    static getPermissionListConfig(options: IPermissionListConfigOptions): IPermissionConfig[] {
        const { identity, values, images } = options;
        const { myIdentity, targetUserIdentity } = identity;
        const { agentId, creditValue, downlineValue } = values;
        const { agentCreditImg, peopleImg, lockImg } = images;

        const configs = this.createPermissionConfigs(agentCreditImg, peopleImg, lockImg);
        const permissionList: IPermissionConfig[] = [];

        // 获取当前角色和目标角色的权限映射
        const rolePermissions = this.PERMISSION_CONTEXT_MAP[myIdentity];
        if (!rolePermissions) {
            return permissionList;
        }

        const targetPermissions = rolePermissions[targetUserIdentity];
        if (!targetPermissions) {
            return permissionList;
        }

        // 根据权限配置添加对应的配置项
        targetPermissions.forEach((permissionKey) => {
            const config = configs[permissionKey];
            if (config) {
                //  上线代理id
                if (permissionKey === "ownAgent") {
                    if (agentId) {
                        config.value = agentId.toString();
                        permissionList.push(config);
                    }
                } else if (permissionKey === "agentCredit") {
                    // 授信值
                    if (creditValue) {
                        config.value = creditValue.toString();
                        permissionList.push(config);
                    }
                } else if (permissionKey === "downlineManage") {
                    // 下线数量
                    if (downlineValue) {
                        config.value = downlineValue.toString();
                        permissionList.push(config);
                    }
                } else {
                    permissionList.push(config);
                }
            }
        });

        console.log("[MemberHelper] getPermissionListConfig() 生成的权限配置:", permissionList);
        return permissionList;
    }

    /**
     * 获取身份设置配置
     * @param myIdentity 当前用户角色
     * @param targetUserIdentity 目标用户角色
     * @returns 身份设置配置
     */
    static getIdentitySettingConfig(myIdentity: okpb.Identity, targetUserIdentity: okpb.Identity): IdentitySettingEnum[] {
        if (myIdentity === okpb.Identity.IDE_MASTER) {
            switch (targetUserIdentity) {
                case okpb.Identity.IDE_MANAGER:
                case okpb.Identity.IDE_TABLE_OPERATOR:
                case okpb.Identity.IDE_AGENT:
                case okpb.Identity.IDE_GENERAL:
                    return [
                        IdentitySettingEnum.AdminAndTableOperator,
                        IdentitySettingEnum.SuperAgent,
                        IdentitySettingEnum.Agent,
                        IdentitySettingEnum.Member,
                    ];
                default:
                    return [];
            }
        } else if (myIdentity === okpb.Identity.IDE_MANAGER) {
            switch (targetUserIdentity) {
                case okpb.Identity.IDE_AGENT:
                case okpb.Identity.IDE_GENERAL:
                    return [IdentitySettingEnum.Agent, IdentitySettingEnum.Member];
                default:
                    return [];
            }
        }
        return [];
    }
}
