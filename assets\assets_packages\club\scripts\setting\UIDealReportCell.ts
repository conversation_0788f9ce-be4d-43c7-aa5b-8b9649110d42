import UBaseView from "../../../../framwork/widget/UBaseView";
import UIDealReportCell_auto from "./UIDealReportCell_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIDealReportCell]";

/**
 * 俱乐部交易报告cell
 *
 * @export
 * @class UIDealReportCell
 * @extends {UBaseView}
 */
@ccclass
@menu("okgame/UIDealReportCell")
export default class UIDealReportCell extends UBaseView {
    //#region 属性
    protected ui: UIDealReportCell_auto = null;
    private cellData: any = null;

    //#endregion

    //#region 生命周期
    onUILoad() {
        this.ui = new UIDealReportCell_auto(this.node);
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIDealReportCell
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }

    /**
     * 更新UI显示
     *
     * @private
     * @memberof UIDealReportCell
     */
    private updateUI() {
        if (!this.cellData) {
            return;
        }

        // 设置标题
        const titleLabel = this.ui.cellTitle.getComponent(cc.Label);
        if (titleLabel && this.cellData.title) {
            titleLabel.string = this.cellData.title;
        }

        // 设置提示
        const tipLabel = this.ui.cellTip.getComponent(cc.Label);
        if (tipLabel && this.cellData.tip) {
            tipLabel.string = this.cellData.tip;
        }

        // 设置筹码值
        const shipValueLabel = this.ui.shipValue.getComponent(cc.Label);
        if (shipValueLabel && this.cellData.shipValue !== undefined) {
            shipValueLabel.string = this.cellData.shipValue.toString();
        }

        // 设置时间
        const timeLabel = this.ui.cellTime.getComponent(cc.Label);
        if (timeLabel && this.cellData.time) {
            timeLabel.string = this.cellData.time;
        }
    }

    //#endregion

    //#region 事件处理
    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    /**
     * 设置cell数据
     *
     * @param {any} data
     * @memberof UIDealReportCell
     */
    setCellData(data: any) {
        this.cellData = data;
        this.log(`设置cell数据: ${JSON.stringify(data)}`);
        this.updateUI();
    }

    /**
     * 设置标题
     *
     * @param {string} title
     * @memberof UIDealReportCell
     */
    setTitle(title: string) {
        const titleLabel = this.ui.cellTitle.getComponent(cc.Label);
        if (titleLabel) {
            titleLabel.string = title;
        }
    }

    /**
     * 设置提示
     *
     * @param {string} tip
     * @memberof UIDealReportCell
     */
    setTip(tip: string) {
        const tipLabel = this.ui.cellTip.getComponent(cc.Label);
        if (tipLabel) {
            tipLabel.string = tip;
        }
    }

    /**
     * 设置筹码值
     *
     * @param {number} value
     * @memberof UIDealReportCell
     */
    setShipValue(value: number) {
        const shipValueLabel = this.ui.shipValue.getComponent(cc.Label);
        if (shipValueLabel) {
            shipValueLabel.string = value.toString();
        }
    }

    /**
     * 设置时间
     *
     * @param {string} time
     * @memberof UIDealReportCell
     */
    setTime(time: string) {
        const timeLabel = this.ui.cellTime.getComponent(cc.Label);
        if (timeLabel) {
            timeLabel.string = time;
        }
    }

    //#endregion
}
