{"frameRate": 60, "isGlobal": 0, "name": "bingtong", "version": "4.5", "armature": [{"defaultActions": [{"gotoAndPlay": "Animation1"}], "skin": [{"name": "", "slot": [{"display": [{"path": "water_09", "type": "image", "name": "water_09", "transform": {}}], "name": "Layer10"}, {"display": [{"path": "water_10", "type": "image", "name": "water_10", "transform": {}}], "name": "Layer12_Copy17"}, {"display": [{"path": "water_03", "type": "image", "name": "water_03", "transform": {}}], "name": "Layer6"}, {"display": [{"path": "water_19", "type": "image", "name": "water_19", "transform": {}}], "name": "Layer15"}, {"display": [{"path": "water_05", "type": "image", "name": "water_05", "transform": {}}], "name": "Layer8"}, {"display": [{"path": "water_11", "type": "image", "name": "water_11", "transform": {}}], "name": "Layer13"}, {"display": [{"path": "water_10", "type": "image", "name": "water_10", "transform": {}}], "name": "Layer12"}, {"display": [{"path": "water_22", "type": "image", "name": "water_22", "transform": {}}], "name": "Layer16"}, {"display": [{"path": "water_14", "type": "image", "name": "water_14", "transform": {}}], "name": "Layer14"}, {"display": [{"path": "water_04", "type": "image", "name": "water_04", "transform": {}}], "name": "Layer7"}, {"display": [{"path": "water_07", "type": "image", "name": "water_07", "transform": {}}], "name": "Layer9"}]}], "animation": [{"frame": [], "duration": 100, "bone": [{"frame": [{"tweenEasing": 0, "duration": 25, "transform": {"skY": -8, "y": 50.5882, "skX": -8, "x": 158.2353}}, {"tweenEasing": 0, "duration": 12, "transform": {"skY": -11, "y": 35.8823, "skX": -11, "x": 150}}, {"tweenEasing": 0, "duration": 1, "transform": {"skY": 45, "y": 50, "skX": 45, "x": 195.2937}}, {"duration": 62, "transform": {"skY": -11, "y": 35.8823, "skX": -11, "x": 150}}], "name": "Layer10"}, {"frame": [{"tweenEasing": 0, "duration": 27, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 1, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -67.647, "x": -52.3529}}, {"tweenEasing": 0, "duration": 1, "transform": {"skY": 9, "y": -55.2941, "skX": 9, "x": -38.2353}}, {"duration": 66, "transform": {"y": -67.647, "x": -52.3529}}], "name": "Layer13"}, {"frame": [{"tweenEasing": 0, "duration": 29, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 1, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 8, "transform": {"y": -61.7647, "x": -18.8235}}, {"tweenEasing": 0, "duration": 1, "transform": {"skY": -6, "y": -32.9412, "skX": -6, "x": -14.7059}}, {"duration": 61, "transform": {"y": -55.2941, "x": 1.1765}}], "name": "Layer9"}, {"frame": [{"tweenEasing": 0, "duration": 34, "transform": {"y": -28.2353, "x": 19.4118}}, {"tweenEasing": 0, "duration": 2, "transform": {"y": -28.2353, "x": 19.4118}}, {"tweenEasing": 0, "duration": 9, "transform": {"y": -22.9412, "x": -2.3529}}, {"tweenEasing": 0, "duration": 1, "transform": {"y": -10.5882, "x": 8.2353}}, {"duration": 54, "transform": {"y": -28.2353, "x": 19.4118}}], "name": "Layer15"}, {"frame": [{"tweenEasing": 0, "duration": 42, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 3, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 2, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 7, "transform": {"y": -44.1176, "x": -23.5294}}, {"duration": 46, "transform": {"y": -44.1176, "scX": 1.3, "scY": 1.3, "x": -23.5294}}], "name": "Layer14"}, {"frame": [{"tweenEasing": 0, "duration": 26, "transform": {"y": 5.2941, "x": 155.8824}}, {"tweenEasing": 0, "duration": 15, "transform": {"skY": -7, "y": -10.5882, "skX": -7, "x": 150}}, {"tweenEasing": 0, "duration": 59, "transform": {"skY": 84, "y": -13.5294, "skX": 84, "x": 159.4117}}, {"duration": 0, "transform": {"skY": 84, "y": -13.5294, "skX": 84, "x": 159.4117}}], "name": "Layer6"}, {"frame": [{"tweenEasing": 0, "duration": 37, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 6, "transform": {"y": -55.2941, "scX": 0.9, "scY": 0.8, "x": 1.1765}}, {"tweenEasing": 0, "duration": 52, "transform": {"y": -55.2941, "x": 1.1765}}, {"duration": 0, "transform": {"y": -55.2941, "x": 1.1765}}], "name": "Layer16"}, {"frame": [{"tweenEasing": 0, "duration": 36, "transform": {"y": -55.8823, "scX": 0.7157, "scY": 0.6395, "x": -105.294}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -55.8823, "scX": 0.7157, "scY": 0.6395, "x": -105.294}}, {"tweenEasing": 0, "duration": 4, "transform": {"y": -61.1765, "scX": 0.7157, "scY": 0.6395, "x": -83.5293}}, {"tweenEasing": 0, "duration": 13, "transform": {"y": -65.8823, "scX": 0.7157, "scY": 0.6395, "x": -95.8822}}, {"tweenEasing": 0, "duration": 2, "transform": {"y": -56.4705, "scX": 0.7157, "scY": 0.6395, "x": -96.4705}}, {"tweenEasing": 0, "duration": 2, "transform": {"skY": 23, "y": -55.2941, "scX": 0.7157, "scY": 0.6395, "skX": 23, "x": -92.4706}}, {"duration": 38, "transform": {"skY": 83, "y": -61.1764, "scX": 0.7157, "scY": 0.6395, "skX": 83, "x": -77.7647}}], "name": "Layer12_Copy17"}, {"frame": [{"tweenEasing": 0, "duration": 36, "transform": {"y": -50, "x": -20.5882}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -50, "x": -20.5882}}, {"tweenEasing": 0, "duration": 4, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 16, "transform": {"y": -86.4706, "x": -17.0588}}, {"tweenEasing": 0, "duration": 1, "transform": {"y": -37.6471, "x": -18.8235}}, {"tweenEasing": 0, "duration": 1, "transform": {"y": -37.6471, "x": -18.8235}}, {"duration": 37, "transform": {"y": -37.6471, "x": -18.8235}}], "name": "Layer12"}, {"frame": [{"tweenEasing": 0, "duration": 36, "transform": {"y": -50, "x": -20.5882}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -50, "x": -20.5882}}, {"tweenEasing": 0, "duration": 15, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 12, "transform": {"y": -76.4706, "x": 17.0588}}, {"tweenEasing": 0, "duration": 4, "transform": {"y": -61.7647, "x": 9.4118}}, {"tweenEasing": 0, "duration": 4, "transform": {"skY": 45, "y": -67.647, "skX": 45, "x": 14.7059}}, {"duration": 24, "transform": {"skY": -3, "y": -48.2353, "skX": -3, "x": -5.8824}}], "name": "Layer11"}, {"frame": [{"tweenEasing": 0, "duration": 36, "transform": {"y": -50, "x": -20.5882}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -50, "x": -20.5882}}, {"tweenEasing": 0, "duration": 4, "transform": {"y": -55.2941, "x": 1.1765}}, {"tweenEasing": 0, "duration": 7, "transform": {"y": -77.0588, "x": 15.8824}}, {"duration": 48, "transform": {"y": -48.8235, "x": 16.4706}}], "name": "Layer8"}, {"frame": [{"tweenEasing": 0, "duration": 43, "transform": {"y": -64.7059, "x": -15.8823}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -64.7059, "x": -15.8823}}, {"tweenEasing": 0, "duration": 5, "transform": {"y": -70, "x": 5.8824}}, {"tweenEasing": 0, "duration": 15, "transform": {"y": -70, "x": 5.8824}}, {"duration": 32, "transform": {"y": -70, "x": 5.8824}}], "name": "Layer7"}, {"frame": [{"duration": 100, "transform": {}}], "name": "root"}], "slot": [{"frame": [{"tweenEasing": 0, "duration": 25}, {"tweenEasing": 0, "duration": 12}, {"tweenEasing": 0, "duration": 1, "color": {"aM": 39}}, {"tweenEasing": null, "displayIndex": -1, "duration": 62}], "name": "Layer10"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 27}, {"tweenEasing": 0, "displayIndex": -1, "duration": 1}, {"tweenEasing": 0, "duration": 5}, {"tweenEasing": 0, "duration": 1}, {"tweenEasing": null, "displayIndex": -1, "duration": 66}], "name": "Layer13"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 29}, {"tweenEasing": 0, "displayIndex": -1, "duration": 1}, {"tweenEasing": 0, "duration": 8}, {"tweenEasing": 0, "duration": 1}, {"tweenEasing": null, "displayIndex": -1, "duration": 61}], "name": "Layer9"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 34}, {"tweenEasing": 0, "displayIndex": -1, "duration": 2}, {"tweenEasing": 0, "duration": 9}, {"tweenEasing": 0, "duration": 1}, {"tweenEasing": null, "displayIndex": -1, "duration": 54}], "name": "Layer15"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 42}, {"tweenEasing": 0, "displayIndex": -1, "duration": 3}, {"tweenEasing": 0, "displayIndex": -1, "duration": 2}, {"tweenEasing": 0, "duration": 7}, {"tweenEasing": null, "duration": 46}], "name": "Layer14"}, {"frame": [{"tweenEasing": 0, "duration": 26}, {"tweenEasing": 0, "duration": 15}, {"tweenEasing": 0, "duration": 59}, {"tweenEasing": null, "duration": 0, "color": {"aM": 0}}], "name": "Layer6"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 37}, {"tweenEasing": 0, "displayIndex": -1, "duration": 5}, {"tweenEasing": 0, "duration": 6}, {"tweenEasing": 0, "duration": 52}, {"tweenEasing": null, "duration": 0}], "name": "Layer16"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 36}, {"tweenEasing": 0, "displayIndex": -1, "duration": 5}, {"tweenEasing": 0, "displayIndex": -1, "duration": 4}, {"tweenEasing": 0, "duration": 13}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": 0, "duration": 2}, {"tweenEasing": null, "duration": 38}], "name": "Layer12_Copy17"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 36}, {"tweenEasing": 0, "displayIndex": -1, "duration": 5}, {"tweenEasing": 0, "displayIndex": -1, "duration": 4}, {"tweenEasing": 0, "duration": 16}, {"tweenEasing": 0, "duration": 1}, {"tweenEasing": 0, "duration": 1}, {"tweenEasing": null, "duration": 37}], "name": "Layer12"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 36}, {"tweenEasing": 0, "displayIndex": -1, "duration": 5}, {"tweenEasing": 0, "displayIndex": -1, "duration": 4}, {"tweenEasing": 0, "duration": 7}, {"tweenEasing": null, "duration": 48}], "name": "Layer8"}, {"frame": [{"tweenEasing": 0, "displayIndex": -1, "duration": 43}, {"tweenEasing": 0, "displayIndex": -1, "duration": 5}, {"tweenEasing": 0, "displayIndex": -1, "duration": 5}, {"tweenEasing": 0, "duration": 15}, {"tweenEasing": null, "duration": 32}], "name": "Layer7"}], "ffd": [], "ik": [], "name": "Animation1"}], "type": "Armature", "frameRate": 60, "bone": [{"name": "root", "transform": {}}, {"inheritScale": false, "transform": {"y": -46.6734, "x": -260}, "name": "Layer6", "parent": "root"}, {"inheritScale": false, "transform": {"y": 112.3266, "x": -12.8334}, "name": "Layer8", "parent": "root"}, {"inheritScale": false, "transform": {"y": 129.9932, "scX": 1.2333, "x": -100.5}, "name": "Layer11", "parent": "root"}, {"inheritScale": false, "transform": {"y": 112.4932, "x": 54.1666}, "name": "Layer12", "parent": "root"}, {"inheritScale": false, "transform": {"y": 112.4932, "x": 54.1666}, "name": "Layer12_Copy17", "parent": "root"}, {"inheritScale": false, "transform": {"y": 56.1599, "x": -0.6667}, "name": "Layer16", "parent": "root"}, {"inheritScale": false, "transform": {"y": 136.6599, "x": 3.3333}, "name": "Layer7", "parent": "root"}, {"inheritScale": false, "transform": {"y": 119.6599, "x": 42.6666}, "name": "Layer14", "parent": "root"}, {"inheritScale": false, "transform": {"y": 10.3266, "x": -4.6667}, "name": "Layer15", "parent": "root"}, {"inheritScale": false, "transform": {"y": 5.4345, "x": -7.9901}, "name": "Layer9", "parent": "root"}, {"inheritScale": false, "transform": {"y": -13.2028, "x": -19.5685}, "name": "Layer13", "parent": "root"}, {"inheritScale": false, "transform": {"y": -132.0067, "x": -266.3332}, "name": "Layer10", "parent": "root"}], "aabb": {"width": 405.6665, "y": -145.5067, "height": 297.6666, "x": -308.3332}, "slot": [{"name": "Layer7", "parent": "Layer7", "color": {}}, {"z": 1, "name": "Layer8", "parent": "Layer8", "color": {}}, {"z": 2, "name": "Layer12", "parent": "Layer12", "color": {}}, {"z": 3, "name": "Layer12_Copy17", "parent": "Layer12_Copy17", "color": {}}, {"z": 4, "name": "Layer16", "parent": "Layer16", "color": {}}, {"z": 5, "name": "Layer6", "parent": "Layer6", "color": {}}, {"z": 6, "name": "Layer14", "parent": "Layer14", "color": {}}, {"z": 7, "name": "Layer15", "parent": "Layer15", "color": {}}, {"z": 8, "name": "Layer9", "parent": "Layer9", "color": {}}, {"z": 9, "name": "Layer13", "parent": "Layer13", "color": {}}, {"z": 10, "name": "Layer10", "parent": "Layer10", "color": {}}], "ik": [], "name": "armatureName"}]}