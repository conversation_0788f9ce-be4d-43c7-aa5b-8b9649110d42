import { ULanguageLabel } from "../../../../framwork/language/ULanguageLabel";
import UBaseScene from "../../../../framwork/widget/UBaseScene";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UTextureArray from "../../../../framwork/widget/UTextureArray";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import ClubManger, { CLUB_EVENT, CLUB_UIID } from "../ClubManger";
import ClubMessageManager from "../members/apply/ClubMessageManager";
import { PermissionType, PermissionUtils } from "../members/PermissionUtils";
import UIClubInfoView from "./UIClubInfoView";
import UIClubScene_auto from "./UIClubScene_auto";
import UIClubTableView from "./UIClubTableView";
const { ccclass, property } = cc._decorator;

@ccclass
export default class UIClubScene extends UBaseScene {
    protected ui: UIClubScene_auto = null;
    private clubInfo: okpb.ClubResp = null;
    private isMenuExpanded: boolean = false;
    private ClubTableView: UIClubTableView = null;
    private ClubInfoView: UIClubInfoView = null;

    onUILoad(): void {
        this.ui = new UIClubScene_auto(this.node);
        this.registerEvent(CLUB_EVENT.UPDATE_CLUB_INFO, this.updateClubInfo.bind(this));
        this.onRegisterEvent(this.ui.closeBtn, this.onCloseBtnClick.bind(this));
        this.onRegisterEvent(this.ui.clubTableBtn, this.onClubTableBtnClick.bind(this));
        this.onRegisterEvent(this.ui.btnMenu, this.toggleMenu.bind(this));
        this.onRegisterEvent(this.ui.btnMessage, this.onMessageBtnClick.bind(this));
        this.onRegisterEvent(this.ui.btnMember, this.onMemberBtnClick.bind(this));
        this.onRegisterEvent(this.ui.btnCashier, this.onCashierBtnClick.bind(this));
        this.onRegisterEvent(this.ui.btnData, this.onDataBtnClick.bind(this));
        this.onRegisterEvent(this.ui.btnAdmin, this.onAdminBtnClick.bind(this));

        this.ui.panelTitle.getComponent(ULanguageLabel).setDataId("club.club");
        this.ClubTableView = this.ui.ClubTableView.getComponent(UPrefabContainer).getNodeComponent(UIClubTableView);
        this.ClubInfoView = this.ui.clubInfoNode.getComponent(UPrefabContainer).getNodeComponent(UIClubInfoView);

        this.initLanguage();

        ClubMessageManager.getInstance().subscribe((count, list) => {
            if (cc.isValid(this.node)) {
                this.ui.redPotImg.active = count > 0;
            }
        });
    }

    /**
     * 权限决定用户可见的功能按钮
     */
    setBtnAccess() {
        this.ui.btnMember.active = ClubManger.getInstance().hasMemberAccess(this.clubInfo);
        this.ui.btnCashier.active = ClubManger.getInstance().hasCashierAccess(this.clubInfo);
        this.ui.btnAdmin.active = ClubManger.getInstance().hasSetingAccess(this.clubInfo);
        // 数据按钮，除了依赖于本身的配置外，创建者可以在成员详情中配置我是否可见数据按钮
        this.ui.btnData.active =
            ClubManger.getInstance().hasDataAccess(this.clubInfo) && PermissionUtils.hasReadDataPermission(this.clubInfo.permissions);
    }

    private initLanguage() {
        this.ui.btnMessageText.getComponent(ULanguageLabel).setDataId("common.Message");
        this.ui.btnMemberText.getComponent(ULanguageLabel).setDataId("club.Member");
        this.ui.btnCashierText.getComponent(ULanguageLabel).setDataId("club.Cashier");
        this.ui.btnDataText.getComponent(ULanguageLabel).setDataId("common.Data");
        this.ui.btnAdminText.getComponent(ULanguageLabel).setDataId("common.Setting");
    }

    onShow(data: okpb.ClubResp, isShowEditProfile: boolean) {
        this.clubInfo = data;
        // 缓存俱乐部信息
        ClubDataManager.getInstance().cacheClubInfo(data);
        // !重要：设置当前的俱乐部id，会减少很多不必要的传参
        ClubDataManager.getInstance().setClubId(data.clubId);
        // 请求俱乐部柜台筹码信息
        ClubDataManager.getInstance().requestClubCounterInfo();
        // 请求俱乐部个人信息
        ClubDataManager.getInstance().requestClubUserSelfInfo();
        // 请求成员列表+代理成员数据+备注
        ClubDataManager.getInstance().requestMembersAndAgetnAndMark(data.clubId);
        // 请求消息列表
        ClubMessageManager.getInstance().fetchMessageList(data.clubId, 1, true);

        this.updateClubInfo(data);
        this.toggleMenu();
        if (isShowEditProfile) ClubManger.getInstance().showClubEditProfileView(this.clubInfo);
    }

    private async updateClubInfo(data: okpb.ClubResp) {
        if (!data) {
            ClubAPI.getInstance()
                .ReqSearchClub(this.clubInfo.clubId, 0)
                .then((rsp: okpb.RspSearchClub) => {
                    if (rsp && rsp.errorCode == okpb.ERET.OK) {
                        this.clubInfo = rsp.data;
                        if (cc.isValid(this.node)) this.updateClubUI();
                    } else if (rsp && rsp.errorCode == okpb.ERET.CLUB_NOT_FIND) {
                        //该俱乐部不存在
                        OKGameManager.getInstance().showHallScene();
                    } else {
                        console.error("updateClubInfo error", rsp.errorCode);
                    }
                });
        } else {
            this.clubInfo = data;
            this.updateClubUI();
        }
    }

    private updateClubUI() {
        if (this.ClubTableView) this.ClubTableView.updateInfo(this.clubInfo);
        if (this.ClubInfoView) this.ClubInfoView.updateInfo(this.clubInfo);
        this.setBtnAccess();
    }

    private toggleMenu() {
        this.isMenuExpanded = !this.isMenuExpanded;
        this.ui.btnMenuIcon.getComponent(UTextureArray).setTexture(this.isMenuExpanded ? 1 : 0);
        let nodes = [this.ui.btnMessage, this.ui.btnMember, this.ui.btnCashier, this.ui.btnData, this.ui.btnAdmin];
        nodes = nodes.filter((node) => node.active);
        let btnMenuX = this.ui.btnMenu.x;
        let spacing = 90;
        let minMenuBtnBgWidth = 160;
        let nodeWidth = nodes[0].width;
        let menuBtnBgWidth = this.isMenuExpanded ? minMenuBtnBgWidth + nodes.length * (spacing + nodeWidth) : minMenuBtnBgWidth;
        cc.tween(this.ui.menuBg).to(0.2, { width: menuBtnBgWidth }, { easing: "backOut" }).start();
        for (let index = 0; index < nodes.length; index++) {
            let node = nodes[index];
            let moveX = this.isMenuExpanded ? btnMenuX / 2 - (nodeWidth + spacing) * (nodes.length - index) : btnMenuX;
            let opacity = this.isMenuExpanded ? 255 : 0;
            cc.tween(node).to(0.3, { x: moveX, opacity: opacity }, { easing: "backOut" }).start();
        }
    }

    private onMessageBtnClick() {
        console.log("消息");
    }
    private onMemberBtnClick() {
        console.log("点击成员按钮，显示成员主页面");
        // FIXME:  不确定的需求：？？？？？？？需求： 成员列表功能：创建者关闭管理者成员列表功能权限 ，则管理者点击成员入口图标时，提示：无此权限
        // 这里不需要做点击判断，会在按钮那里设置是否可见
        // if (PermissionUtils.hasPermission(this.clubInfo.permissions, PermissionType.USER_LIST)) {
        ClubManger.getInstance().showClubMemberView();
        // } else {
        //     OKGameManager.getInstance().showToastById("club.noPermission");
        // }
    }
    private onCashierBtnClick() {
        const hasCounterPermission = PermissionUtils.hasCounterPermission(this.clubInfo.permissions);
        console.log("点击钱柜，当前用户是否有钱柜权限:", hasCounterPermission + ",权限位掩码:", this.clubInfo.permissions);
        if (hasCounterPermission) {
            ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubCashierView, this.clubInfo);
        } else {
            OKGameManager.getInstance().showToastById("club.noPermission");
        }
    }
    private onDataBtnClick() {
        const hasReadDataPermission = PermissionUtils.hasReadDataPermission(this.clubInfo.permissions);
        console.log("点击数据，当前用户是否有权限查看数据:", hasReadDataPermission + ",权限位掩码:", this.clubInfo.permissions);
        if (hasReadDataPermission) {
            ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubDetailDataView, this.clubInfo);
        } else {
            OKGameManager.getInstance().showToastById("club.noPermission");
        }
    }
    private onAdminBtnClick() {
        ClubManger.getInstance().showClubDialog(CLUB_UIID.ClubSettingView);
    }

    private onClubTableBtnClick() {
        OKGameManager.getInstance().showCreateRoomGameList(this.clubInfo.clubId);
    }
    private onCloseBtnClick() {
        OKGameManager.getInstance().showHallScene();
    }

    public updateDateTimer(data: { startTime: number; endTime: number; isAutoReq: boolean }) {
        console.log("updateDateTimer", data);
    }

    public getClubInfo() {
        return this.clubInfo;
    }
}
