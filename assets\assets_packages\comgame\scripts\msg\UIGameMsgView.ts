import UBaseDialog from "../../../../framwork/widget/UBaseDialog"
import UScrollView from "../../../../framwork/widget/UScrollView/USAdaptiveHeight"

import UIMsgDetailRoomDefaultCell from "../../../okgame/scripts/message/view/UIMsgDetailRoomDefaultCell"
import UIMsgDetailRoomHandleCell from "../../../okgame/scripts/message/view/UIMsgDetailRoomHandleCell"

import { okpb } from "../../../okgame/proto/proto_msg"
import ComGameAPI from "../../network/ComGameAPI"
import { OKGameMsgType } from "../../../okgame/network/OKGameMsgType"
import OKGameManager from "../../../okgame/public/OKGameManager"
const { ccclass, menu, property } = cc._decorator
const logTag = "[UIGameMsgView]"

/**
 * 游戏消息界面
 *
 * @export
 * @class UIGameMsgView
 * @extends {UBaseDialog}
 */
@ccclass
@menu("okgame/UIGameMsgView")
export default class UIGameMsgView extends UBaseDialog {
  //#region 属性
  @property({ type: cc.Node, tooltip: "消息提示节点" })
  private msgTip: cc.Node = null

  @property({ type: cc.Node, tooltip: "空数据节点" })
  private emptyNode: cc.Node = null

  @property({ type: UScrollView, tooltip: "数据列表" })
  private scrollView: UScrollView = null

  @property({ type: cc.Prefab, tooltip: "房间默认Cell" })
  private defaultRoomCell: cc.Prefab = null

  @property({ type: cc.Prefab, tooltip: "房间操作Cell" })
  private handleRoomCell: cc.Prefab = null

  /**
   * 列表数据数组
   *
   * @private
   * @type {okpb.MessageInfo[]}
   * @memberof UIGameMsgView
   */
  private listData: okpb.MessageInfo[] = []

  /**
   * 是否正在加载数据中
   *
   * @private
   * @type {boolean}
   * @memberof UIGameMsgView
   */
  private isLoading: boolean

  //#endregion

  //#region 生命周期
  onUILoad() {
    this.initBaseUI()
    this.scrollView.onUpdateCell = this.onUpdateCell.bind(this)

    this.registerProto(OKGameMsgType.clubMsg, this.onUnderMsgNotify.bind(this))
  }

  start() {
    this.loadDetailListData()
  }

  //#endregion

  //#region 函数方法
  /**
   * 日志打印
   *
   * @private
   * @param {string} msg
   * @memberof UIGameMsgView
   */
  private log(msg: string) {
    console.log(`${logTag}${msg}`)
  }

  /**
   * 初始化基础UI控件信息
   *
   * @private
   * @memberof UIMessageCenter
   */
  private initBaseUI() {
    this.emptyNode.active = true
    this.scrollView.node.active = false
    this.isLoading = false
    this.listData = []
    this.scrollView.clearData(true)
  }

  /**
   * 更新Cell数据信息
   *
   * @private
   * @param {number} i
   * @param {cc.Node} node
   * @return {*}  {cc.Node}
   * @memberof UIMessageDetail
   */
  private onUpdateCell(i: number, node: cc.Node): cc.Node {
    if (!this.listData?.length || i < 0 || i >= this.listData.length) {
      return null
    }

    const doorItem = this.listData[i]
    const isDefault = doorItem.msgType === 0

    let cellPrefab: cc.Prefab
    let cellComponent: new (node: cc.Node) => cc.Component

    if (isDefault) {
      cellPrefab = this.defaultRoomCell
      cellComponent = UIMsgDetailRoomDefaultCell
    } else {
      cellPrefab = this.handleRoomCell
      cellComponent = UIMsgDetailRoomHandleCell
    }

    if (!cellPrefab || !cellComponent) {
      return null
    }

    if (!node) {
      node = cc.instantiate(cellPrefab)
    }

    const cell = node.getComponent(cellComponent)
    if (cell && cell?.setDetailCell) {
      cell?.setDetailCell(doorItem)
    }

    return node
  }

  /**
   * 加载消息列表数据
   *
   * @private
   * @memberof UIMessageDetail
   */
  private async loadDetailListData() {
    this.isLoading = true
    OKGameManager.getInstance().showLoading()
    const res = await ComGameAPI.getInstance().ReqGetUserUnDealMsg()
    OKGameManager.getInstance().hideLoading()
    this.log(`获取消息列表：${JSON.stringify(res)}`)
    this.isLoading = false
    if (res.errorCode === 0) {
      const dataList = res.data.doorList
      if (Array.isArray(dataList)) {
        this.listData = this.listData.concat(dataList)
      }

      this.emptyNode.active = this.listData.length === 0
      this.scrollView.node.active = this.listData.length > 0
      this.scheduleOnce(() => {
        this.scrollView.loadData(this.listData.length)
      })
    }
  }

  /**
   * 未读消息通知处理
   *
   * @private
   * @memberof UIGameMsgView
   */
  private onUnderMsgNotify() {
    this.msgTip.active = true
  }

  //#endregion

  //#region 事件处理
  /**
   * 点击消息提示按钮事件处理
   *
   * @private
   * @memberof UIGameMsgView
   */
  private onClickMsgTip() {
    this.msgTip.active = false
    this.listData = []
    this.scrollView.clearData(true)
    this.loadDetailListData()
  }

  //#endregion

  //#region getter/setter
  //#endregion

  //#region 对外公开的方法
  //#endregion
}
