import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseView from "../../../../framwork/widget/UBaseView";
import USFixedHeight from "../../../../framwork/widget/UScrollView/USFixedHeight";
import { okpb } from "../../../okgame/proto/proto_msg";
import UIItemTable from "../../../okgame/room/scripts/list/UIItemTable";
import { GAME_PLAY_TYPE, GAME_TYPE_CONFIGS } from "../../../okgame/room/scripts/OKRoomConst";
import OKRoomManger from "../../../okgame/room/scripts/OKRoomManger";
import ClubAPI from "../ClubAPI";
import UIClubTableView_auto from "./UIClubTableView_auto";
const TAB_GAME_TYPE_CONFIGS: Array<{ playType: string; LangKey: string }> = [
    {
        playType: Array.from(GAME_TYPE_CONFIGS.keys()).join(","),
        <PERSON><PERSON>ey: "room.All",
    },
    {
        playType: GAME_PLAY_TYPE.TexasBase.toString(),
        LangKey: "room.baseGame",
    },
    {
        playType: GAME_PLAY_TYPE.TexasSixPlus.toString(),
        LangKey: "room.sixPlusGame",
    },
    {
        playType: GAME_PLAY_TYPE.TexasOmh.toString(),
        LangKey: "room.amhGame",
    },
];
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubTableView extends UBaseView {
    @property(cc.Prefab)
    item: cc.Prefab = null;
    protected ui: UIClubTableView_auto = null;
    private scrollView: USFixedHeight = null;
    private _playType: string = Array.from(GAME_TYPE_CONFIGS.keys()).join(",");
    private _minGrade: number = 0;
    private _maxGrade: number = 0;
    private _timeSort: number = 0;
    private _excludeFull: number = 0;
    private _includeStr: number = 1;
    private _pageNum: number = 0;
    private _roomList: okpb.RoomViewPO[] = [];
    private _itemNodes: cc.Node[] = [];
    private clubInfo: okpb.ClubResp = null;
    onUILoad(): void {
        this.ui = new UIClubTableView_auto(this.node);
        this.scrollView = this.ui.TableScroll.getComponent(USFixedHeight);
        this.scrollView.onUpdateCell = this.onUpdateCell.bind(this);
    }

    public updateInfo(clubInfo: okpb.ClubResp) {
        this.clubInfo = clubInfo;
        this.tabView();
        this.fetchRoomList();
    }

    private tabView() {
        this.ui.clubTabScrollContent.removeAllChildren();
        for (let index = 0; index < TAB_GAME_TYPE_CONFIGS.length; index++) {
            const info = TAB_GAME_TYPE_CONFIGS[index];
            console.log("tabView", info);
            let node = cc.instantiate(this.ui.TabItem);
            this.ui.clubTabScrollContent.addChild(node);
            let defText = cc.find(`${this.ui.TabItemDef.name}/${this.ui.TabItemDefText.name}`, node);
            let checkText = cc.find(`${this.ui.TabItemCheck.name}/${this.ui.TabItemCheckText.name}`, node);
            defText.getComponent(cc.Label).string = ULanguage.getInstance().getLangByID(info.LangKey);
            checkText.getComponent(cc.Label).string = ULanguage.getInstance().getLangByID(info.LangKey);
            this.onRegisterEvent(node, this.tabItemClick.bind(this, info));
        }
    }

    private tabItemClick(info: { playType: string; LangKey: string }) {
        this._playType = info.playType;
        this._minGrade = 0;
        this._maxGrade = 0;
        this._timeSort = 0;
        this._excludeFull = 0;
        this._includeStr = 1;
        this._pageNum = 0;
        this.fetchRoomList();
    }

    private async fetchRoomList() {
        let data: okpb.RspGetRoomLive = await ClubAPI.getInstance().ReqGetRoomLive(
            this.clubInfo.clubId,
            this._playType,
            this._minGrade,
            this._maxGrade,
            this._excludeFull,
            this._includeStr,
            this._timeSort,
            this._pageNum
        );
        if (!cc.isValid(this.node)) return;
        if (data && data.errorCode === 0) {
            this._roomList = data.data.roomViewList || [];
            this.updateListView();
        } else {
            this._pageNum = 0;
        }
    }

    private updateListView() {
        this.scrollView.clearData();
        this.scrollView.loadData(this._roomList.length);
        this.scrollView.scrollToTop();
        this.playEnterAnimation();
    }

    private onUpdateCell(i: number, node: cc.Node): cc.Node {
        let data = this._roomList[i];
        if (!data) return null;
        if (!node) {
            node = cc.instantiate(this.item);
            node.x = cc.winSize.width;
            let view = node.getComponent(UIItemTable);
            this.onRegisterEvent(node, () => {
                this.onItemClick(view.getData());
            });
            view.setData(data);
            this._itemNodes.push(node);
        } else {
            let view = node.getComponent(UIItemTable);
            view.setData(data);
        }
        return node;
    }

    private playEnterAnimation() {
        this._itemNodes.forEach((node, index) => {
            cc.tween(node)
                .delay(0.05 * index)
                .to(0.3, { x: 0 }, { easing: "backOut" })
                .start();
        });
    }

    private onItemClick(data: okpb.RoomPO | okpb.RoomViewPO) {
        OKRoomManger.getInstance().enterRoomByRoomId(data.roomId);
    }
}