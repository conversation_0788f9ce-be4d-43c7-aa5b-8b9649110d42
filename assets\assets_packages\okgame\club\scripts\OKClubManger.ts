import UEventHandle from "../../../../framwork/utils/UEventHandle";
/**
 * 俱乐部管理类
 * @description 负责俱乐部相关功能管理，采用单例模式
 */
export default class OKClubManger extends UEventHandle {
    protected static _instance: OKClubManger;
    /** 正则表达式用于匹配图标URL */
    protected static readonly ICON_URL_REGEX = /https:\/\/127.0.0.1\/img\/(\d+)/;
    protected constructor() {
        super();
    }

    /** 获取单例实例 */
    public static getInstance(): OKClubManger {
        return this._instance || (this._instance = new OKClubManger());
    }

    /**
     * 处理头像URL
     * @param iconUrl 原始头像URL
     * @returns 处理后的头像URL
     */
    public processAvatarUrl(iconUrl: string | number): string {
        const iconUrlStr = String(iconUrl);

        // 检查是否包含图片扩展名
        if (iconUrlStr.match(/\.(png|jpg|jpeg|gif|webp)(\?|$)/)) {
            // 如果是完整的图片URL，直接使用
            return iconUrlStr;
        }

        // 对于其他URL，提取数字ID
        const match = iconUrlStr.match(/@?https?:\/\/[^\/]+\/(?:img\/)?(\d+)(?:\?|$)/);
        if (match) {
            return match[1];
        }

        return iconUrlStr;
    }

    /**
     * 判断是否为Web URL
     * @param iconUrl 原始头像URL
     * @returns 是否为Web URL
     */
    public isWebUrl(iconUrl: string): boolean {
        const iconUrlStr = String(iconUrl);
        return !!iconUrlStr.match(/\.(png|jpg|jpeg|gif|webp)(\?|$)/);
    }
}
