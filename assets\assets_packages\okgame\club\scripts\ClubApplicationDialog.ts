import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UIconSprite from "../../../../framwork/widget/UIconSprite";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import { okpb } from "../../proto/proto_msg";
import OKGameData from "../../public/OKGameData";
import OKGameManager from "../../public/OKGameManager";
import ItemClubAvator from "./ItemClubAvator";
import OKClubAPI from "./OKClubAPI";

const { ccclass, property } = cc._decorator;
const TAG = "[ClubApplicationDialog]";

/**
 * 申请加入俱乐部的弹框
 */
@ccclass
export default class ClubApplicationDialog extends UBaseDialog {
    @property({ type: cc.Button })
    btnApply: cc.Button = null;

    @property({ type: cc.Button })
    btnClose: cc.Button = null;

    @property({ type: cc.EditBox })
    appMessageEditBox: cc.EditBox = null;

    @property({ type: cc.Label })
    clubNameLabel: cc.Label = null;

    @property({ type: cc.Label })
    clubIdLabel: cc.Label = null;

    @property({ type: cc.Node })
    clubAvatorNode: cc.Node = null;

    /**
     * 推荐人ID
     */
    private referrerID: number = 0;

    private clubInfo: okpb.ClubResp = null;

    onUILoad() {
        const nickName = OKGameData.getInstance().getUserInfo().nickname;
        this.appMessageEditBox.string = ULanguage.getInstance().getLangByID("club.iAm") + nickName;
        this.initListeners();
    }

    onShow(clubInfo: okpb.ClubResp, referrerID: number): void {
        console.log(TAG, "onShow() 收到传参:", JSON.stringify(clubInfo), "referrerID:", referrerID);
        this.clubInfo = clubInfo;
        this.referrerID = referrerID;
        this.clubNameLabel.string = clubInfo.clubName;
        this.clubIdLabel.string = "ID:" + clubInfo.clubId.toString();

        const iconUrl = clubInfo.icon;
        if (iconUrl) {
            const prefabContainer = this.clubAvatorNode.getComponent(UPrefabContainer);
            if (!prefabContainer) {
                // cc.warn(TAG, `${prefabContainer} 缺少 UPrefabContainer 组件`);
                return;
            }
            const script: ItemClubAvator = prefabContainer.getNodeComponent("ItemClubAvator");
            if (!script) {
                // cc.warn(TAG, `${script} 缺少 ItemClubAvator 组件`);
                return;
            }
            script.showNormalMask(true);
            script.setAvatorId(iconUrl, null);
            script.setClickEnable(false);
        }
    }

    initListeners() {
        this.onRegisterEvent(this.btnApply.node, this.onClickApply.bind(this));
        this.onRegisterEvent(this.btnClose.node, this.onClickClose.bind(this));
    }

    onClickApply() {
        const message = this.appMessageEditBox.string;
        if (message.length === 0) {
            OKGameManager.getInstance().showToastById("club.enterApplicationNote");
            return;
        }
        const clubId = this.clubInfo.clubId.toString();
        this.showLoading();
        OKClubAPI.getInstance()
            .ReqJoinClub(Number(clubId), this.referrerID, message)
            .then((res: okpb.RspJoinClub) => {
                if (res.errorCode === okpb.ERET.OK) {
                    console.log(TAG, "ReqJoinClub 请求成功:", JSON.stringify(res));
                    this.onJoinClubRequestSuccess();
                } else {
                    this.onJoinClubRequestFailed(res.errorCode, res.errMsg);
                }
            })
            .catch((err) => {
                this.onJoinClubRequestFailed(1010, err.message);
            })
            .finally(() => {
                this.dismissLoading();
            });
    }

    onClickClose() {
        this.closeDialog();
    }

    /**
     * 发送加入俱乐部请求成功回调
     * @param clubId 请求加入的俱乐部ID
     */
    private onJoinClubRequestSuccess(): void {
        OKGameManager.getInstance().showToastById("club.applicationSent");
        console.log(TAG, "onJoinClubRequestSuccess() 关闭弹窗");
        this.closeDialog();
    }

    /**
     * 发送加入俱乐部请求失败回调
     * @param clubId 请求加入的俱乐部ID
     * @param errorCode 错误码
     * @param errorMsg 错误信息
     */
    private onJoinClubRequestFailed(errorCode: number, errorMsg: string): void {
        OKGameManager.getInstance().showToastById("errorCode." + errorCode);
    }

    private showLoading(): void {
        OKGameManager.getInstance().showLoading();
    }

    private dismissLoading(): void {
        OKGameManager.getInstance().hideLoading();
    }
}
