import { ULanguage } from "../../../../framwork/language/ULanguage";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import ClubIconSelectView from "../../../okgame/club/scripts/ClubIconSelectView";
import { okpb } from "../../../okgame/proto/proto_msg";
import ClubManger, { CLUB_EVENT } from "../ClubManger";
import BaseEditView from "./BaseEditView";
import { EDIT_TYPE_NAME, EDIT_TYPE_NOTICE } from "./EditSubView";

const { ccclass, property } = cc._decorator;
const TAG = "[EditProfileDialog]";

/**
 * 编辑俱乐部信息
 */
@ccclass
export default class EditProfileDialog extends BaseEditView {
    @property(cc.Button)
    btnClose: cc.Button = null;

    @property(cc.Label)
    clubNameLabel: cc.Label = null;

    @property(cc.Label)
    announcementLabel: cc.Label = null;

    @property(cc.Node)
    iconNode: cc.Node = null;

    @property(cc.Node)
    clubNameNode: cc.Node = null;

    @property(cc.Node)
    announcementNode: cc.Node = null;

    @property(cc.Button)
    btnSave: cc.Button = null;
    /**
     * 俱乐部头像选择脚本
     */
    private clubAvatorScript: ClubIconSelectView = null;
    private clubInfo: okpb.ClubResp;

    onUILoad() {
        console.log(TAG, "onUILoad");
        this.initView();
        this.onRegisterEvent(this.btnSave.node, this.clickToSaveReq.bind(this));
        this.onRegisterEvent(this.clubNameNode, this.onClubNameLabelClick.bind(this));
        this.onRegisterEvent(this.announcementNode, this.onAnnouncementLabelClick.bind(this));
        this.onRegisterEvent(this.btnClose.node, this.closeDialog.bind(this));
    }

    /**
     * 显示编辑俱乐部信息
     * @param clubInfo 俱乐部数据
     * @description   {"clubId":6810743,"icon":"http://img.ojbkprj.com/1?x-oss-process=image/resize,w_120","clubName":"dean02的🦅1俱乐部","isInClub":1}
     */
    onShow(clubInfo: okpb.ClubResp) {
        console.log(TAG, "onShow() 俱乐部资料弹窗初始化数据: ", JSON.stringify(clubInfo));
        this.clubInfo = clubInfo;
        if (this.clubInfo) {
            this.clubNameLabel.string = this.clubInfo.clubName;
            this.announcementLabel.string = this.clubInfo.intro || ULanguage.getInstance().getLangByID("club.welcomeToOKPoker");
            // 设置俱乐部头像
            this.clubAvatorScript.setSelectedAvatarId(this.clubInfo.icon);
        } else {
            console.warn(TAG, "onShow: 干哦，初始化编辑俱乐部信息为空");
        }
    }

    initView() {
        // 初始化俱乐部头像公共的脚本
        this.clubAvatorScript = this.iconNode.getComponent(UPrefabContainer).getNodeComponent("ClubIconSelectView");
        if (this.clubAvatorScript) {
            this.clubAvatorScript.showSmallAvator(true);
        } else {
            console.error(TAG, "clubAvatorScript is null");
        }
    }

    /**
     * 显示编辑俱乐部名称弹窗
     */
    onClubNameLabelClick() {
        const clubNameModifyCount = this.clubInfo?.clubNameModifyCount ?? -1;
        if (clubNameModifyCount === -1) {
            console.warn(TAG, "onClubNameLabelClick: 俱乐部名称修改次数异常,不显示");
            return;
        }

        ClubManger.getInstance().showClubEditProfileSubView(this.clubInfo, EDIT_TYPE_NAME, (clubInfo: okpb.ClubResp) => {
            console.log(TAG, "onClubNameLabelClick() 编辑俱乐部名称弹窗回调: ", JSON.stringify(clubInfo));
            this.clubInfo = clubInfo;
            this.clubNameLabel.string = this.clubInfo.clubName;
            this.emitEvent(CLUB_EVENT.UPDATE_CLUB_INFO, this.clubInfo);
        });
    }

    /**
     * 显示编辑俱乐部公告弹窗
     */
    onAnnouncementLabelClick() {
        ClubManger.getInstance().showClubEditProfileSubView(this.clubInfo, EDIT_TYPE_NOTICE, (clubInfo: okpb.ClubResp) => {
            this.clubInfo = clubInfo;
            this.announcementLabel.string = this.clubInfo.intro;
            this.emitEvent(CLUB_EVENT.UPDATE_CLUB_INFO, this.clubInfo);
        });
    }

    /**
     * 点击保存按钮
     * @description 实际上只保存了俱乐部头像，名称和公告在其他地方保存
     */
    clickToSaveReq() {
        console.log(TAG, "onSaveClick Req 准备上传俱乐部编辑后的信息");
        if (!this.clubInfo) {
            console.error(TAG, "onSaveClick: 干哦，初始化编辑俱乐部信息为空");
            return;
        }
        // !获得选择的头像url,没使用回调
        const newIconUrl = this.clubAvatorScript.getSelectedAvatarUrl();
        console.log(TAG, "俱乐部头像: ", newIconUrl);
        if (newIconUrl == this.clubInfo.icon) {
            console.log(TAG, "俱乐部头像没有变化，不进行请求");
            this.closeDialog();
            return;
        }
        this.clubInfo.icon = newIconUrl;

        this.requestEditProfile(this.clubInfo, (newClubInfo: okpb.ClubResp) => {
            this.clubInfo = newClubInfo;
            this.emitEvent(CLUB_EVENT.UPDATE_CLUB_INFO, this.clubInfo);
            this.closeDialog();
        });
    }
}
