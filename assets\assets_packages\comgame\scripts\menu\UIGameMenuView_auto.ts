export default class UIGameMenuView_auto {
    node:cc.Node = null;   
	GameMenuView: cc.Node;
	maskNode: cc.Node;
	view: cc.Node;
	menuLayout: cc.Node;
	exitBtn: cc.Node;
	standBtn: cc.Node;
	lblStand: cc.Node;
	dissolveBtn: cc.Node;
	cardTypeBtn: cc.Node;
	insuranceBtn: cc.Node;
	saveTableLeave: cc.Node;
	lbSaveTable: cc.Node;
	settingBtn: cc.Node;
	squidBtn: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.GameMenuView = this.node;
		this.maskNode = this.GameMenuView.getChildByName("maskNode");
		this.view = this.GameMenuView.getChildByName("view");
		this.menuLayout = this.view.getChildByName("menuLayout");
		this.exitBtn = this.menuLayout.getChildByName("exitBtn-");
		this.standBtn = this.menuLayout.getChildByName("standBtn");
		this.lblStand = this.standBtn.getChildByName("lblStand");
		this.dissolveBtn = this.menuLayout.getChildByName("dissolveBtn-");
		this.cardTypeBtn = this.menuLayout.getChildByName("cardTypeBtn-");
		this.insuranceBtn = this.menuLayout.getChildByName("insuranceBtn-");
		this.saveTableLeave = this.menuLayout.getChildByName("saveTableLeave");
		this.lbSaveTable = this.saveTableLeave.getChildByName("lbSaveTable");
		this.settingBtn = this.menuLayout.getChildByName("settingBtn-");
		this.squidBtn = this.menuLayout.getChildByName("squidBtn-");

    }
}
