export default class UIClubRoundDataItem_auto {
    node:cc.Node = null;   
	ClubRoundDataItem: cc.Node;
	headNode: cc.Node;
	headIcon: cc.Node;
	headFrameIcon: cc.Node;
	labelName: cc.Node;
	labelID: cc.Node;
	labelCardCount: cc.Node;
	labelWinLose: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubRoundDataItem = this.node;
		this.headNode = this.ClubRoundDataItem.getChildByName("headNode");
		this.headIcon = this.headNode.getChildByName("headIcon");
		this.headFrameIcon = this.headNode.getChildByName("headFrameIcon");
		this.labelName = this.ClubRoundDataItem.getChildByName("labelName");
		this.labelID = this.ClubRoundDataItem.getChildByName("labelID");
		this.labelCardCount = this.ClubRoundDataItem.getChildByName("labelCardCount");
		this.labelWinLose = this.ClubRoundDataItem.getChildByName("labelWinLose");

    }
}
