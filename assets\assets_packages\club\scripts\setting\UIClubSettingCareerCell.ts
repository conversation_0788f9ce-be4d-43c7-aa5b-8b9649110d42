import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseView from "../../../../framwork/widget/UBaseView";
import UIClubSettingCareerCell_auto from "./UIClubSettingCareerCell_auto";

const { ccclass, menu, property } = cc._decorator;
const logTag = "[UIClubSettingCareerCell]";

export interface ICareerCellData {
    title: string;
    today: number;
    week: number;
    lastWeek: number;
    total: number;
}

/**
 * 俱乐部设置-生涯记录
 *
 * @export
 * @class UIClubSettingCareerCell
 * @extends {UBaseView}
 */
@ccclass
@menu("okgame/UIClubSettingCareerCell")
export default class UIClubSettingCareerCell extends UBaseView {
    //#region 属性
    protected ui: UIClubSettingCareerCell_auto = null;

    //#endregion

    //#region 生命周期
    onUILoad(): void {
        this.ui = new UIClubSettingCareerCell_auto(this.node);
    }

    start() {}

    //#endregion

    //#region 函数方法
    /**
     * 日志打印
     *
     * @private
     * @param {string} msg
     * @memberof UIClubSettingCareerCell
     */
    private log(msg: string) {
        console.log(`${logTag}${msg}`);
    }
    //#endregion

    //#region 事件处理
    //#endregion

    //#region getter/setter
    //#endregion

    //#region 对外公开的方法
    /**
     * 设置单元格数据
     *
     * @param {ICareerCellData} data
     * @memberof UIClubSettingCareerCell
     */
    public setCellData(data: ICareerCellData) {
        this.ui.cellTitle.getComponent(cc.Label).string = ULanguage.getInstance().getLangByID(data.title);
        this.ui.today.getComponent(cc.Label).string = data.today.toString();
        this.ui.week.getComponent(cc.Label).string = data.week.toString();
        this.ui.lastWeek.getComponent(cc.Label).string = data.lastWeek.toString();
        this.ui.total.getComponent(cc.Label).string = data.total.toString();
    }

    //#endregion
}
