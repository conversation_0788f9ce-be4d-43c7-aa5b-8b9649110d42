import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import OKRoomManger from "../../../okgame/room/scripts/OKRoomManger";
import UIGameMenuView_auto from "./UIGameMenuView_auto";

export interface GameMenuCallbacks {
	onExitCallback?: Function,
	onStandCallback?: Function,
	onDissolveCallback?: Function,
	onCardTypeCallback?: Function,
	onInsuranceCallback?: Function,
	onSaveTableCallback?: Function,
	onSettingCallback?: Function,
	onSquidCallback?: Function,
}

export interface GameMenuStatus {
	standEnable?: boolean,
	saveTableEnable?: boolean,
}


const { ccclass, property } = cc._decorator;
@ccclass
export default class UIGameMenuView extends UBaseDialog {
	public zIndex: number = 0;
	//---
	protected ui: UIGameMenuView_auto = null;
	private _callbacks: GameMenuCallbacks = null
	onUILoad(): void {
		this.ui = new UIGameMenuView_auto(this.node);
		this.ui.menuLayout.y = 1500
		this.onRegisterEvent(this.ui.exitBtn, this.onExitBtn.bind(this))
		this.onRegisterEvent(this.ui.standBtn, this.onStandBtn.bind(this))
		this.onRegisterEvent(this.ui.dissolveBtn, this.onDissolveBtn.bind(this))
		this.onRegisterEvent(this.ui.cardTypeBtn, this.onCardTypeBtn.bind(this))
		this.onRegisterEvent(this.ui.insuranceBtn, this.onInsuranceBtn.bind(this))
		this.onRegisterEvent(this.ui.saveTableLeave, this.onSaveTableBtn.bind(this))
		this.onRegisterEvent(this.ui.settingBtn, this.onSettingBtn.bind(this))
		this.onRegisterEvent(this.ui.squidBtn, this.onSquidBtn.bind(this))
	}

	onShow(callbacks: GameMenuCallbacks, status: GameMenuStatus) {
		this._callbacks = callbacks
		if (!this._callbacks.onStandCallback) {
			this.ui.standBtn.active = false
		}
		if (!this._callbacks.onDissolveCallback) {
			this.ui.dissolveBtn.active = false
		}
		if (!this._callbacks.onCardTypeCallback) {
			this.ui.cardTypeBtn.active = false
		}
		if (!this._callbacks.onInsuranceCallback) {
			this.ui.insuranceBtn.active = false
		}
		if (!this._callbacks.onSaveTableCallback) {
			this.ui.saveTableLeave.active = false
		}
		if (!this._callbacks.onSettingCallback) {
			this.ui.settingBtn.active = false
		}
		if (!this._callbacks.onSquidCallback) {
			this.ui.squidBtn.active = false
		}
		this.updateStandState(status.standEnable)
		this.updateSaveTableState(status.saveTableEnable)
	}

	private updateStandState(state: boolean) {
		if (state) {
			this.ui.standBtn.getComponent(cc.Button).interactable = true
			this.ui.lblStand.opacity = 255
		} else {
			this.ui.standBtn.getComponent(cc.Button).interactable = false
			this.ui.lblStand.opacity = 128
		}
	}


	private updateSaveTableState(state: boolean) {
		if (state) {
			this.ui.saveTableLeave.getComponent(cc.Button).interactable = true
			this.ui.lbSaveTable.opacity = 255
		} else {
			this.ui.saveTableLeave.getComponent(cc.Button).interactable = false
			this.ui.lbSaveTable.opacity = 128
		}
	}

	playEnterAnim() {
		cc.tween(this.ui.menuLayout).to(0.2, {
			y: 0
		}, {
			easing: 'sineOut'
		}).start()
	}

	playExitAnim() {
		cc.tween(this.ui.menuLayout).to(0.2, {
			y: 1500
		}, {
			easing: 'sineIn'
		}).call(this.closeDialog.bind(this)).start()
	}

	onExitBtn() {
		console.log('onExitBtn')
		if (this._callbacks.onExitCallback) {
			if (!this._callbacks.onExitCallback()) {
				this.closeDialog();
			}
		} else {
			OKRoomManger.getInstance().exitRoomReq()
			this.closeDialog();
		}
	}

	onStandBtn() {
		console.log('onStandBtn')
		if (!this._callbacks.onStandCallback()) {
			this.closeDialog();
		}
	}

	onDissolveBtn() {
		console.log('onDissolveBtn')
		if (!this._callbacks.onDissolveCallback()) {
			this.closeDialog();
		}
	}

	onCardTypeBtn() {
		console.log('onCardTypeBtn')
		if (!this._callbacks.onCardTypeCallback()) {
			this.closeDialog();
		}
	}
	onInsuranceBtn() {
		console.log('onInsuranceBtn')
		if (!this._callbacks.onInsuranceCallback()) {
			this.closeDialog();
		}
	}
	onSaveTableBtn() {
		console.log('onSaveTableBtn')
		if (!this._callbacks.onSaveTableCallback()) {
			this.closeDialog();
		}
	}
	onSettingBtn() {
		console.log('onSettingBtn')
		if (!this._callbacks.onSettingCallback()) {
			this.closeDialog();
		}
	}
	onSquidBtn() {
		console.log('onSquidBtn')
		if (!this._callbacks.onSquidCallback()) {
			this.closeDialog();
		}
	}
}