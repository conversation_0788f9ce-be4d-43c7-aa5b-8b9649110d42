import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import { okpb } from "../../proto/proto_msg";
import { ClubJoinHelper } from "./ClubJoinHelper";

const { ccclass, property } = cc._decorator;

const TAG = "[ClubJoinView]";

/**
 * 申请俱乐部
 * @description 就是个查询俱乐部的UI
 */
@ccclass
export default class ClubJoinView extends UBaseDialog {
    @property({ type: cc.Button })
    btnClose: cc.Button = null;

    @property({ type: cc.Button })
    btnJoin: cc.Button = null;

    @property({ type: cc.EditBox })
    idEditBox: cc.EditBox = null;

    @property({ type: cc.EditBox })
    referrerEditBox: cc.EditBox = null;

    @property({ type: cc.Node })
    panelMask: cc.Node = null;

    onUILoad() {
        // 移除不必要的EditBox触摸事件处理，因为EditBox默认会阻止事件冒泡
        this.initListeners();
    }

    initListeners() {
        this.onRegisterEvent(this.btnClose.node, this.onClickClose.bind(this));
        this.onRegisterEvent(this.btnJoin.node, this.onClickJoin.bind(this));
        this.onRegisterEvent(this.panelMask, this.onClickClose.bind(this));
    }

    onClickJoin(): void {
        const clubId: string = this.idEditBox.string.trim();
        const referrer: string = this.referrerEditBox.string.trim();

        if (!clubId) {
            console.warn(TAG, "俱乐部ID不能为空");
            return;
        }

        ClubJoinHelper.getInstance().handleClubJoin(clubId, referrer, (clubInfo: okpb.ClubResp) => {
            console.log(TAG, "查询俱乐部信息成功", clubInfo);
            this.closeDialog();
        });
    }

    onClickClose() {
        this.closeDialog();
    }
}
