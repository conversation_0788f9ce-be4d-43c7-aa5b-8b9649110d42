{"frameRate": 60, "isGlobal": 0, "name": "Animation1", "version": "4.5", "armature": [{"aabb": {"width": 0, "y": 0, "height": 0, "x": 0}, "skin": [{"name": "", "slot": [{"display": [{"path": "meigui_1", "type": "image", "name": "meigui_1", "transform": {}}, {"path": "meigui_2", "type": "image", "name": "meigui_2", "transform": {}}, {"path": "meigui_3", "type": "image", "name": "meigui_3", "transform": {}}, {"path": "meigui_4", "type": "image", "name": "meigui_4", "transform": {}}, {"path": "meigui_5", "type": "image", "name": "meigui_5", "transform": {}}, {"path": "meigui_6", "type": "image", "name": "meigui_6", "transform": {}}, {"path": "meigui_7", "type": "image", "name": "meigui_7", "transform": {}}, {"path": "1", "type": "image", "name": "meigui_8", "transform": {}}, {"path": "2", "type": "image", "name": "meigui_9", "transform": {}}, {"path": "3", "type": "image", "name": "meigui_10", "transform": {}}, {"path": "4", "type": "image", "name": "meigui_11", "transform": {}}, {"path": "5", "type": "image", "name": "meigui_12", "transform": {}}, {"path": "6", "type": "image", "name": "meigui_13", "transform": {}}, {"path": "meigui_18", "type": "image", "name": "meigui_18", "transform": {}}, {"path": "meigui_19", "type": "image", "name": "meigui_19", "transform": {}}, {"path": "meigui_20", "type": "image", "name": "meigui_20", "transform": {}}, {"path": "meigui_21", "type": "image", "name": "meigui_21", "transform": {}}, {"path": "meigui_22", "type": "image", "name": "meigui_22", "transform": {}}, {"path": "meigui_23", "type": "image", "name": "meigui_23", "transform": {}}, {"path": "meigui_24", "type": "image", "name": "meigui_24", "transform": {}}, {"path": "meigui_25", "type": "image", "name": "meigui_25", "transform": {}}], "name": "Layer1"}]}], "animation": [{"frame": [], "duration": 120, "bone": [{"frame": [{"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"tweenEasing": 0, "duration": 6, "transform": {}}, {"duration": 0, "transform": {}}], "name": "Layer1"}], "ik": [], "slot": [{"frame": [{"tweenEasing": 0, "displayIndex": 7, "duration": 6}, {"tweenEasing": 0, "displayIndex": 8, "duration": 6}, {"tweenEasing": 0, "displayIndex": 9, "duration": 6}, {"tweenEasing": 0, "displayIndex": 10, "duration": 6}, {"tweenEasing": 0, "displayIndex": 11, "duration": 6}, {"tweenEasing": 0, "displayIndex": 12, "duration": 6}, {"tweenEasing": 0, "duration": 6}, {"tweenEasing": 0, "displayIndex": 1, "duration": 6}, {"tweenEasing": 0, "displayIndex": 2, "duration": 6}, {"tweenEasing": 0, "displayIndex": 3, "duration": 6}, {"tweenEasing": 0, "displayIndex": 4, "duration": 6}, {"tweenEasing": 0, "displayIndex": 5, "duration": 6}, {"tweenEasing": 0, "displayIndex": 6, "duration": 6}, {"tweenEasing": 0, "displayIndex": 13, "duration": 6}, {"tweenEasing": 0, "displayIndex": 14, "duration": 6}, {"tweenEasing": 0, "displayIndex": 15, "duration": 6}, {"tweenEasing": 0, "displayIndex": 16, "duration": 6}, {"tweenEasing": 0, "displayIndex": 17, "duration": 6}, {"tweenEasing": 0, "displayIndex": 18, "duration": 6}, {"tweenEasing": 0, "displayIndex": 19, "duration": 6}, {"tweenEasing": null, "displayIndex": 20, "duration": 0}], "name": "Layer1"}], "ffd": [], "name": "Animation1"}], "type": "Armature", "frameRate": 60, "bone": [{"inheritScale": false, "name": "Layer1", "transform": {"y": -3.6733, "x": -1}}], "ik": [], "slot": [{"displayIndex": -1, "name": "Layer1", "parent": "Layer1", "color": {}}], "defaultActions": [{"gotoAndPlay": "Animation1"}], "name": "armatureName"}]}