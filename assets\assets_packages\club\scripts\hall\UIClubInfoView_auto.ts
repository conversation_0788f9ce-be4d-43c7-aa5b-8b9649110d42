export default class UIClubInfoView_auto {
    node:cc.Node = null;   
	ClubInfoView: cc.Node;
	avatarNode: cc.Node;
	infoNode: cc.Node;
	nameNode: cc.Node;
	clubName: cc.Node;
	IDNode: cc.Node;
	clubId: cc.Node;
	clubChipNode: cc.Node;
	clubChipBg: cc.Node;
	clubChipIcon: cc.Node;
	clubChipAdd: cc.Node;
	clubChipText: cc.Node;
	clubCashierNode: cc.Node;
	clubCashierBg: cc.Node;
	clubCashierIcon: cc.Node;
	clubCashierAdd: cc.Node;
	clubCashierText: cc.Node;
	clubNoticeNode: cc.Node;
	noticeBg: cc.Node;
	clubNoticeIcon: cc.Node;
	noticeText: cc.Node;
	clubServiceBtn: cc.Node;
	clubServiceBtnImg: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubInfoView = this.node;
		this.avatarNode = this.ClubInfoView.getChildByName("avatarNode");
		this.infoNode = this.ClubInfoView.getChildByName("infoNode");
		this.nameNode = this.infoNode.getChildByName("nameNode");
		this.clubName = this.nameNode.getChildByName("clubName");
		this.IDNode = this.infoNode.getChildByName("IDNode");
		this.clubId = this.IDNode.getChildByName("clubId");
		this.clubChipNode = this.ClubInfoView.getChildByName("clubChipNode");
		this.clubChipBg = this.clubChipNode.getChildByName("clubChipBg");
		this.clubChipIcon = this.clubChipNode.getChildByName("clubChipIcon");
		this.clubChipAdd = this.clubChipNode.getChildByName("clubChipAdd");
		this.clubChipText = this.clubChipNode.getChildByName("clubChipText");
		this.clubCashierNode = this.ClubInfoView.getChildByName("clubCashierNode");
		this.clubCashierBg = this.clubCashierNode.getChildByName("clubCashierBg");
		this.clubCashierIcon = this.clubCashierNode.getChildByName("clubCashierIcon");
		this.clubCashierAdd = this.clubCashierNode.getChildByName("clubCashierAdd");
		this.clubCashierText = this.clubCashierNode.getChildByName("clubCashierText");
		this.clubNoticeNode = this.ClubInfoView.getChildByName("clubNoticeNode");
		this.noticeBg = this.clubNoticeNode.getChildByName("noticeBg");
		this.clubNoticeIcon = this.clubNoticeNode.getChildByName("clubNoticeIcon");
		this.noticeText = this.clubNoticeNode.getChildByName("noticeText");
		this.clubServiceBtn = this.ClubInfoView.getChildByName("clubServiceBtn");
		this.clubServiceBtnImg = this.clubServiceBtn.getChildByName("clubServiceBtnImg");

    }
}
