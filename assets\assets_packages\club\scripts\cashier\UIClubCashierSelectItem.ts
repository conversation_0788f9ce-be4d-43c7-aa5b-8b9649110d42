import UBaseView from "../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../framwork/widget/UPrefabContainer";
import UIIdentityIcon from "../../../okgame/common/scripts/UIIdentityIcon";
import UIClubCashierSelectItem_auto from "./UIClubCashierSelectItem_auto";
import { ClubSelectUserItemData } from "./UIClubCashierTransactionLayer";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierSelectItem extends UBaseView {

	protected ui: UIClubCashierSelectItem_auto = null;
	private _curData: ClubSelectUserItemData = null;
	private father: any = null;

	onUILoad(): void {
		this.ui = new UIClubCashierSelectItem_auto(this.node);
	}

	onEnable(): void {
		this.updateUI();
	}

	setData(father: any, data: ClubSelectUserItemData) {
		if (!cc.isValid(this.node) || !data) return;
		this._curData = data;
		this.father = father;
		this.updateUI();
	}

	updateUI() {
		if (!cc.isValid(this.ui) || !this._curData) return;
		this.ui.toggleSelect.getComponent(cc.Toggle).isChecked = this._curData.isSelect ? true : false;
		this.ui.labelNoteName.getComponent(cc.Label).string = "" + this._curData.data.nickname;
		this.ui.freeTxt.getComponent(cc.Label).string = "" + this._curData.data.chipBalance;
		this.ui.userID.getComponent(cc.Label).string = "ID:" + this._curData.data.userId;
		this.ui.roleTypeImg.getComponent(UPrefabContainer).getNodeComponent(UIIdentityIcon).setIcon(this._curData.data.identity);
	}

	onToggleSelect(toggle: cc.Toggle) {
		let isCheck = toggle.isChecked;
		this._curData.isSelect = isCheck;
		if (this.father.updateSelectNum) this.father.updateSelectNum();
	}

}