import { okpb } from "../../../../okgame/proto/proto_msg";
import ClubAPI from "../../ClubAPI";
import OKGameManager from "../../../../okgame/public/OKGameManager";

/**
 * 俱乐部申请消息管理单例
 * @description 为什么这样设计呢： 俱乐部大厅中会有消息小红点、成员主页面会有小红点，还有兼顾到分页的需求
 */
type MessageList = okpb.JoinClubReqInfo[];
const TAG = "[ClubMessageManager]";
export default class ClubMessageManager {
    private static _instance: ClubMessageManager | null = null;
    public static getInstance(): ClubMessageManager {
        if (!this._instance) this._instance = new ClubMessageManager();
        return this._instance;
    }

    private _messageList: MessageList = [];
    private _messageCount: number = 0;
    private _subscribers: Array<(count: number, list: MessageList) => void> = [];
    private _isRequesting: boolean = false;

    private constructor() {}

    /**
     * 拉取消息列表（自动缓存，支持分页/刷新）
     * @param clubId 俱乐部ID
     * @param page 页码
     * @param isRefresh 是否强制刷新（true时清空缓存，其实就是下拉刷新）
     */
    public async fetchMessageList(clubId: number, page: number = 1, isRefresh: boolean = false): Promise<MessageList> {
        console.log(TAG, "fetchMessageList", clubId, page, isRefresh);
        if (this._isRequesting) return this._messageList;
        this._isRequesting = true;
        if (isRefresh) {
            this._messageList = [];
            this._messageCount = 0;
        }
        try {
            OKGameManager.getInstance().showLoading();
            const res = await ClubAPI.getInstance().ReqGetClubMessages(clubId, page);
            console.log(TAG, "fetchMessageList res", res);
            if (res.errorCode === okpb.ERET.OK) {
                if (page === 1 || isRefresh) {
                    this._messageList = res.joinList || [];
                } else {
                    this._messageList = this._messageList.concat(res.joinList || []);
                }
                this._messageCount = this._messageList.length;
                this.notify();
            }
        } catch (err) {
            console.error("fetchMessageList error:", err);
        } finally {
            this._isRequesting = false;
            OKGameManager.getInstance().hideLoading();
        }
        return this._messageList;
    }

    /**
     * 获取当前缓存的消息列表
     * @returns 消息列表
     */
    public getMessageList(): MessageList {
        return this._messageList;
    }

    /**
     * 获取当前缓存的消息数量
     * @returns 消息数量
     */
    public getMessageCount(): number {
        return this._messageCount;
    }

    /**
     * 删除消息
     * @param mailId 消息ID
     */
    public deleteMessage(mailId: number): void {
        this._messageList = this._messageList.filter((item) => item.mailId !== mailId);
        this._messageCount = this._messageList.length;
        this.notify();
    }

    /**
     * 订阅消息数量变化
     */
    public subscribe(callback: (count: number, list: MessageList) => void): void {
        if (this._subscribers.indexOf(callback) === -1) {
            this._subscribers.push(callback);
        }
    }

    /**
     * 取消订阅
     */
    public unsubscribe(callback: (count: number, list: MessageList) => void): void {
        const idx = this._subscribers.indexOf(callback);
        if (idx !== -1) this._subscribers.splice(idx, 1);
    }

    /**
     * 通知所有订阅者
     */
    private notify(): void {
        for (const cb of this._subscribers) {
            try {
                cb(this._messageCount, this._messageList);
            } catch (e) {
                console.error("ClubMessageManager notify error:", e);
            }
        }
    }

    /**
     * 清空缓存（如登出时调用）
     */
    public clear(): void {
        this._messageList = [];
        this._messageCount = 0;
        this.notify();
    }
}
