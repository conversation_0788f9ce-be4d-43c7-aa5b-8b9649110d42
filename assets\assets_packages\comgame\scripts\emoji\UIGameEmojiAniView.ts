import UBaseDialog from "../../../../framwork/widget/UBaseDialog";
import UIGameEmojiAniView_auto from "./UIGameEmojiAniView_auto";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIGameEmojiAniView extends UBaseDialog {
	protected ui: UIGameEmojiAniView_auto = null;
	onLoad(): void {
		this.ui = new UIGameEmojiAniView_auto(this.node);
	}

	onShow(pos: cc.Vec2, type: string, isSelf: boolean = false): void {
		this.init(pos, type, isSelf);
	}

	init(pos: cc.Vec2, type: string, isSelf: boolean = false) {
		if (type == "emoji10") {
			this.ui.emojiAni.x = pos.x;
		} else {
			this.ui.emojiAni.x = pos.x + 3 * 1.4400;
		}

		const numType = type.replace("emoji", "");
		const numStr = parseInt(numType);
		if (numStr < 10 && !numType.startsWith("0")) {
			type = "emoji" + "0" + numType;
		}
		this.ui.emojiAni.y = pos.y;
		let animation: cc.Animation = this.ui.emojiAni.getComponent(cc.Animation);
		this.ui.emojiAni.getComponent(cc.Animation).play(`${type}`);
		animation.on('finished', () => {
			this.node.destroy();
		}, this);
		if (isSelf) {
			this.ui.emojiMask.width = 162;
			this.ui.emojiMask.height = 162;
		} else {
			this.ui.emojiMask.width = 144;
			this.ui.emojiMask.height = 144;
		}
		this.ui.emojiMask.x = pos.x;
		this.ui.emojiMask.y = pos.y;
	}
}