import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import UPrefabContainer from "../../../../../framwork/widget/UPrefabContainer";
import UIDatePickerBtn from "../../../../okgame/common/scripts/DatePicker/UIDatePickerBtn";
import { okpb } from "../../../../okgame/proto/proto_msg";
import ClubDataManager from "../../ClubDataManager";
import ClubAPI from "../../ClubAPI";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import MemberHelper from "../MemberHelper";
import MemberConst from "../MemberConst";
import ClubManger from "../../ClubManger";
import { IClubCompleteInfo } from "../MemberTypes";
import { ULanguage } from "../../../../../framwork/language/ULanguage";

const TAG = "[AgentDataDialog]";
const { ccclass, property } = cc._decorator;

/**
 * 成员详情-代理数据
 */
@ccclass
export default class AgentDataDialog extends UBaseDialog {
    @property({ type: cc.Node, tooltip: "关闭按钮" })
    closeNode: cc.Node = null;

    @property({ type: cc.Node, tooltip: "下线成员信息" })
    membersInfoNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "下线数量" })
    subLineCountTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "下线筹码值" })
    downlineChipsValueTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "授信额度" })
    agentCreditBalanceValueTxt: cc.Label = null;

    @property({ type: cc.Node, tooltip: "日期选择器" })
    choiceDateNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "选择日期-盈亏值" })
    choiceDateProfitLossTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "选择日期-服务费" })
    choiceDateServiceFeeTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "上周-盈亏值" })
    lastWeekProfitLossTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "上周-服务费" })
    lastWeekServiceFeeTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "总计-盈亏值" })
    totalProfitLossTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "总计-服务费" })
    totalServiceFeeTxt: cc.Label = null;

    private clubId: number = 0;
    private agentId: number = 0;
    /**
     * 日期选择器
     */
    private dateChoiceScript: UIDatePickerBtn = null;

    onUILoad(): void {
        this.initView();
        this.initEvent();
        this.initData();
    }

    private initData(): void {
        this.clubId = ClubDataManager.getInstance().getClubId();
    }

    private initView(): void {
        this.choiceDateNode.active = true;
        this.dateChoiceScript = this.choiceDateNode.getComponent(UPrefabContainer).getNodeComponent(UIDatePickerBtn);
        this.dateChoiceScript.setSerachTimer(this.updateDateTimer.bind(this));
        this.dateChoiceScript.setDateLabelColor(MemberConst.COLOR_GRAY);
        this.dateChoiceScript.setDateTimerFontSize(80);
        this.dateChoiceScript.setDefaultTxt(this.getLangByID("club.selectDate"));
    }

    private getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id) || "";
    }

    private initEvent(): void {
        this.onRegisterEvent(this.membersInfoNode, this.onMembersInfoClick.bind(this));
        this.onRegisterEvent(this.closeNode, this.closeDialog.bind(this));
    }

    /**
     * 显示代理数据
     * @param subCount 下线数量
     * @param agentId 代理ID
     * @param agentCreditBalance 授信额度
     */
    onShow(agentId: number): void {
        console.log(TAG, "onShow() 显示代理数据, agentId:", agentId);
        this.agentId = agentId;
        this.getTotalAgentData();
    }

    /**
     * 显示下线成员弹框
     */
    private onMembersInfoClick(): void {
        console.log(TAG, "onMembersInfoClick()");
        ClubManger.getInstance().showDownlineManagementView(okpb.Identity.IDE_AGENT);
    }

    /**
     * 获取总计数据
     */
    private getTotalAgentData() {
        console.log(TAG, "getTotalAgentData() 获取代理数据, clubId:", this.clubId, "agentId:", this.agentId);
        // 获取总计数据，使用0作为开始时间，当前时间作为结束时间
        const endTime = Date.now();
        const beginTime = 0; // 使用0表示从最早开始
        this.getAgentStatisticsData(beginTime, endTime);
    }

    /**
     * 通用的代理数据统计请求函数
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param successCallback 成功回调
     * @param failCallback 失败回调
     */
    private getAgentStatisticsData(beginTime: number, endTime: number): void {
        console.log(TAG, "getAgentStatisticsData() 获取代理统计数据, beginTime:", beginTime, "endTime:", endTime);
        ClubAPI.getInstance()
            .ReqClubAgentDataStatistics(this.clubId, this.agentId, beginTime, endTime)
            .then((response) => {
                if (cc.isValid(this.node)) {
                    console.log(TAG, "Rsp 获取代理历史数据统计成功:", response);
                    if (response.errorCode === 0) {
                        this.updateTotalDataUI(response);
                    } else {
                        console.error(TAG, "获取代理历史数据统计失败:", response.errMsg);
                        OKGameManager.getInstance().showToast("errorCode." + response.errorCode);
                        this.handleTotalDataFail();
                    }
                }
            })
            .catch((error) => {
                if (cc.isValid(this.node)) {
                    console.error(TAG, "请求代理历史数据统计异常:", error);
                    this.handleTotalDataFail();
                }
            });
    }

    private handleTotalDataFail(): void {
        this.totalProfitLossTxt.string = "--";
        this.totalServiceFeeTxt.string = "--";
    }

    /**
     * 更新代理数据UI显示
     * @param response 代理历史数据统计响应
     */
    private updateTotalDataUI(response: okpb.RspClubAgentDataStatistics): void {
        if (!response) {
            console.warn(TAG, "代理历史数据统计为空");
            return;
        }
        // 更新下线信息
        this.subLineCountTxt.string = response.subCount.toString();
        this.downlineChipsValueTxt.string = response.subTotalChips.toString();
        this.agentCreditBalanceValueTxt.string = response.chipCredit.toString();
        // 更新总计信息
        this.totalProfitLossTxt.string = response.totalWinLose.toString();
        this.totalServiceFeeTxt.string = response.totalServiceFee.toString();
        // 更新选择日期
        this.choiceDateProfitLossTxt.string = response.periodWinLose.toString();
        this.choiceDateServiceFeeTxt.string = response.periodServiceFee.toString();
        // 更新上周信息
        this.lastWeekProfitLossTxt.string = response.lastWeekWinLose.toString();
        this.lastWeekServiceFeeTxt.string = response.lastWeekServiceFee.toString();
    }

    /**
     * 日期选择器回调
     * @param data 日期选择器数据
     */
    public updateDateTimer(data: { startTime: number; endTime: number; isAutoReq: boolean }): void {
        console.log(TAG, "updateDateTimer() 选择的日期: ", data);
        this.getAgentStatisticsData(data.startTime, data.endTime);
    }
}
