import UBaseView from "../../../../../framwork/widget/UBaseView";
import UPrefabContainer from "../../../../../framwork/widget/UPrefabContainer";
import UIListEmpty from "../../../../okgame/common/scripts/UIListEmpty";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameData from "../../../../okgame/public/OKGameData";
import ClubDataManager from "../../ClubDataManager";
import { PermissionUtils } from "../PermissionUtils";
import ItemApplyView from "./ItemApplyView";
import ClubMessageManager from "./ClubMessageManager";

const { ccclass, property } = cc._decorator;

const TAG = "[ApplyView]";
/**
 * 俱乐部成员申请列表
 */
@ccclass("ApplyView")
export default class ApplyView extends UBaseView {
    @property(cc.ScrollView)
    private scrollView: cc.ScrollView = null!;

    @property(cc.Prefab)
    private itemApplyView: cc.Prefab = null!;

    @property(cc.Node)
    private emptyNode: cc.Node = null!;

    /**
     * 俱乐部ID
     */
    private clubId: number = 0;
    /**
     * 用户ID
     */
    private userId: number = 0;
    /**
     * 消息列表
     */
    private messageList: okpb.JoinClubReqInfo[] = [];
    /**
     * 消息列表数量
     */
    private messageListCount: number = 0;
    /**
     * 防抖相关的几个变量
     */
    private isRequesting: boolean = false;
    private debounceTimer: NodeJS.Timeout | null = null;
    private readonly DEBOUNCE_DELAY: number = 300;
    /**
     * 当前页码
     */
    private currentPage: number = 1;
    /**
     * 是否滑动到顶部
     */
    private isScrollToTop: boolean = false;
    /**
     * 是否滑动到底部
     */
    private isScrollToBottom: boolean = false;

    onUILoad(): void {
        // 是否有新成员审核权限
        const myPermissions = ClubDataManager.getInstance().getClubInfo().permissions;
        const hasNewAuditPermission = PermissionUtils.hasNewAuditPermission(myPermissions);
        console.log(TAG, "是否有新成员审核权限:", hasNewAuditPermission, "权限位掩码:", myPermissions);
        if (hasNewAuditPermission) {
            this.initView();
            this.initEvent();
        } else {
            const emptyScript: UIListEmpty = this.emptyNode.getComponent(UPrefabContainer).getNodeComponent(UIListEmpty);
            if (emptyScript) {
                emptyScript.setTitle("club.noPermission");
            }
        }
    }

    onUIDestroy(): void {
        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        super.onUIDestroy();
    }

    private initView(): void {
        this.emptyNode.active = false;
        // 确保 ScrollView 支持弹性滚动
        this.scrollView.elastic = true;
        this.scrollView.bounceDuration = 0.5;
    }

    private initEvent(): void {
        // 添加事件监听
        this.scrollView.node.on("scroll-to-top", this.scrollToTop, this);
        this.scrollView.node.on("scroll-to-bottom", this.scrollToBottom, this);
        this.scrollView.node.on("scroll-ended", this.scrollEnded, this);
    }

    /**
     * 初始化分页数据
     */
    private initData(): void {
        this.currentPage = 1;
        this.messageList = [];
        this.messageListCount = 0;
    }

    /**
     * 设置俱乐部信息
     */
    public showApplyView(): void {
        this.clubId = ClubDataManager.getInstance().getClubId();
        this.userId = OKGameData.getInstance().getUserId();
        console.log(TAG, "当前俱乐部id:", this.clubId, ",我的id:", this.userId);
        this.initData();

        // 先显示缓存信息
        const cacheMessageList = ClubMessageManager.getInstance().getMessageList();
        console.log(TAG, "cacheMessageList:", cacheMessageList);
        this.updateMessageList(cacheMessageList, true);
        this.updateListView(true, cacheMessageList);
        // 之后网络请求最新消息
        this.getMessageList(true);
    }

    /**
     * 刷新数据，用于下拉刷新
     */
    private refreshData(): void {
        if (this.isRequesting) {
            console.log(TAG, "refreshData() 请求正在进行中，忽略本次请求");
            return;
        }
        this.initData();
        // 重新获取数据
        this.getMessageList(true);
    }

    /**
     * 获取俱乐部成员申请列表
     * @param isRefresh 是否是下拉刷新操作
     */
    private async getMessageList(isRefresh: boolean = false): Promise<void> {
        if (this.isRequesting) {
            console.log(TAG, "请求正在进行中，忽略本次请求");
            return;
        }

        if (this.debounceTimer !== null) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }

        console.log(TAG, "Req 请求申请消息列表:", this.currentPage, "isRefresh:", isRefresh);
        this.debounceTimer = setTimeout(async () => {
            this.isRequesting = true;
            try {
                // 记录调用前的消息数量
                const previousMessageCount = this.messageList.length;
                // 调用ClubMessageManager获取消息列表
                const allMessages = await ClubMessageManager.getInstance().fetchMessageList(this.clubId, this.currentPage, isRefresh);
                if (cc.isValid(this.node)) {
                    console.log(TAG, "Rsp: 俱乐部成员申请列表成功，总消息数:", allMessages.length);
                    // 计算新增的消息（用于UI增量更新）
                    let newMessages: okpb.JoinClubReqInfo[] = [];
                    if (isRefresh || this.currentPage === 1) {
                        // 下拉刷新或首次加载：所有消息都是"新的"
                        newMessages = allMessages;
                    } else {
                        // 上拉加载更多：只取新增的部分
                        newMessages = allMessages.slice(previousMessageCount);
                    }
                    // 更新本地数据
                    this.updateMessageList(allMessages, isRefresh);
                    // 更新UI（传入新增的消息用于增量更新）
                    this.updateListView(isRefresh, newMessages);
                }
            } catch (err) {
                if (cc.isValid(this.node)) {
                    console.log(TAG, "Err: 俱乐部成员申请列表", err);
                    // 如果是上拉加载更多失败，需要回滚页码
                    if (!isRefresh && this.currentPage > 1) {
                        this.currentPage--;
                    }
                    this.handleEmptyNode();
                }
            } finally {
                if (cc.isValid(this.node)) {
                    console.log(TAG, "Finally: 俱乐部成员申请列表");
                    this.isRequesting = false;
                }
            }
        }, this.DEBOUNCE_DELAY);
    }

    /**
     * 更新消息列表
     * @param allMessages 从ClubMessageManager获取的完整消息列表
     * @param isRefresh 是否是下拉刷新操作
     */
    private updateMessageList(allMessages: okpb.JoinClubReqInfo[], isRefresh: boolean = false): void {
        // 直接使用ClubMessageManager返回的完整消息列表
        this.messageList = allMessages;
        this.messageListCount = this.messageList.length;
    }

    /**
     * 更新消息列表UI
     * @param isRefresh 是否是下拉刷新操作
     * @param newMessages 新增的消息（上拉加载更多时只包含新增部分，下拉刷新时包含全部消息）
     */
    private updateListView(isRefresh: boolean = false, newMessages?: okpb.JoinClubReqInfo[]): void {
        this.handleEmptyNode();

        if (isRefresh || this.currentPage === 1) {
            // 下拉刷新或首次加载：清空所有子节点，重新添加所有消息
            this.scrollView.content.removeAllChildren();
            this.messageList.forEach((item, index) => {
                const itemApplyView = cc.instantiate(this.itemApplyView);
                itemApplyView.getComponent(ItemApplyView)!.setMsgInfo(item, index, this.deleteItemCallback.bind(this));
                this.scrollView.content.addChild(itemApplyView);
            });
        } else if (newMessages && newMessages.length > 0) {
            // 上拉加载更多：只添加新增的消息项
            const currentChildrenCount = this.scrollView.content.children.length;
            newMessages.forEach((item, index) => {
                const itemApplyView = cc.instantiate(this.itemApplyView);
                const actualIndex = currentChildrenCount + index;
                itemApplyView.getComponent(ItemApplyView)!.setMsgInfo(item, actualIndex, this.deleteItemCallback.bind(this));
                this.scrollView.content.addChild(itemApplyView);
            });
        }
    }

    /**
     * 删除消息的回调处理
     */
    private deleteItemCallback(): void {
        console.log(TAG, "deleteItem() 接收到删除消息");
        this.messageListCount--;

        if (this.messageListCount <= 0) {
            this.messageListCount = 0;
            this.messageList = [];
            // 清空ClubMessageManager的缓存
            ClubMessageManager.getInstance().clear();
            // 如果当前的消息处理完成后，再次获取新的消息
            this.getMessageList(true);
        } else {
            // 如果还有消息，重新拉取数据以保持ClubMessageManager缓存同步
            this.currentPage = 1;
            this.getMessageList(true);
        }

        this.handleEmptyNode();
    }

    /**
     * 处理显示空白页面
     */
    private handleEmptyNode(): void {
        this.emptyNode.active = this.messageListCount === 0;
        if (this.emptyNode.active) {
            this.emptyNode.getComponent(UPrefabContainer).getNodeComponent(UIListEmpty).setTitle("club.noApplyMsg");
        }
    }

    /**
     * 滑动到顶部
     */
    private scrollToTop(): void {
        console.log(TAG, "scrollToTop() ");
        this.isScrollToTop = true;
    }

    /**
     * 滑动到底部
     */
    private scrollToBottom(): void {
        console.log(TAG, "scrollToBottom() ");
        this.isScrollToBottom = true;
    }

    /**
     * 滚动结束
     */
    private scrollEnded(): void {
        console.log(TAG, "scrollEnded()");
        if (this.isScrollToTop) {
            console.log(TAG, "scrollEnded() 触发 下拉刷新");
            this.refreshData();
        } else if (this.isScrollToBottom) {
            console.log(TAG, "scrollEnded() 触发 上拉加载更多");
            this.loadMore();
        }
        this.isScrollToTop = false;
        this.isScrollToBottom = false;
    }

    /**
     * 加载更多数据
     */
    private loadMore(): void {
        this.currentPage++;
        this.getMessageList(false);
    }
}
