import { ULanguage } from "../../../../../framwork/language/ULanguage";
import UBaseView from "../../../../../framwork/widget/UBaseView";
import { okpb } from "../../../../okgame/proto/proto_msg";
import ClubDataManager from "../../ClubDataManager";
import MemberConst from "../MemberConst";
import MemberHelper from "../MemberHelper";

const TAG = "[ItemHistoryChipsView]";
const { ccclass, property } = cc._decorator;
/**
 * 成员详情-->代理授信弹框-->筹码历史ItemView
 */
@ccclass
export default class ItemHistoryChipsView extends UBaseView {
    @property({ type: cc.Label, tooltip: "操作人昵称" })
    accountNameTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "操作类型" })
    operationTypeTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "数值" })
    valueTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "时间" })
    timeTxt: cc.Label = null;

    public setData(data: okpb.ClubChipTransactionData): void {
        console.log(TAG, "setData() 设置数据,接收到的传参: ", JSON.stringify(data));
        const operatorId = data.operatorId;
        const operatorInfo = ClubDataManager.getInstance().getMemberInfoById(operatorId);
        console.log(TAG, "setData() 获取到的操作人信息: ", JSON.stringify(operatorInfo));
        if (operatorInfo) {
            this.accountNameTxt.string = operatorInfo.nickname;
        } else {
            console.warn(TAG, "setData() 获取到的操作人信息为空, 操作人ID: ", operatorId);
            this.accountNameTxt.string = "--";
        }
        if (data.chips > 0) {
            this.operationTypeTxt.string = ULanguage.getInstance().getLangByID("club.send");
        } else {
            this.operationTypeTxt.string = ULanguage.getInstance().getLangByID("club.recover");
        }
        this.valueTxt.string = data.chips.toString();
        if (data.chips > 0) {
            this.valueTxt.node.color = MemberConst.COLOR_GREEN;
        } else {
            this.valueTxt.node.color = MemberConst.COLOR_PINK;
        }
        this.timeTxt.string = MemberHelper.formatTime2(data.createTime);
    }
}
