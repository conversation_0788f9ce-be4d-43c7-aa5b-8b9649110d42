export default class UIClubCreateView_auto {
    node:cc.Node = null;   
	ClubCreateView: cc.Node;
	panelMask: cc.Node;
	panelView: cc.Node;
	clubNameTitle: cc.Node;
	closeBtn: cc.Node;
	closeImg: cc.Node;
	ContentView: cc.Node;
	IconNode: cc.Node;
	clubName: cc.Node;
	inputClubNameNode: cc.Node;
	clubNameEditBox: cc.Node;
	BACKGROUND_SPRITE: cc.Node;
	TEXT_LABEL: cc.Node;
	PLACEHOLDER_LABEL: cc.Node;
	btnConfirm: cc.Node;
	createBg: cc.Node;
	createTxt: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCreateView = this.node;
		this.panelMask = this.ClubCreateView.getChildByName("panelMask");
		this.panelView = this.ClubCreateView.getChildByName("panelView");
		this.clubNameTitle = this.panelView.getChildByName("clubNameTitle");
		this.closeBtn = this.panelView.getChildByName("closeBtn");
		this.closeImg = this.closeBtn.getChildByName("closeImg");
		this.ContentView = this.panelView.getChildByName("ContentView");
		this.IconNode = this.ContentView.getChildByName("IconNode");
		this.clubName = this.ContentView.getChildByName("clubName");
		this.inputClubNameNode = this.ContentView.getChildByName("inputClubNameNode");
		this.clubNameEditBox = this.inputClubNameNode.getChildByName("clubNameEditBox");
		this.BACKGROUND_SPRITE = this.clubNameEditBox.getChildByName("BACKGROUND_SPRITE");
		this.TEXT_LABEL = this.clubNameEditBox.getChildByName("TEXT_LABEL");
		this.PLACEHOLDER_LABEL = this.clubNameEditBox.getChildByName("PLACEHOLDER_LABEL");
		this.btnConfirm = this.ContentView.getChildByName("btnConfirm");
		this.createBg = this.btnConfirm.getChildByName("createBg");
		this.createTxt = this.createBg.getChildByName("createTxt");

    }
}
