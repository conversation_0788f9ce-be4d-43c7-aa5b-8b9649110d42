import { okpb } from "../../okgame/proto/proto_msg";
import { IClubMemberFullInfo } from "./members/MemberTypes";

/**
 * 整合后的俱乐部成员数据
 */
export const mockClubMemberFullInfo: IClubMemberFullInfo[] = [
    {
        userId: 123456789,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=1",
        nickname: "🌟 快乐小王",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 234567891,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=2",
        nickname: "John Smith Jr. The Third",
        realname: "",
        mark: "",
        signature: "",
        identity: okpb.Identity.IDE_TABLE_OPERATOR,
        activeTime: "",
        freeScore: 0,
        serviceFee: 666666660,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
        serviceFeeThisWeek: 0,
    },
    {
        userId: 345678912,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=3",
        nickname: "🎮 游戏达人",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 456789123,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=4",
        nickname: "Nguyễn Văn Anh Minh",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 567891234,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=5",
        nickname: "💎 钻石玩家",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 678912345,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=6",
        nickname: "Maria Isabella Garcia",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 789123456,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=7",
        nickname: "🎲 幸运玩家",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 891234567,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=8",
        nickname: "Trần Th�?Bảo Ngọc",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 912345678,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=9",
        nickname: "🏆 冠军玩家",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 987654321,
        avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=10",
        nickname: "Alexander Chen Wei Ming",
        realname: "",
        mark: "",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 0,
        winLose: 0,
        handCount: 0,
        lastLogin: Date.now(),
        lastPlay: Date.now(),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10001,
        avatar: "https://www.gravatar.com/avatar/205e460b479e2e5b48aec07710c08d50?s=200",
        nickname: "昵称: 玩家1",
        realname: "",
        mark: "",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 1000,
        winLose: 5000,
        handCount: 100,
        lastLogin: Date.now() - Math.floor(Math.random() * 86400000),
        lastPlay: Date.now() - Math.floor(Math.random() * 172800000),
        agentId: 20001,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 5000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10002,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y&s=200",
        nickname: "管理员",
        realname: "",
        mark: "备注：资深玩家 无成员审核和更改职位权限",
        signature: "",
        identity: okpb.Identity.IDE_MANAGER,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2000,
        winLose: -3000,
        handCount: 200,
        lastLogin: Date.now() - Math.floor(Math.random() * 1800000),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 20002,
        agentName: "",
        subCount: 0,
        permissions: 863,
        markDetail: "",
        chipBalance: -3000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10003,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000001?d=identicon&f=y&s=200",
        nickname: "创建者",
        realname: "",
        mark: "备注：俱乐部老板",
        signature: "",
        identity: okpb.Identity.IDE_MASTER,
        activeTime: "",
        freeScore: 0,
        serviceFee: 5000,
        winLose: 10000,
        handCount: 500,
        lastLogin: Date.now(),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 10000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10004,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000002?d=monsterid&f=y&s=200",
        nickname: "代理1",
        realname: "",
        mark: "备注：金牌代理，有所有权",
        signature: "",
        identity: okpb.Identity.IDE_AGENT,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3000,
        winLose: 8000,
        handCount: 300,
        lastLogin: Date.now() - Math.floor(Math.random() * 900000),
        lastPlay: Date.now() - Math.floor(Math.random() * 1800000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 262,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 156000,
        creditChips: 80000,
        changeScoreThisWeek: 12500,
        serviceFeeThisWeek: 1800,
        changeScoreLastWeek: 9800,
        serviceFeeLastWeek: 1600,
        changeScoreTotal: 45000,
        serviceFeeTotal: 8200,
    },
    {
        userId: 10005,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000003?d=wavatar&f=y&s=200",
        nickname: "德州高手",
        realname: "",
        mark: "备注：德州王",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 1500,
        winLose: 12000,
        handCount: 800,
        lastLogin: Date.now() - Math.floor(Math.random() * 7200000),
        lastPlay: Date.now() - Math.floor(Math.random() * 10800000),
        agentId: 20001,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 12000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10006,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000004?d=retro&f=y&s=200",
        nickname: "新手玩家",
        realname: "",
        mark: "备注：新手",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 500,
        winLose: -2000,
        handCount: 50,
        lastLogin: Date.now() - Math.floor(Math.random() * 43200000),
        lastPlay: Date.now() - Math.floor(Math.random() * 86400000),
        agentId: 20002,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: -2000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10007,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000005?d=robohash&f=y&s=200",
        nickname: "机器人玩家",
        realname: "",
        mark: "备注：AI玩家",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3000,
        winLose: 6000,
        handCount: 400,
        lastLogin: Date.now() - Math.floor(Math.random() * 21600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 43200000),
        agentId: 20004,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 6000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10008,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000006?d=mp&f=y&s=200",
        nickname: "代理2",
        realname: "",
        mark: "备注：代理 有所有权",
        signature: "",
        identity: okpb.Identity.IDE_AGENT,
        activeTime: "",
        freeScore: 0,
        serviceFee: 4000,
        winLose: 9000,
        handCount: 600,
        lastLogin: Date.now() - Math.floor(Math.random() * 3600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 262,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 240000,
        creditChips: 120000,
        changeScoreThisWeek: 15600,
        serviceFeeThisWeek: 2400,
        changeScoreLastWeek: 11200,
        serviceFeeLastWeek: 2100,
        changeScoreTotal: 68000,
        serviceFeeTotal: 12500,
    },
    {
        userId: 10009,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000007?d=identicon&f=y&s=200",
        nickname: "管理员",
        realname: "",
        mark: "备注：副管理员，无发放筹码、无收回筹码权限",
        signature: "",
        identity: okpb.Identity.IDE_MANAGER,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2500,
        winLose: -1000,
        handCount: 250,
        lastLogin: Date.now() - Math.floor(Math.random() * 3600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 1017,
        markDetail: "",
        chipBalance: -1000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10010,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000008?d=monsterid&f=y&s=200",
        nickname: "常客玩家",
        realname: "",
        mark: "备注：老玩家",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 1800,
        winLose: 3000,
        handCount: 450,
        lastLogin: Date.now() - Math.floor(Math.random() * 1800000),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 20001,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 3000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10011,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000009?d=wavatar&f=y&s=200",
        nickname: "土豪玩家",
        realname: "",
        mark: "备注：大老板",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 8000,
        winLose: 20000,
        handCount: 1000,
        lastLogin: Date.now() - Math.floor(Math.random() * 900000),
        lastPlay: Date.now() - Math.floor(Math.random() * 1800000),
        agentId: 20002,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 20000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10012,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000010?d=retro&f=y&s=200",
        nickname: "休闲玩家",
        realname: "",
        mark: "备注：休闲玩家",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 800,
        winLose: -1000,
        handCount: 150,
        lastLogin: Date.now() - Math.floor(Math.random() * 86400000),
        lastPlay: Date.now() - Math.floor(Math.random() * 172800000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: -1000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10013,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000011?d=robohash&f=y&s=200",
        nickname: "专业玩家",
        realname: "",
        mark: "备注：职业玩家",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3500,
        winLose: 15000,
        handCount: 600,
        lastLogin: Date.now() - Math.floor(Math.random() * 3600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 20004,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 15000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10014,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000012?d=mp&f=y&s=200",
        nickname: "代理3",
        realname: "",
        mark: "备注：铜牌代理：无回收权",
        signature: "",
        identity: okpb.Identity.IDE_AGENT,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3500,
        winLose: 7000,
        handCount: 320,
        lastLogin: Date.now() - Math.floor(Math.random() * 7200000),
        lastPlay: Date.now() - Math.floor(Math.random() * 10800000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 260,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 95000,
        creditChips: 45000,
        changeScoreThisWeek: 7800,
        serviceFeeThisWeek: 1200,
        changeScoreLastWeek: 5400,
        serviceFeeLastWeek: 900,
        changeScoreTotal: 28000,
        serviceFeeTotal: 5600,
    },
    {
        userId: 10015,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000013?d=identicon&f=y&s=200",
        nickname: "管理员",
        realname: "",
        mark: "备注：助理管理员，有全部权限",
        signature: "",
        identity: okpb.Identity.IDE_MANAGER,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2200,
        winLose: -2000,
        handCount: 280,
        lastLogin: Date.now() - Math.floor(Math.random() * 5400000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 1023,
        markDetail: "",
        chipBalance: -2000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10016,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000014?d=monsterid&f=y&s=200",
        nickname: "新手玩家2",
        realname: "",
        mark: "备注：新玩家",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 600,
        winLose: -1500,
        handCount: 801,
        lastLogin: Date.now() - Math.floor(Math.random() * 43200000),
        lastPlay: Date.now() - Math.floor(Math.random() * 86400000),
        agentId: 20002,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: -1500,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10017,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000015?d=wavatar&f=y&s=200",
        nickname: "常客玩家2",
        realname: "",
        mark: "备注：老常客",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2000,
        winLose: 4000,
        handCount: 380,
        lastLogin: Date.now() - Math.floor(Math.random() * 1800000),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 4000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10018,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000016?d=retro&f=y&s=200",
        nickname: "土豪玩家2",
        realname: "",
        mark: "备注：大客户",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 7000,
        winLose: 18000,
        handCount: 900,
        lastLogin: Date.now() - Math.floor(Math.random() * 900000),
        lastPlay: Date.now() - Math.floor(Math.random() * 1800000),
        agentId: 20004,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 18000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10019,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000017?d=robohash&f=y&s=200",
        nickname: "专业玩家2",
        realname: "",
        mark: "备注：职业选手",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3200,
        winLose: 12000,
        handCount: 550,
        lastLogin: Date.now() - Math.floor(Math.random() * 3600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 20005,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 12000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 10020,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000018?d=mp&f=y&s=200",
        nickname: "休闲玩家2",
        realname: "",
        mark: "备注：休闲玩家",
        signature: "",
        identity: 0,
        activeTime: "",
        freeScore: 0,
        serviceFee: 900,
        winLose: -800,
        handCount: 120,
        lastLogin: Date.now() - Math.floor(Math.random() * 86400000),
        lastPlay: Date.now() - Math.floor(Math.random() * 172800000),
        agentId: 20001,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: -800,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 20001,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000019?d=mp&f=y&s=200",
        nickname: "扑克大师",
        realname: "",
        mark: "备注：扑克大师：无任何权限",
        signature: "",
        identity: okpb.Identity.IDE_AGENT,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2000,
        winLose: 8000,
        handCount: 300,
        lastLogin: Date.now() - Math.floor(Math.random() * 86400000),
        lastPlay: Date.now() - Math.floor(Math.random() * 172800000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 120000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 120000,
        creditChips: 50000,
        changeScoreThisWeek: 8000,
        serviceFeeThisWeek: 1200,
        changeScoreLastWeek: 6000,
        serviceFeeLastWeek: 1000,
        changeScoreTotal: 30000,
        serviceFeeTotal: 5000,
    },
    {
        userId: 20002,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000020?d=identicon&f=y&s=200",
        nickname: "德州新星",
        realname: "",
        mark: "备注：新手",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 1200,
        winLose: 5000,
        handCount: 150,
        lastLogin: Date.now() - Math.floor(Math.random() * 1800000),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 30002,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 5000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 20003,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000021?d=monsterid&f=y&s=200",
        nickname: "俱乐部老板2",
        realname: "",
        mark: "备注：老板2",
        signature: "",
        identity: okpb.Identity.IDE_MASTER,
        activeTime: "",
        freeScore: 0,
        serviceFee: 6000,
        winLose: 15000,
        handCount: 600,
        lastLogin: Date.now(),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 15000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 20004,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000022?d=wavatar&f=y&s=200",
        nickname: "代理大师",
        realname: "",
        mark: "备注：代理大师，有收回筹码和数据权限",
        signature: "",
        identity: okpb.Identity.IDE_AGENT,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3500,
        winLose: 9000,
        handCount: 350,
        lastLogin: Date.now() - Math.floor(Math.random() * 900000),
        lastPlay: Date.now() - Math.floor(Math.random() * 1800000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 258,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 320000,
        creditChips: 150000,
        changeScoreThisWeek: 18500,
        serviceFeeThisWeek: 2800,
        changeScoreLastWeek: 14600,
        serviceFeeLastWeek: 2300,
        changeScoreTotal: 85000,
        serviceFeeTotal: 15200,
    },
    {
        userId: 20005,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000023?d=retro&f=y&s=200",
        nickname: "新手2",
        realname: "",
        mark: "备注：新手",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 700,
        winLose: -1000,
        handCount: 60,
        lastLogin: Date.now() - Math.floor(Math.random() * 43200000),
        lastPlay: Date.now() - Math.floor(Math.random() * 86400000),
        agentId: 30002,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: -1000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 20006,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000024?d=robohash&f=y&s=200",
        nickname: "AI玩家2",
        realname: "",
        mark: "备注：AI2",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2500,
        winLose: 4000,
        handCount: 200,
        lastLogin: Date.now() - Math.floor(Math.random() * 21600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 43200000),
        agentId: 30003,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 4000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 20007,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000025?d=mp&f=y&s=200",
        nickname: "代理新星",
        realname: "",
        mark: "备注：代理新星",
        signature: "",
        identity: okpb.Identity.IDE_AGENT,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3200,
        winLose: 7000,
        handCount: 400,
        lastLogin: Date.now() - Math.floor(Math.random() * 3600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 262,
        markDetail: "",
        chipBalance: 0,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 68000,
        creditChips: 35000,
        changeScoreThisWeek: 5200,
        serviceFeeThisWeek: 800,
        changeScoreLastWeek: 3600,
        serviceFeeLastWeek: 600,
        changeScoreTotal: 18500,
        serviceFeeTotal: 3200,
    },
    {
        userId: 30001,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000039?d=mp&f=y&s=200",
        nickname: "Nguyễn Văn Minh",
        realname: "",
        mark: "备注：成�?-1",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2500,
        winLose: 12000,
        handCount: 450,
        lastLogin: Date.now() - Math.floor(Math.random() * 1800000),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 40001,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 12000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 30002,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000040?d=identicon&f=y&s=200",
        nickname: "Trần Th�?Hương",
        realname: "",
        mark: "备注：成�?-2",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 1800,
        winLose: 9000,
        handCount: 300,
        lastLogin: Date.now() - Math.floor(Math.random() * 900000),
        lastPlay: Date.now() - Math.floor(Math.random() * 1800000),
        agentId: 40002,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 9000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 30003,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000041?d=monsterid&f=y&s=200",
        nickname: "Phạm Quốc Hùng",
        realname: "",
        mark: "备注：成�?-3",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 3200,
        winLose: 15000,
        handCount: 600,
        lastLogin: Date.now(),
        lastPlay: Date.now() - Math.floor(Math.random() * 3600000),
        agentId: 40003,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 15000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 30004,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000042?d=wavatar&f=y&s=200",
        nickname: "Lê Th�?Hoa",
        realname: "",
        mark: "备注：成�?-4",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2100,
        winLose: 7000,
        handCount: 350,
        lastLogin: Date.now() - Math.floor(Math.random() * 900000),
        lastPlay: Date.now() - Math.floor(Math.random() * 1800000),
        agentId: 40004,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 7000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 30005,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000043?d=retro&f=y&s=200",
        nickname: "Đặng Văn Hùng",
        realname: "",
        mark: "备注：成�?-5",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 900,
        winLose: -1000,
        handCount: 60,
        lastLogin: Date.now() - Math.floor(Math.random() * 43200000),
        lastPlay: Date.now() - Math.floor(Math.random() * 86400000),
        agentId: 40005,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: -1000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 30006,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000044?d=robohash&f=y&s=200",
        nickname: "Vũ Th�?Hạnh",
        realname: "",
        mark: "备注：成�?-6",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 1700,
        winLose: 4000,
        handCount: 200,
        lastLogin: Date.now() - Math.floor(Math.random() * 21600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 43200000),
        agentId: 0,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 4000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
    {
        userId: 30007,
        avatar: "https://www.gravatar.com/avatar/00000000000000000000000000000045?d=mp&f=y&s=200",
        nickname: "Ngô Minh Hùng",
        realname: "",
        mark: "备注：成�?-7",
        signature: "",
        identity: okpb.Identity.IDE_GENERAL,
        activeTime: "",
        freeScore: 0,
        serviceFee: 2200,
        winLose: 7000,
        handCount: 400,
        lastLogin: Date.now() - Math.floor(Math.random() * 3600000),
        lastPlay: Date.now() - Math.floor(Math.random() * 7200000),
        agentId: 40007,
        agentName: "",
        subCount: 0,
        permissions: 0,
        markDetail: "",
        chipBalance: 7000,
        joinClubTime: Date.now() - Math.floor(Math.random() * 2592000000),
        subBalance: 0,
        creditChips: 0,
        changeScoreThisWeek: 0,
        serviceFeeThisWeek: 0,
        changeScoreLastWeek: 0,
        serviceFeeLastWeek: 0,
        changeScoreTotal: 0,
        serviceFeeTotal: 0,
    },
];

/**
 * RspGetClubUserList 测试数据
 */
export const mockRspGetClubUserList: okpb.RspGetClubUserList = {
    errorCode: 0,
    errMsg: "",
    sysTime: Date.now(),
    identity: okpb.Identity.IDE_MASTER,
    permissions: 1023,
    data: {
        clubUserMap: {
            msgData: { users: mockClubMemberFullInfo.slice(0, 10) },
            members1: { users: mockClubMemberFullInfo.slice(10, 30) },
            members2: { users: mockClubMemberFullInfo.slice(30, 37) },
            members3: { users: mockClubMemberFullInfo.slice(37, 44) },
        },
    },
    onlineCount: 10,
};

/**
 * RspGetUserHistoryData 测试数据
 */
export const mockRspGetUserHistoryData: okpb.RspGetUserHistoryData = {
    errorCode: 0,
    errMsg: "",
    sysTime: Date.now(),
    data: {
        changeScore: 1000,
        handNum: 100,
        bbPer100Hands: 100,
        serviceFee: 1000,
    },
};
