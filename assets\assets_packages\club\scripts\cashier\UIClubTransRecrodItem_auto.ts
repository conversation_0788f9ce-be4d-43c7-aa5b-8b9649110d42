export default class UIClubTransRecrodItem_auto {
    node:cc.Node = null;   
	ClubTransRecrodItem: cc.Node;
	arrow: cc.Node;
	userHeadNode1: cc.Node;
	headIcon1: cc.Node;
	headFrameIcon1: cc.Node;
	userHeadNode2: cc.Node;
	headIcon2: cc.Node;
	headFrameIcon2: cc.Node;
	CurrencyBgImg: cc.Node;
	clubChipIcon: cc.Node;
	freeTxt: cc.Node;
	roleTypeIcon1: cc.Node;
	userID1: cc.Node;
	labelNoteName1: cc.Node;
	roleTypeIcon2: cc.Node;
	userID2: cc.Node;
	labelNoteName2: cc.Node;
	labelTime: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubTransRecrodItem = this.node;
		this.arrow = this.ClubTransRecrodItem.getChildByName("arrow");
		this.userHeadNode1 = this.ClubTransRecrodItem.getChildByName("userHeadNode1");
		this.headIcon1 = this.userHeadNode1.getChildByName("headIcon1");
		this.headFrameIcon1 = this.userHeadNode1.getChildByName("headFrameIcon1");
		this.userHeadNode2 = this.ClubTransRecrodItem.getChildByName("userHeadNode2");
		this.headIcon2 = this.userHeadNode2.getChildByName("headIcon2");
		this.headFrameIcon2 = this.userHeadNode2.getChildByName("headFrameIcon2");
		this.CurrencyBgImg = this.ClubTransRecrodItem.getChildByName("CurrencyBgImg");
		this.clubChipIcon = this.CurrencyBgImg.getChildByName("clubChipIcon");
		this.freeTxt = this.CurrencyBgImg.getChildByName("freeTxt");
		this.roleTypeIcon1 = this.ClubTransRecrodItem.getChildByName("roleTypeIcon1");
		this.userID1 = this.ClubTransRecrodItem.getChildByName("userID1");
		this.labelNoteName1 = this.ClubTransRecrodItem.getChildByName("labelNoteName1");
		this.roleTypeIcon2 = this.ClubTransRecrodItem.getChildByName("roleTypeIcon2");
		this.userID2 = this.ClubTransRecrodItem.getChildByName("userID2");
		this.labelNoteName2 = this.ClubTransRecrodItem.getChildByName("labelNoteName2");
		this.labelTime = this.ClubTransRecrodItem.getChildByName("labelTime");

    }
}
