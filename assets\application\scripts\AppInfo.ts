/**
 * 应用ID
 */
const APP_ID = 1000;
/**
 * 版本号(一般从平台读取)
 */
const VERSION_CODE = "1";
/**
 * 配置服务器地址
 */
//在发布正式环境时，需要屏蔽此地址: todo: 凯凯说开发服是123
// var SERVER_CONFIG_URL = 'http://ec2-43-199-202-253.ap-east-1.compute.amazonaws.com';
var SERVER_CONFIG_URL = 'http://ec2-18-166-53-123.ap-east-1.compute.amazonaws.com';
//在发布正式环境时，需要屏蔽此地址
var SERVER_CONFIG_URL_TEST = "http://ec2-43-199-202-253.ap-east-1.compute.amazonaws.com";
//在发布正式环境时，需要屏蔽此地址
var SERVER_CONFIG_URL_DEV = "http://ec2-18-166-53-123.ap-east-1.compute.amazonaws.com";
/**
 * 渠道号(一般从平台读取)
 */
const CHANNEL = "0";

export default class AppInfo {
    /**
     * 应用ID
     */
    static appId: number = APP_ID;
    /**
     * 版本号
     */
    static appVersion: string = '';
    /**
     * 版本号
     */
    static versionCode: string = VERSION_CODE;
    /**
     * 版本号
     */
    static resVersion: string = '';
    /**
     * 配置服务器地址-测试
     */
    static serverConfigUrlTest: string = SERVER_CONFIG_URL_TEST;
    /**
     * 配置服务器地址-开发
     */
    static serverConfigUrlDev: string = SERVER_CONFIG_URL_DEV;
    /**
     * 渠道号
     */
    static channel: string = CHANNEL;
    /**
     * 是否开发版本
     */
    static isDev: boolean = false;
    /**
     * 是否正式环境
     */
    static get isOpen(): boolean {
        return AppInfo.serverConfigUrlTest == "" && AppInfo.serverConfigUrlDev == ""
    };
}
