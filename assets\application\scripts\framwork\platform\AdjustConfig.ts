
/**
* ad上报配置
*/

const configWeb = {
    //Register: "",
}

const configAnd = {
    //Register: "",
}

const configIos = {
    //Register: "",
}

//特殊渠道配置
const channelConfig = {
}


export default class AdjustConfig {
    private static _adjustConfig: any = {}
    //-------------------------------
    public static init(key: string) {
        if (!cc.sys.isNative) {
            //Web端
            this._adjustConfig = key ? channelConfig[key] : configWeb;
            return
        }
        if (key) {
            //渠道配置
            let path = 'adjustConfig/' + key + ".json";
            if (jsb.fileUtils.isFileExist(path)) {
                let data = jsb.fileUtils.getStringFromFile(path);
                this._adjustConfig = JSON.parse(data);
            } else {
                this._adjustConfig = channelConfig[key]
            }
            return
        }
        if (cc.sys.os == cc.sys.OS_ANDROID) {
            //And端
            this._adjustConfig = configAnd
            return
        }
        if (cc.sys.os == cc.sys.OS_IOS) {
            //IOS端
            this._adjustConfig = configIos
            return
        }
    }

    public static getAdKey(key: string) {
        if (this._adjustConfig) {
            return this._adjustConfig[key];
        }
        return null;
    }
}
