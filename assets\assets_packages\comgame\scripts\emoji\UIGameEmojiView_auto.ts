export default class UIGameEmojiView_auto {
    node:cc.Node = null;   
	GameEmojiView: cc.Node;
	maskNode: cc.Node;
	view: cc.Node;
	content: cc.Node;
	emojiItem: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.GameEmojiView = this.node;
		this.maskNode = this.GameEmojiView.getChildByName("maskNode");
		this.view = this.GameEmojiView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.emojiItem = this.content.getChildByName("emojiItem");

    }
}
