import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import { CLUB_EVENT } from "../ClubManger";
import UIClubCashierSendView_auto from "./UIClubCashierSendView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierSendView extends UBaseSFixedDialog {

	protected ui: UIClubCashierSendView_auto = null;

	onUILoad(): void {
		super.onUILoad();
		this.ui = new UIClubCashierSendView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.onRegisterEvent(this.ui.btnOK, this.onBtnOK.bind(this));

		//柜台筹码相关数据更新
		this.registerEvent(CLUB_EVENT.UPDATE_CLUB_CASHIER_INFO, this.updateClubCounterInfo.bind(this));

		//缺省页
		this.initListEmpty("room.noTables");
		this.updateClubCounterInfo();
	}

	onShow(data: okpb.ClubUserPO[]): void {
		//封装一层，添加一个选择信息
		let dataList = [];
		for (let i = 0; i < data.length; i++) {
			dataList.push({
				data: data[i],
				isSelect: true,
			});
		}
		this.updateListData({ errorCode: 0 }, dataList);

	}

	updateClubCounterInfo() {
		let data = ClubDataManager.getInstance().getClubCounterInfo();
		if (data) {
			//当前自己的柜台筹码
			this.ui.labelCurChip.getComponent(cc.Label).string = "" + data.data.counterChips;
		}
	}

	updateSelectNum() {
		let num = 0;
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) num++;
		}
		this.ui.labelPeopleNum.getComponent(cc.Label).string = num < 2 ? "" : ("X" + num);
		this.updatelabelTotal();
	}

	onEditReturn() {
		//输入的数量
		let editStr = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		let num = Number(editStr);
		this.updatelabelTotal();
	}

	updatelabelTotal() {
		let counterInfo = ClubDataManager.getInstance().getClubCounterInfo();

		//输入的数量
		let editStr = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		if (editStr.length == 0) editStr = "0";
		let num = Number(editStr);
		let people = 0;
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) people++;
		}

		let total = num * people;
		let isRed = counterInfo.data.counterChips < total;
		this.ui.labelTotal.color = isRed ? cc.Color.RED : cc.Color.WHITE;
		this.ui.labelTotal.getComponent(cc.Label).string = "" + (people * num);
	}

	async onBtnOK() {
		let counterInfo = ClubDataManager.getInstance().getClubCounterInfo();
		if (!counterInfo) return;

		//输入的数量
		let editStr = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		if (editStr.length == 0) {
			OKGameManager.getInstance().showToastById("club.enterQuantity");
			return;
		}
		let num = Number(editStr);

		//选择人数
		let userIdList = [];
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) userIdList.push(this.listData[i].data.userId);
		}

		if (userIdList.length == 0) {
			OKGameManager.getInstance().showToastById("club.leastOneMember");
			return;
		}

		if (counterInfo.data.counterChips < num * userIdList.length) {
			OKGameManager.getInstance().showToastById("club.InsufficientChips");
			return;
		}

		let data: okpb.RspGrantClubChips = await ClubAPI.getInstance().ReqGrantClubChips(userIdList, num, okpb.ClubChipType.CCT_NORMAL);
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode == okpb.ERET.OK) {
			OKGameManager.getInstance().showToast("发放成功");
			//更新俱乐部代币信息
			ClubDataManager.getInstance().requestClubCounterInfo();
			//更新成员列表的筹码信息
			ClubDataManager.getInstance().requestClubAllUserChipBalance();
			this.closeDialog();
		}
	}
}