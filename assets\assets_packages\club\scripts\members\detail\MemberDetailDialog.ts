import { ULanguage } from "../../../../../framwork/language/ULanguage";
import UBaseDialog from "../../../../../framwork/widget/UBaseDialog";
import UIconSprite from "../../../../../framwork/widget/UIconSprite";
import UPrefabContainer from "../../../../../framwork/widget/UPrefabContainer";
import UIDatePickerBtn from "../../../../okgame/common/scripts/DatePicker/UIDatePickerBtn";
import { UIAlertDialogOptions } from "../../../../okgame/common/scripts/UIAlertDialog";
import { okpb } from "../../../../okgame/proto/proto_msg";
import OKGameData from "../../../../okgame/public/OKGameData";
import OKGameManager from "../../../../okgame/public/OKGameManager";
import ClubAPI from "../../ClubAPI";
import ClubDataManager from "../../ClubDataManager";
import ClubManger, { CLUB_UIID } from "../../ClubManger";
import MemberConst, { DetailFeatureEnum, IdentitySettingEnum } from "../MemberConst";
import MemberHelper from "../MemberHelper";
import { IClubCompleteInfo, IClubMemberFullInfo, IPermissionConfig } from "../MemberTypes";
import { PermissionUtils } from "../PermissionUtils";
import ItemFeatureView from "./ItemFeatureView";

const { ccclass, property } = cc._decorator;
const TAG = "[MemberDetailDialog]";

/**
 * 成员详情弹窗
 */
@ccclass
export default class MemberDetailDialog extends UBaseDialog {
    @property({ type: cc.Sprite, tooltip: "返回按钮" })
    backImg: cc.Sprite = null;

    @property({ type: cc.Label, tooltip: "昵称" })
    nickNameTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "用户ID" })
    userIDTxt: cc.Label = null;

    @property({ type: cc.Sprite, tooltip: "角色类型图标" })
    roleTypeImg: cc.Sprite = null;

    @property({ type: cc.Label, tooltip: "账号名称" })
    accountNameTxt: cc.Label = null;

    @property({ type: cc.Sprite, tooltip: "头像" })
    avatorImg: cc.Sprite = null;

    @property({ type: cc.Label, tooltip: "最后登录时间" })
    lastLoginTxt: cc.Label = null;

    @property({ type: cc.Button, tooltip: "编辑按钮" })
    btnEdit: cc.Button = null;

    @property({ type: cc.Toggle, tooltip: "总计" })
    toggleTotal: cc.Toggle = null;

    @property({ type: cc.Label, tooltip: "总计" })
    totalTxt: cc.Label = null;

    @property({ type: cc.Toggle, tooltip: "上周" })
    toggleLastWeek: cc.Toggle = null;

    @property({ type: cc.Label, tooltip: "上周" })
    lastWeekTxt: cc.Label = null;

    @property({ type: cc.Toggle, tooltip: "选择日期" })
    toggleChoiceDate: cc.Toggle = null;

    @property({ type: cc.Node, tooltip: "选择的日期" })
    choiceDateNode: cc.Node = null;

    @property({ type: cc.Node, tooltip: "盈亏节点" })
    profitLossNode: cc.Node = null;

    @property({ type: cc.Label, tooltip: "盈亏值" })
    profitLossValueTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "手数" })
    handNumValueTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "BB值" })
    BBValueTxt: cc.Label = null;

    @property({ type: cc.Label, tooltip: "服务费" })
    serviceFeeValueTxt: cc.Label = null;

    @property({ type: cc.Node, tooltip: "角色设置节点" })
    RoleLayoutNode: cc.Node = null;

    @property({ type: cc.Toggle, tooltip: "管理员" })
    AdminToggle: cc.Toggle = null;

    @property({ type: cc.Label, tooltip: "管理员or开桌员" })
    AdminOrTableOperatorTxt: cc.Label = null;

    @property({ type: cc.Toggle, tooltip: "超级代理" })
    SuperAgentToggle: cc.Toggle = null;

    @property({ type: cc.Toggle, tooltip: "代理" })
    AgentToggle: cc.Toggle = null;

    @property({ type: cc.Toggle, tooltip: "成员" })
    MemberToggle: cc.Toggle = null;

    @property({ type: cc.ScrollView, tooltip: "功能列表节点" })
    featureScrollView: cc.ScrollView = null;

    @property({ type: cc.Prefab, tooltip: "功能列表子项" })
    itemFeaturePrefab: cc.Prefab = null;

    @property({ type: cc.SpriteFrame, tooltip: "创建者的icon图标" })
    createImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "管理员的icon图标" })
    managerImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "超级代理的icon图标" })
    superAgentImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "代理的icon图标" })
    agentImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "开桌员的icon图标" })
    tableOperatorImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "Item代理授信的icon图标" })
    agentCreditImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "Item下线管理的icon图标" })
    peopleImg: cc.SpriteFrame = null;

    @property({ type: cc.SpriteFrame, tooltip: "Item权限限制的icon图标" })
    lockImg: cc.SpriteFrame = null;

    /**
     * 俱乐部信息，返回上个界面需要这个数据
     */
    private clubInfo: IClubCompleteInfo = null;
    /**
     * 目标用户ID
     */
    private targetUserId: number = 0;
    /**
     * 当前用户备注
     */
    private curUserMark: string = "";
    /**
     * 当前用户备注详情
     */
    private curUserMarkDetail: string = "";
    /**
     * 当前用户ID
     */
    private myUserId: number = 0;
    /**
     * 日期选择器
     */
    private dateChoiceScript: UIDatePickerBtn = null;
    /**
     * 底部列表权限的配置
     */
    private listPermissionConfig: IPermissionConfig[] = [];
    /**
     * 当前用户角色
     */
    private myIdentity: okpb.Identity = okpb.Identity.IDE_GENERAL;
    /**
     * 目标用户角色
     */
    private targetUserIdentity: okpb.Identity = okpb.Identity.IDE_GENERAL;
    /**
     * 被查看用户的上线代理id
     */
    private agentId: string = "";
    /**
     * 被查看用户的上线代理名称
     */
    private agentName: string = "";
    /**
     * 被查看用户的授信值
     */
    private creditValue: number = 0;
    /**
     * 被查看用户的下线数量
     */
    private downlineValue: number = 0;
    /**
     * 被查看用户的权限
     */
    private permissions: number = 0;

    onUILoad(): void {
        console.log(TAG, "onUILoad()");
        this.myUserId = OKGameData.getInstance().getUserInfo().userId;
        this.initView();
        this.initEvent();
    }

    private initView(): void {
        console.log(TAG, "initView()");
        this.toggleTotal.isChecked = true;
        this.toggleLastWeek.isChecked = false;
        this.toggleChoiceDate.isChecked = false;

        this.choiceDateNode.active = true;
        this.dateChoiceScript = this.choiceDateNode.getComponent(UPrefabContainer).getNodeComponent(UIDatePickerBtn);
        this.dateChoiceScript.setSerachTimer(this.updateDateTimer.bind(this));
        this.dateChoiceScript.setDateTimerFontSize(40);
        this.dateChoiceScript.setDefaultTxt(this.getLangByID("club.selectDate"));
        this.dateChoiceScript.setDateLabelColor(MemberConst.COLOR_GRAY);
        this.dateChoiceScript.setShowCallback(this.onToggleChoiceDateClick.bind(this, false));

        this.totalTxt.node.color = MemberConst.COLOR_WHITE;
        this.lastWeekTxt.node.color = MemberConst.COLOR_GRAY;
    }

    private initEvent(): void {
        this.onRegisterEvent(this.backImg.node, this.onBackClick.bind(this));
        this.onRegisterEvent(this.btnEdit.node, this.onEditClick.bind(this));
        this.onRegisterEvent(this.toggleTotal.node, this.onToggleTotalClick.bind(this));
        this.onRegisterEvent(this.toggleLastWeek.node, this.onToggleLastWeekClick.bind(this));
        this.onRegisterEvent(this.toggleChoiceDate.node, this.onToggleChoiceDateClick.bind(this, true));
        this.onRegisterEvent(this.profitLossNode, this.onProfitLossNodeClick.bind(this));
        this.onRegisterEvent(this.AdminToggle.node, this.onAdminToggleClick.bind(this));
        this.onRegisterEvent(this.SuperAgentToggle.node, this.onSuperAgentToggleClick.bind(this));
        this.onRegisterEvent(this.AgentToggle.node, this.onAgentToggleClick.bind(this));
        this.onRegisterEvent(this.MemberToggle.node, this.onMemberToggleClick.bind(this));
    }

    /**
     * 设置成员详情
     * @param _memberInfo 成员信息: 来自成员列表
     * @param _agentUserInfo 代理信息: 来自代理列表
     */
    public onShow(_memberInfo: IClubMemberFullInfo): void {
        // TODO: 需要在每次进入这个dialog时，再次刷新最新的个人数据，有个功能： 删除代理时，如果有下线则不能删除，如果用户执行了移除下线，这个时候需要更新下线数量，对这个功能进行判断
        this.clubInfo = ClubDataManager.getInstance().getClubInfo();
        console.log(TAG, "onShow() 成员详情，俱乐部信息: ", this.clubInfo, ",成员信息: ", _memberInfo);
        this.myIdentity = this.clubInfo.identity;
        this.targetUserIdentity = _memberInfo.identity;

        this.toShowMemberUI(_memberInfo);

        this.refreshBaseView();
    }

    onEnable(): void {
        console.log(TAG, "onEnable()");
    }

    onDisable(): void {
        console.log(TAG, "onDisable()");
    }

    private toShowMemberUI(memberInfo: IClubMemberFullInfo): void {
        console.log(TAG, "handleMemberData() 详情中显示成员数据: ", memberInfo);
        this.permissions = memberInfo.permissions;
        this.targetUserId = memberInfo.userId;
        this.curUserMark = memberInfo.mark;
        this.curUserMarkDetail = memberInfo.markDetail;
        this.agentId = memberInfo.agentId.toString();
        this.agentName = memberInfo.agentName;

        // 设置角色图标
        this.changeRoleMarkIcon(memberInfo.identity);
        // 授信余额
        this.creditValue = memberInfo.creditChips;
        // 下线数量
        this.downlineValue = memberInfo.subCount;

        const iconScript = this.avatorImg.getComponent(UIconSprite);
        if (iconScript) {
            iconScript.setIcon(memberInfo.avatar);
        }
        this.accountNameTxt.string = memberInfo.mark ? memberInfo.mark : this.getLangByID("club.nickName") + ": " + memberInfo.nickname;
        this.userIDTxt.string = "ID:" + memberInfo.userId.toString();
        this.nickNameTxt.string = this.getLangByID("club.nickName") + ": " + memberInfo.nickname;
        this.lastLoginTxt.string = this.getLangByID("club.lastLogin") + ": " + MemberHelper.formatTime2(memberInfo.lastLogin);
    }

    /**
     * 根据角色设置角色图标
     * @param identity 角色
     */
    private changeRoleMarkIcon(identity: okpb.Identity): void {
        this.roleTypeImg.node.active = true;
        switch (identity) {
            case okpb.Identity.IDE_MASTER:
                this.roleTypeImg.spriteFrame = this.createImg;
                break;
            case okpb.Identity.IDE_MANAGER:
                this.roleTypeImg.spriteFrame = this.managerImg;
                break;
            case okpb.Identity.IDE_AGENT:
                this.roleTypeImg.spriteFrame = this.agentImg;
                break;
            case okpb.Identity.IDE_TABLE_OPERATOR:
                this.roleTypeImg.spriteFrame = this.tableOperatorImg;
                break;
            case okpb.Identity.IDE_GENERAL:
                this.roleTypeImg.node.active = false;
                break;
        }
    }

    /**
     * 刷新基础信息
     * @description 独立出此方法来，是为了在修改完用户角色后，可以刷新相关的配置信息
     */
    private refreshBaseView() {
        // 角色设置按钮的可见、可用状态处理
        this.changeRoleLayoutState();

        // 获得可查看的大功能块
        const bigFeatures = MemberHelper.getViewableFeatures(this.myIdentity, this.targetUserIdentity);
        console.log(
            TAG,
            "refreshBaseView() 我的角色: ",
            MemberHelper.getRoleName(this.myIdentity),
            ",被看用户角色: ",
            MemberHelper.getRoleName(this.targetUserIdentity),
            ",可以展示大功能块: ",
            MemberHelper.getFeatureNames(bigFeatures)
        );
        this.setViewableFeatures(bigFeatures);

        // 获得可以点击的角色切换按钮
        const identitySettingConfig = MemberHelper.getIdentitySettingConfig(this.myIdentity, this.targetUserIdentity);
        console.log(TAG, "refreshBaseView() 角色切换按钮: ", MemberHelper.getIdentitySettingNames(identitySettingConfig));
        this.setIdentitySettingConfig(identitySettingConfig);

        // 得到可以展示的列表数据
        this.initListPermissionConfig(this.myIdentity, this.targetUserIdentity);

        // 获得汇总数据
        if (this.isHavePermissionToShowData()) {
            this.getTotalData();
        } else {
            this.showNoPremissionState();
        }
    }

    /**
     * 设置上半部分大功能模块
     * @param viewableFeatures 可查看的功能列表
     */
    private setViewableFeatures(bigFeatures: DetailFeatureEnum[]): void {
        this.RoleLayoutNode.active = false;
        console.log(TAG, "setViewableFeatures() 设置可查看的功能列表: ", bigFeatures);
        bigFeatures.forEach((feature) => {
            switch (feature) {
                case DetailFeatureEnum.RoleSetting:
                    this.RoleLayoutNode.active = true;
                    break;
                case DetailFeatureEnum.PermissionSetting:
                    break;
                case DetailFeatureEnum.AgentInfo:
                    break;
                case DetailFeatureEnum.DeleteMember:
                    break;
            }
        });
    }

    /**
     * 设置角色切换按钮的可用、不可用状态
     * @param identitySettingConfig 角色权限配置
     */
    private setIdentitySettingConfig(identitySettingConfig: IdentitySettingEnum[]): void {
        console.log(TAG, "setIdentitySettingConfig() 设置角色权限: ", MemberHelper.getIdentitySettingNames(identitySettingConfig));

        this.AdminToggle.interactable = identitySettingConfig.includes(IdentitySettingEnum.AdminAndTableOperator);
        this.SuperAgentToggle.interactable = false;
        this.AgentToggle.interactable = identitySettingConfig.includes(IdentitySettingEnum.Agent);
        this.MemberToggle.interactable = identitySettingConfig.includes(IdentitySettingEnum.Member);
    }

    /**
     * 设置角色设置模块的可见、可用状态
     */
    private changeRoleLayoutState(): void {
        // 仅创建者和管理员才展示角色设置模块
        if (this.myIdentity === okpb.Identity.IDE_MASTER) {
            this.RoleLayoutNode.active = true;
            this.AdminToggle.node.active = true;
            this.SuperAgentToggle.node.active = true;
            this.AgentToggle.node.active = true;
            this.MemberToggle.node.active = true;
        } else if (this.myIdentity === okpb.Identity.IDE_MANAGER) {
            // 我是管理员 + 对方也是管理员 = 不显示角色设置
            if (this.targetUserIdentity === okpb.Identity.IDE_MANAGER) {
                this.RoleLayoutNode.active = false;
                return;
            }
            // 我是管理员 + 对方是创建者 = 不显示角色设置
            if (this.targetUserIdentity === okpb.Identity.IDE_MASTER) {
                this.RoleLayoutNode.active = false;
                return;
            }
            // 我是管理员 + 没有权限设置角色 = 不显示角色设置
            const hasIdentityPermission = PermissionUtils.hasIdentityPermission(this.clubInfo.permissions);
            console.log(TAG, "changeRoleLayoutState() 是否有权限设置角色:", hasIdentityPermission, "权限位掩码:", this.clubInfo.permissions);
            if (!hasIdentityPermission) {
                this.RoleLayoutNode.active = false;
                return;
            }
            // 我是管理员 + 有权限设置角色 = 显示角色设置
            this.RoleLayoutNode.active = true;
            this.AdminToggle.node.active = false;
            this.SuperAgentToggle.node.active = true;
            this.AgentToggle.node.active = true;
            this.MemberToggle.node.active = true;
            this.SuperAgentToggle.node.width = 305;
            this.AgentToggle.node.width = 305;
            this.MemberToggle.node.width = 305;
        } else {
            this.RoleLayoutNode.active = false;
        }

        // 设置目标用户的角色按钮默认选中
        if (this.targetUserIdentity === okpb.Identity.IDE_MANAGER || this.targetUserIdentity === okpb.Identity.IDE_TABLE_OPERATOR) {
            this.AdminToggle.isChecked = true;
        } else if (this.targetUserIdentity === okpb.Identity.IDE_AGENT) {
            this.AgentToggle.isChecked = true;
        } else if (this.targetUserIdentity === okpb.Identity.IDE_GENERAL) {
            this.MemberToggle.isChecked = true;
        } else {
            // nothing to do.
        }

        // 设置管理员or开桌员的文本
        if (this.targetUserIdentity === okpb.Identity.IDE_MANAGER) {
            this.AdminOrTableOperatorTxt.string = this.getLangByID("club.admin");
        } else if (this.targetUserIdentity === okpb.Identity.IDE_TABLE_OPERATOR) {
            this.AdminOrTableOperatorTxt.string = this.getLangByID("club.dealer");
        } else {
            // 默认显示管理员
            this.AdminOrTableOperatorTxt.string = this.getLangByID("club.admin");
        }
    }

    /**
     * 点击返回按钮
     */
    private onBackClick(): void {
        ClubManger.getInstance().showClubMemberView();
        this.node.destroy();
    }

    private onEditClick(): void {
        console.log(TAG, "onEditClick() 弹出备注编辑框");
        if (this.myUserId === this.targetUserId) {
            console.log(TAG, "onEditClick() 不能编辑自己的备注");
            return;
        }
        ClubManger.getInstance().showMemberEditRemarksView(
            this.targetUserId,
            this.curUserMark,
            this.curUserMarkDetail,
            this.refreshMemberInfo.bind(this)
        );
    }

    /**
     * 基础信息刷新
     */
    private refreshMemberInfo(): void {
        const memberInfo = ClubDataManager.getInstance().getMemberInfoByClubIdAndUserId(this.clubInfo.clubId, this.targetUserId);
        if (memberInfo) {
            this.toShowMemberUI(memberInfo);
        }
    }

    /**
     * 点击管理员(开桌员)的toggle
     */
    private onAdminToggleClick(): void {
        console.log(TAG, "onAdminToggleClick()");
        if (this.myIdentity !== okpb.Identity.IDE_MASTER) {
            console.log(TAG, "onAdminToggleClick() 只有创建者可以修改管理员(开桌员)的角色");
            return;
        }

        // 操作者:创建者 + 被操作者: 开桌员 + 目标: 管理员(开桌员) =  setRoleDialog
        // 操作者:创建者 + 被操作者: 管理员 + 目标: 开桌员 =  setRoleDialog
        if (
            this.myIdentity === okpb.Identity.IDE_MASTER &&
            (this.targetUserIdentity === okpb.Identity.IDE_TABLE_OPERATOR || this.targetUserIdentity === okpb.Identity.IDE_MANAGER)
        ) {
            ClubManger.getInstance().showSetRoleDialog(
                this.clubInfo.clubId,
                this.targetUserId,
                this.targetUserIdentity,
                (newIdentity: okpb.Identity) => {
                    console.log(TAG, "onAdminToggleClick() 接收到新的角色: ", newIdentity);
                    if (newIdentity === this.targetUserIdentity) {
                        console.log(TAG, "onAdminToggleClick() 新角色和旧角色相同，不进行刷新");
                        this.changeRoleLayoutState();
                        return;
                    }
                    this.targetUserIdentity = newIdentity;
                    this.refreshBaseView();
                    this.changeRoleMarkIcon(newIdentity);
                }
            );
            return;
        }

        let descMsg = "";
        if (this.myIdentity === okpb.Identity.IDE_MASTER && this.targetUserIdentity === okpb.Identity.IDE_AGENT) {
            // 操作者: 创建者 + 被操作者: 代理 + 目标: 管理员(开桌员) = 提示dialog + setRoleDialog
            descMsg = this.getLangByID("club.cancelAgentTip");
        } else if (this.myIdentity === okpb.Identity.IDE_MASTER && this.targetUserIdentity === okpb.Identity.IDE_GENERAL) {
            // 操作者：创建者 + 被操作者：普通成员 + 目标：管理员(开桌员) = 提示dialog + setRoleDialog
            descMsg = this.getLangByID("club.changeAgentTip");
        }
        const alertParams: UIAlertDialogOptions = {
            title: this.getLangByID("common.tip"),
            content: descMsg,
            mask: true,
            confirmText: this.getLangByID("common.confirm"),
            cancelText: this.getLangByID("common.cancel"),
            confirm: () => {
                ClubManger.getInstance().showSetRoleDialog(
                    this.clubInfo.clubId,
                    this.targetUserId,
                    this.targetUserIdentity,
                    (newIdentity: okpb.Identity) => {
                        console.log(TAG, "onAdminToggleClick() 接收到新的角色: ", newIdentity);
                        if (newIdentity === this.targetUserIdentity) {
                            console.log(TAG, "onAdminToggleClick() 新角色和旧角色相同，不进行刷新");
                            this.changeRoleLayoutState();
                            return;
                        }
                        this.targetUserIdentity = newIdentity;
                        this.refreshBaseView();
                        this.changeRoleMarkIcon(newIdentity);
                    }
                );
            },
            cancel: this.changeRoleLayoutState.bind(this),
        };
        OKGameManager.getInstance().showAlert(alertParams);
    }

    /**
     * 点击超级代理的toggle
     */
    private onSuperAgentToggleClick(): void {
        // this.changeIdentity(okpb.Identity.IDE_SUPER_AGENT);
    }

    /**
     * 点击代理的toggle
     */
    private onAgentToggleClick(): void {
        if (this.targetUserIdentity === okpb.Identity.IDE_AGENT) {
            console.log(TAG, "onAgentToggleClick() 当前已经是代理，不能修改");
            return;
        }
        console.log(TAG, "onAgentToggleClick()");
        // 操作者：创建者  + 被操作者: 开桌员 + 目标：代理 = 提示dialog
        // 操作者：创建者  + 被操作者: 管理员 + 目标：代理 = 提示dialog
        // 操作者：创建者 + 被操作者：普通成员 + 目标：代理 = 提示dialog
        // 操作者：管理员  + 被操作者: 普通成员 + 目标：代理 = 提示dialog
        const alertParams: UIAlertDialogOptions = {
            title: this.getLangByID("common.tip"),
            content: this.getLangByID("club.changeAgentTip"),
            mask: true,
            confirmText: this.getLangByID("common.confirm"),
            cancelText: this.getLangByID("common.cancel"),
            confirm: this.changeIdentity.bind(this, okpb.Identity.IDE_AGENT),
            cancel: this.changeRoleLayoutState.bind(this),
        };
        OKGameManager.getInstance().showAlert(alertParams);
    }

    /**
     * 点击普通成员的toggle
     */
    private onMemberToggleClick(): void {
        console.log(TAG, "onMemberToggleClick()");
        if (this.myIdentity === okpb.Identity.IDE_GENERAL) {
            console.log(TAG, "onMemberToggleClick() 当前已经是普通成员，不能修改");
            return;
        }
        let descMsg = "";
        if (this.myIdentity === okpb.Identity.IDE_MASTER && this.targetUserIdentity === okpb.Identity.IDE_AGENT) {
            // 操作者：创建者 + 被操作者:代理 + 目标：普通成员= 提示dialog
            descMsg = this.getLangByID("club.cancelAgentTip");
        } else if (this.myIdentity === okpb.Identity.IDE_MANAGER && this.targetUserIdentity === okpb.Identity.IDE_AGENT) {
            // 操作者：管理员 + 被操作者:代理 + 目标：普通成员 = 提示dialog
            descMsg = this.getLangByID("club.cancelAgentTip");
        } else if (this.myIdentity === okpb.Identity.IDE_MASTER && this.targetUserIdentity === okpb.Identity.IDE_TABLE_OPERATOR) {
            // 操作员：创建者 + 被操作者：开桌员 + 目标：普通成员 = 提示dialog
            descMsg = this.getLangByID("club.changeAgentTip");
        } else if (this.myIdentity === okpb.Identity.IDE_MASTER && this.targetUserIdentity === okpb.Identity.IDE_MANAGER) {
            // 操作员：创建者 + 被操作者：管理员 + 目标：普通成员 = 提示dialog
            descMsg = this.getLangByID("club.changeAgentTip");
        }

        const alertParams: UIAlertDialogOptions = {
            title: this.getLangByID("common.tip"),
            content: descMsg, // 取消代理身份，将丢失代理下线资料，仍然要变更许可证?
            mask: true,
            confirmText: this.getLangByID("common.confirm"),
            cancelText: this.getLangByID("common.cancel"),
            confirm: this.changeIdentity.bind(this, okpb.Identity.IDE_GENERAL),
            cancel: this.changeRoleLayoutState.bind(this),
        };
        OKGameManager.getInstance().showAlert(alertParams);
    }

    /**
     * 修改用户角色
     * @param newIdentity 新角色
     */
    private changeIdentity(newIdentity: okpb.Identity): void {
        OKGameManager.getInstance().showLoading();
        console.log(TAG, "Req: 修改角色请求: ", this.clubInfo.clubId, [this.targetUserId], newIdentity);
        ClubAPI.getInstance()
            .ReqChangeClubUserIdentity(this.clubInfo.clubId, [this.targetUserId], newIdentity)
            .then((res: okpb.RspChangeClubUserIdentity) => {
                console.log(TAG, "Rsp: 修改角色返回: ", res);
                if (cc.isValid(this.node)) {
                    if (res && res.errorCode === 0) {
                        OKGameManager.getInstance().showToastById("club.success");
                        this.targetUserIdentity = newIdentity;
                        this.refreshBaseView();
                        this.changeRoleMarkIcon(newIdentity);
                        // 更新缓存中的角色
                        ClubDataManager.getInstance().updateMemberIdentity(this.clubInfo.clubId, this.targetUserId, newIdentity);
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                        this.changeRoleLayoutState();
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    console.error(TAG, "changeIdentity()", err);
                    // 如果设置失败了，就将角色按钮恢复到之前的状态
                    this.changeRoleLayoutState();
                }
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }

    /**
     * 初始化底部的权限列表
     */
    private initListPermissionConfig(myIdentity: okpb.Identity, targetUserIdentity: okpb.Identity): void {
        console.log(TAG, "initListPermissionConfig() 上线代理id:", this.agentId, ",授信值:", this.creditValue, ",下线数量:", this.downlineValue);
        // 使用封装的方法获取权限配置
        const agentInfo = this.agentName && this.agentId !== "0" ? this.agentName + "  (" + this.agentId + ")" : "";
        this.listPermissionConfig = MemberHelper.getPermissionListConfig({
            identity: {
                myIdentity,
                targetUserIdentity,
            },
            values: {
                agentId: agentInfo,
                creditValue: this.creditValue + "", // 授信余额
                downlineValue: this.downlineValue + "", // 下线数量
            },
            images: {
                agentCreditImg: this.agentCreditImg,
                peopleImg: this.peopleImg,
                lockImg: this.lockImg,
            },
        });

        // 需求：如果自己没有删除成员权限，则不展示删除成员的按钮
        const myPermissions = this.clubInfo.permissions;
        const hasDelUserPermission = PermissionUtils.hasDelUserPermission(myPermissions);
        console.log(TAG, "initListPermissionConfig() 是否有删除成员权限:", hasDelUserPermission, "权限位掩码:", myPermissions);
        if (hasDelUserPermission) {
            this.listPermissionConfig = this.listPermissionConfig.filter((item) => item.featureName !== "deleteMember");
        }

        console.log(TAG, "initListPermissionConfig() 得到list会展示的数组: ", this.listPermissionConfig);
        this.featureScrollView.content.removeAllChildren();
        this.listPermissionConfig.forEach((item) => {
            const prefab = cc.instantiate(this.itemFeaturePrefab);
            prefab.getComponent(ItemFeatureView).setData(item, () => {
                console.log(TAG, "点击了", item.featureName);
                switch (item.showDialogPath) {
                    case "deleteMember":
                        console.log(TAG, "显示删除成员弹框");
                        this.showDeleteMemberDialog();
                        break;
                    case "permissionLimit":
                        console.log(TAG, "显示权限限制的弹框,传参目标用户id: ", this.targetUserId, ",权限:", this.permissions);
                        ClubManger.getInstance().showPermissionLimitDialog(this.targetUserId, this.permissions);
                        break;
                    case "chipsHistory":
                        console.log(
                            TAG,
                            "显示筹码历史记录的弹框,传参权限: ",
                            this.permissions,
                            ",目标用户id: ",
                            this.targetUserId,
                            ",余额: ",
                            this.creditValue
                        );
                        ClubManger.getInstance().showChipsHistoryDialog(this.permissions, this.targetUserId, this.creditValue);
                        break;
                    case "agentDataDialog":
                        console.log(TAG, "显示代理数据弹框,传参代理id: ", this.targetUserId);
                        ClubManger.getInstance().showAgentDataDialog(this.targetUserId);
                        break;
                    case "downlineManage":
                        console.log(TAG, "显示下线管理弹框,传参目标身份: ", this.targetUserIdentity);
                        ClubManger.getInstance().showDownlineManagementView(this.targetUserId, this.targetUserIdentity);
                        break;
                    default:
                        ClubManger.getInstance().showDialog(item.showDialogPath, item.value);
                        break;
                }
            });
            this.featureScrollView.content.addChild(prefab);
        });
    }

    /**
     * 显示删除成员的弹框
     */
    private showDeleteMemberDialog(): void {
        console.log(TAG, "showDeleteMemberDialog()");
        const targetUserInfo = ClubDataManager.getInstance().getMemberInfoByClubIdAndUserId(this.clubInfo.clubId, this.targetUserId);
        this.downlineValue = targetUserInfo.subCount;
        console.log(TAG, "showDeleteMemberDialog() 目标用户下线数量:", this.downlineValue);
        if (this.downlineValue > 0) {
            OKGameManager.getInstance().showToastById("club.agentHasDownline");
            return;
        }
        const alertParams: UIAlertDialogOptions = {
            title: this.getLangByID("common.tip"),
            content: this.getLangByID("club.deleteMemberTip"),
            mask: true,
            confirmText: this.getLangByID("common.confirm"),
            cancelText: this.getLangByID("common.cancel"),
            confirm: this.doDeleteMember.bind(this),
            cancel: () => {},
        };

        OKGameManager.getInstance().showAlert(alertParams);
    }

    /**
     * 执行删除成员
     */
    private doDeleteMember(): void {
        console.log(TAG, "doDeleteMember()");
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqDeleteClubMember(this.clubInfo.clubId, this.myUserId, [this.targetUserId])
            .then((res: okpb.RspRemoveClubUser) => {
                if (cc.isValid(this.node)) {
                    console.log(TAG, "doDeleteMember()", res);
                    if (res && res.errorCode === 0) {
                        OKGameManager.getInstance().showToastById("club.success");
                        this.onBackClick();
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    console.error(TAG, "doDeleteMember()", err);
                }
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }

    /**
     * 点击总计的toggle
     */
    private onToggleTotalClick(): void {
        this.dateChoiceScript.setDateLabelColor(MemberConst.COLOR_GRAY);
        this.totalTxt.node.color = MemberConst.COLOR_WHITE;
        this.lastWeekTxt.node.color = MemberConst.COLOR_GRAY;

        this.toggleTotal.isChecked = true;
        this.toggleLastWeek.isChecked = false;
        this.toggleChoiceDate.isChecked = false;
        if (this.isHavePermissionToShowData()) {
            this.getTotalData();
        } else {
            this.showNoPremissionState();
        }
    }

    /**
     * 点击过去7天的toggle
     */
    private onToggleLastWeekClick(): void {
        this.dateChoiceScript.setDateLabelColor(MemberConst.COLOR_GRAY);
        this.totalTxt.node.color = MemberConst.COLOR_GRAY;
        this.lastWeekTxt.node.color = MemberConst.COLOR_WHITE;

        this.toggleTotal.isChecked = false;
        this.toggleLastWeek.isChecked = true;
        this.toggleChoiceDate.isChecked = false;
        if (this.isHavePermissionToShowData()) {
            // 获取最近7天的时间范围
            const timeRange = MemberHelper.getLastSevenDaysRange();
            this.getUserSummaryDataByTime(timeRange.startTime, timeRange.endTime);
        } else {
            this.showNoPremissionState();
        }
    }

    /**
     * 点击选择日期的toggle
     */
    private onToggleChoiceDateClick(isAutoShowDataPicker: boolean): void {
        this.dateChoiceScript.setDateLabelColor(MemberConst.COLOR_WHITE);
        this.totalTxt.node.color = MemberConst.COLOR_GRAY;
        this.lastWeekTxt.node.color = MemberConst.COLOR_GRAY;

        this.setStatisticsDataDefault("0");

        this.toggleTotal.isChecked = false;
        this.toggleLastWeek.isChecked = false;
        this.toggleChoiceDate.isChecked = true;
        if (isAutoShowDataPicker) {
            this.dateChoiceScript.showDatePicker();
        }
        // 网络请求在选择日期后处理
    }

    private getLangByID(id: string): string {
        return ULanguage.getInstance().getLangByID(id);
    }

    /**
     * 获得汇总数据
     */
    private getTotalData(): void {
        console.log(TAG, "getTotalData()获得汇总数据: ", this.clubInfo.clubId, "准备查看的用户id:", this.targetUserId);
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqGetUserHistoryData(this.clubInfo.clubId, this.targetUserId)
            .then((res: okpb.RspGetUserHistoryData) => {
                console.log(TAG, "getTotalData() Rsp 汇总数据:", res);
                if (cc.isValid(this.node)) {
                    if (res && res.errorCode === 0) {
                        this.handleTotalData(res);
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    // console.error(TAG, "getUserDetail()", err);
                }
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }

    private handleTotalData(res: okpb.RspGetUserHistoryData): void {
        console.log(TAG, "handleTotalData() Rsp 汇总数据:", res);
        // TODO: TEST数据,后面要把这个if整个删掉
        if (res.data === null) {
            res.data = {
                changeScore: 564765234,
                handNum: 223456789,
                bbPer100Hands: 1462115,
                serviceFee: 576775444333,
            };
        }
        if (res.data) {
            this.setStatisticsData(res.data.changeScore, res.data.handNum, res.data.bbPer100Hands, res.data.serviceFee);
        } else {
            this.setStatisticsDataDefault("0");
        }
    }

    /**
     * 是否有权限展示数据
     * @returns 是否有权限展示数据
     */
    private isHavePermissionToShowData(): boolean {
        const hasReadDataPermission = PermissionUtils.hasReadDataPermission(this.clubInfo.permissions);
        console.log(TAG, "isHavePermissionToShowData() 是否有权限展示数据:", hasReadDataPermission, "权限位掩码:", this.clubInfo.permissions);
        return hasReadDataPermission;
    }

    /**
     * 显示没有权限的默认值
     */
    private showNoPremissionState(): void {
        this.setStatisticsDataDefault("**");
    }

    /**
     * 获得历史数据 按日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    private getUserSummaryDataByTime(startTime: number, endTime: number): void {
        console.log(TAG, "getUserSummaryDataByTime()", this.clubInfo.clubId, "myUserId:", this.myUserId, "targetUserId:", this.targetUserId);
        OKGameManager.getInstance().showLoading();
        ClubAPI.getInstance()
            .ReqGetUserHistoryDataByDate(this.clubInfo.clubId, this.targetUserId, startTime, endTime)
            .then((res: okpb.RspGetUserHistoryDataByDate) => {
                console.log(TAG, "getUserSummaryDataByTime()", res);
                if (cc.isValid(this.node)) {
                    if (res && res.errorCode === 0) {
                        this.handleUserDetail(res);
                    } else {
                        OKGameManager.getInstance().showToastById("errorCode." + res.errorCode);
                    }
                }
            })
            .catch((err) => {
                if (cc.isValid(this.node)) {
                    // console.error(TAG, "getUserSummaryDataByTime()", err);
                }
            })
            .finally(() => {
                OKGameManager.getInstance().hideLoading();
            });
    }

    /**
     * 处理历史数据 按日期
     * @param res 历史数据 按日期
     */
    private handleUserDetail(res: okpb.RspGetUserHistoryData): void {
        console.log(TAG, "handleUserDetail()", res);
        if (res.data === null) {
            // TODO: TEST数据,后面要把这个if整个删掉
            res.data = {
                changeScore: 100 + Math.floor(Math.random() * 100000000),
                handNum: 100 + Math.floor(Math.random() * 100000000),
                bbPer100Hands: 100 + Math.floor(Math.random() * 100000000),
                serviceFee: 100 + Math.floor(Math.random() * 100000000),
            };
        }

        if (res.data === null) {
            this.setStatisticsDataDefault("0");
        } else {
            this.setStatisticsData(res.data.changeScore, res.data.handNum, res.data.bbPer100Hands, res.data.serviceFee);
        }
    }

    /**
     * 更新日期选择器
     * @param data 日期选择器数据
     */
    public updateDateTimer(data: { startTime: number; endTime: number; isAutoReq: boolean }) {
        console.log(TAG, "updateDateTimer() 选择日期: ", data, "toggleChoiceDate.isChecked:", this.toggleChoiceDate.isChecked);
        if (this.toggleChoiceDate.isChecked) {
            if (this.isHavePermissionToShowData()) {
                this.getUserSummaryDataByTime(data.startTime, data.endTime);
            } else {
                this.showNoPremissionState();
            }
        }
    }

    /**
     * 盈亏节点点击
     */
    private onProfitLossNodeClick(): void {
        console.log(TAG, "onProfitLossNodeClick()");
        // 理论上应该有个弹框介绍盈亏，但是暂时不做
    }

    /**
     * 设置统计数据显示
     * @param profitLoss 盈亏值
     * @param handNum 手数
     * @param bbValue BB值
     * @param serviceFee 服务费
     */
    private setStatisticsData(profitLoss: string | number, handNum: string | number, bbValue: string | number, serviceFee: string | number): void {
        this.profitLossValueTxt.string = profitLoss.toString();
        this.handNumValueTxt.string = handNum.toString();
        this.BBValueTxt.string = bbValue.toString();
        this.serviceFeeValueTxt.string = serviceFee.toString();
    }

    /**
     * 设置统计数据为默认值
     * @param defaultValue 默认值，默认为 "0"，也可以传 "*" 或 "**"
     */
    private setStatisticsDataDefault(defaultValue: string = "0"): void {
        this.setStatisticsData(defaultValue, defaultValue, defaultValue, defaultValue);
    }
}
