import { ULanguage } from "../../../../framwork/language/ULanguage";
import UBaseSFixedDialog from "../../../../framwork/widget/UBaseSFixedDialog";
import { okpb } from "../../../okgame/proto/proto_msg";
import OKGameManager from "../../../okgame/public/OKGameManager";
import ClubAPI from "../ClubAPI";
import ClubDataManager from "../ClubDataManager";
import { CLUB_EVENT } from "../ClubManger";
import UIClubCashierRecoverView_auto from "./UIClubCashierRecoverView_auto";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubCashierRecoverView extends UBaseSFixedDialog {

	protected ui: UIClubCashierRecoverView_auto = null;
	private maxChips: number = 100000000;

	onUILoad(): void {
		super.onUILoad();
		this.ui = new UIClubCashierRecoverView_auto(this.node);
		this.onRegisterEvent(this.ui.btnClose, this.playExitAnim.bind(this));
		this.onRegisterEvent(this.ui.btnOK, this.onBtnOK.bind(this));
		//缺省页
		this.initListEmpty("room.noTables");
	}

	onShow(data: okpb.ClubUserPO[]): void {
		//封装一层，添加一个选择信息
		let dataList = [];
		for (let i = 0; i < data.length; i++) {
			dataList.push({
				data: data[i],
				isSelect: true,
			});
		}
		this.updateListData({ errorCode: 0 }, dataList);
		this.updateSelectNum();

		this.ui.EditBoxMask.active = false;
		this.ui.EditBoxNum.getComponent(cc.EditBox).string = ULanguage.getInstance().getLangByID("club.enterQuantity");
	}

	updateSelectNum() {
		let num = 0;
		this.maxChips = 100000000;
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) {
				num++;
				if (this.maxChips > this.listData[i].data.chipBalance) {
					this.maxChips = this.listData[i].data.chipBalance;
				}
			}
		}
		if (num == 0) this.maxChips = 0;
		this.ui.labelMaxNum.getComponent(cc.Label).string = "" + this.maxChips;
		this.ui.labelPeopleNum.getComponent(cc.Label).string = num < 2 ? "" : ("X" + num);
		this.updateLabelTotal();
	}

	onEditReturn() {
		//输入的数量
		let editStr = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		if (editStr.length == 0) editStr = "0";
		let num = Number(editStr);

		let isRed = num > this.maxChips;
		this.ui.labelMaxNum.color = isRed ? cc.Color.RED : cc.Color.WHITE;
		this.updateLabelTotal();
	}

	onBtnAllRecover(toggle: cc.Toggle) {
		let isAll = toggle.isChecked;
		if (isAll) {
			this.ui.EditBoxMask.active = true;
			this.ui.EditBoxNum.getComponent(cc.EditBox).blur();
			this.ui.EditBoxNum.getComponent(cc.EditBox).string = ULanguage.getInstance().getLangByID("club.recoverAll");
		} else {
			this.ui.EditBoxMask.active = false;
			this.ui.EditBoxNum.getComponent(cc.EditBox).string = ULanguage.getInstance().getLangByID("club.enterQuantity");
		}
		this.updateLabelTotal();
	}

	updateLabelTotal() {
		if (this.ui.toggleSelect.getComponent(cc.Toggle).isChecked) {
			let total = 0;
			for (let i = 0; i < this.listData.length; i++) {
				total += this.listData[i].data.chips;
			}
			return;
		} else {
			let people = 0;
			for (let i = 0; i < this.listData.length; i++) {
				if (this.listData[i].isSelect) people++;
			}

			//输入的数量
			let editStr = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
			if (editStr.length == 0) editStr = "0";
			let num = Number(editStr);

			//若是大于最大数量
			let isSccess = num <= this.maxChips;
			this.ui.labelTotal.getComponent(cc.Label).string = isSccess ? ("" + (people * num)) : "0";
		}
	}

	async onBtnOK() {
		//输入的数量
		let editStr = this.ui.EditBoxNum.getComponent(cc.EditBox).string;
		if (editStr.length == 0) {
			OKGameManager.getInstance().showToastById("club.enterQuantity");
			return;
		}
		let num = Number(editStr);
		let isAll = this.ui.toggleSelect.getComponent(cc.Toggle).isChecked;

		//超过了输入的最大筹码数量
		if (!isAll && num > this.maxChips) {
			return;
		}

		//选择人数
		let userIdList = [];
		for (let i = 0; i < this.listData.length; i++) {
			if (this.listData[i].isSelect) userIdList.push(this.listData[i].data.userId);
		}

		//没有选择回收全部的情况下，没有选择任何成员列表,提示错误
		if (!isAll && userIdList.length == 0) {
			OKGameManager.getInstance().showToastById("club.leastOneMember");
			return;
		}

		let data: okpb.RspRecycleClubChips = await ClubAPI.getInstance().ReqRecycleClubChips(isAll, userIdList, (isAll ? null : num));
		if (!cc.isValid(this.node)) return;
		if (data && data.errorCode == okpb.ERET.OK) {
			OKGameManager.getInstance().showToast("回收成功");
			ClubDataManager.getInstance().requestClubCounterInfo();
			ClubDataManager.getInstance().requestClubAllUserChipBalance();
			this.closeDialog();
		}
	}

}