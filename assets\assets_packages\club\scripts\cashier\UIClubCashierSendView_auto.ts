export default class UIClubCashierSendView_auto {
    node:cc.Node = null;   
	ClubCashierSendView: cc.Node;
	Mask: cc.Node;
	View: cc.Node;
	BG: cc.Node;
	btnClose: cc.Node;
	closeImg: cc.Node;
	titleTxt: cc.Node;
	ChipNode: cc.Node;
	ChipBg: cc.Node;
	ChipIcon: cc.Node;
	labelCurChip: cc.Node;
	EditBoxNum: cc.Node;
	BACKGROUND_SPRITE: cc.Node;
	TEXT_LABEL: cc.Node;
	PLACEHOLDER_LABEL: cc.Node;
	labelPeopleNum: cc.Node;
	ScrollView: cc.Node;
	view: cc.Node;
	content: cc.Node;
	listEmpty: cc.Node;
	totalLayout: cc.Node;
	labelTips2: cc.Node;
	labelTotal: cc.Node;
	btnOK: cc.Node;
	labeOK: cc.Node;


    constructor(node:cc.Node) {
        this.node = node;
		this.ClubCashierSendView = this.node;
		this.Mask = this.ClubCashierSendView.getChildByName("Mask");
		this.View = this.ClubCashierSendView.getChildByName("View");
		this.BG = this.View.getChildByName("BG");
		this.btnClose = this.BG.getChildByName("btnClose");
		this.closeImg = this.btnClose.getChildByName("closeImg");
		this.titleTxt = this.BG.getChildByName("titleTxt");
		this.ChipNode = this.View.getChildByName("ChipNode");
		this.ChipBg = this.ChipNode.getChildByName("ChipBg");
		this.ChipIcon = this.ChipNode.getChildByName("ChipIcon");
		this.labelCurChip = this.ChipNode.getChildByName("labelCurChip");
		this.EditBoxNum = this.View.getChildByName("EditBoxNum");
		this.BACKGROUND_SPRITE = this.EditBoxNum.getChildByName("BACKGROUND_SPRITE");
		this.TEXT_LABEL = this.EditBoxNum.getChildByName("TEXT_LABEL");
		this.PLACEHOLDER_LABEL = this.EditBoxNum.getChildByName("PLACEHOLDER_LABEL");
		this.labelPeopleNum = this.EditBoxNum.getChildByName("labelPeopleNum");
		this.ScrollView = this.View.getChildByName("ScrollView");
		this.view = this.ScrollView.getChildByName("view");
		this.content = this.view.getChildByName("content");
		this.listEmpty = this.ScrollView.getChildByName("listEmpty");
		this.totalLayout = this.View.getChildByName("totalLayout");
		this.labelTips2 = this.totalLayout.getChildByName("labelTips2");
		this.labelTotal = this.totalLayout.getChildByName("labelTotal");
		this.btnOK = this.View.getChildByName("btnOK");
		this.labeOK = this.btnOK.getChildByName("labeOK");

    }
}
