import OKBaseAP<PERSON> from "../../okgame/network/OKBaseAPI";
import { okpb } from "../../okgame/proto/proto_msg";

export default class ComGameAPI extends OKBaseAPI {
    //------------
    protected static _instance = null;
    public static getInstance(): ComGameAPI {
        if (this._instance == null) {
            this._instance = new ComGameAPI();
        }
        return this._instance;
    }

    //------------
    constructor() {
        super();
    }

    // 获取好友房聊天记录的请求结构体
    public async ReqGetChatRecord(roomId: number, lastSendTime?: number): Promise<okpb.RspGetChatRecord> {
        let req = {} as okpb.ReqGetChatRecord;
        req.roomId = roomId;
        if (lastSendTime) {
            req.lastSendTime = lastSendTime;
        }
        let data = await this.request('/getChatRecord.ajax', req);
        return data;
    }

    // 获取未处理消息总数请求
    public async ReqGetUserUnDealMsgCount(): Promise<okpb.RspGetUserUnDealMsgCount> {
        let data = await this.request('/getUserUnDealMsgCount.ajax', null);
        return data;
    }

    // 获取动态消息列表响应
    public async ReqGetUserUnDealMsg(): Promise<okpb.RspGetMsgList> {
        return this.request('/getUserUnDealMsg.ajax', null);
    }

    // 获取房间详情请求(好友局拉取,牌局排行榜展示用到)
    public ReqGetRoomInfoV2(roomId: number): Promise<okpb.RspGetRoomInfoV2> {
        let req = {} as okpb.ReqGetRoomInfoV2;
        req.roomId = roomId;
        return this.request('/battle/getRoomInfoV2', req);
    }

}
