{"skeleton": {"hash": "UgPE1parYEbgAtmiaED8hTLsjcg", "spine": "3.6.38", "width": 0, "height": 0, "images": "./images/"}, "bones": [{"name": "root"}, {"name": "bone13", "parent": "root", "x": -0.09, "y": 8.21, "scaleX": 0.4, "scaleY": 0.4}, {"name": "bone", "parent": "bone13", "length": 233.76, "rotation": 90, "x": 0.09, "y": 67.36, "scaleX": 0.7, "scaleY": 0.7}, {"name": "bone2", "parent": "bone", "length": 59.37, "rotation": -93.4, "x": 10.02, "y": -25.15}, {"name": "bone3", "parent": "bone2", "length": 47.14, "rotation": 1.53, "x": 71.06, "y": -1.06}, {"name": "bone4", "parent": "bone3", "length": 51.61, "rotation": 3.01, "x": 47.14}, {"name": "bone5", "parent": "bone", "length": 54.71, "rotation": 91.08, "x": 12.5, "y": 17.54}, {"name": "bone6", "parent": "bone5", "length": 54.72, "rotation": -2.7, "x": 61.94, "y": 0.38}, {"name": "bone7", "parent": "bone6", "length": 23.36, "rotation": -45.17, "x": 47.32, "y": 17.83}, {"name": "bone8", "parent": "bone5", "length": 22.88, "rotation": -118.38, "x": 10.77, "y": -65.09}, {"name": "bone9", "parent": "bone2", "length": 24.8, "rotation": 55.92, "x": 34.39, "y": 36.66}, {"name": "bone10", "parent": "bone5", "length": 30.61, "rotation": 133.92, "x": 25.01, "y": 63.9}, {"name": "bone11", "parent": "bone5", "length": 44.13, "rotation": -133.07, "x": 17.18, "y": 31.24}, {"name": "bone12", "parent": "bone6", "x": 16.89, "y": -24.49}, {"name": "bone18", "parent": "bone13", "length": 224.35, "rotation": 90, "x": 35.04, "y": 1094.77}, {"name": "bone19", "parent": "bone18", "x": -20.38, "y": -195.65}, {"name": "bone20", "parent": "bone18", "x": -166.56, "y": 8.49}, {"name": "bone21", "parent": "bone18", "x": -1.35, "y": 222.14}, {"name": "bone22", "parent": "bone18", "x": 111.97, "y": -4.49}, {"name": "bone23", "parent": "bone22", "x": 64.88, "y": -8.65}, {"name": "bone24", "parent": "bone18", "x": 15.93, "y": 18.71}], "slots": [{"name": "wang02_bianyuan2", "bone": "bone18", "attachment": "wang02_bianyuan"}, {"name": "wang02_neiceng01", "bone": "bone18"}, {"name": "weiba", "bone": "bone3", "attachment": "weiba"}, {"name": "yuqi3", "bone": "bone10", "attachment": "yuqi3"}, {"name": "yuqi1", "bone": "bone8", "attachment": "yuqi1"}, {"name": "yuqi2", "bone": "bone9", "attachment": "yuqi2"}, {"name": "shenti", "bone": "bone", "attachment": "shenti"}, {"name": "yuzui", "bone": "bone7", "attachment": "yuzui"}, {"name": "yushou", "bone": "bone11", "attachment": "yushou"}, {"name": "yanjing", "bone": "bone12", "attachment": "yanjing"}, {"name": "yanpi", "bone": "bone12", "attachment": "yanpi"}, {"name": "wang02_neiceng02", "bone": "bone18"}, {"name": "biyan", "bone": "bone12"}, {"name": "wang02_bianyuan", "bone": "bone18", "attachment": "wang02_bianyuan"}], "skins": {"default": {"shenti": {"shenti": {"type": "mesh", "uvs": [0.47959, 0.15653, 0.60818, 0.19283, 0.71721, 0.24727, 0.83741, 0.32591, 0.93106, 0.38438, 1, 0.43076, 1, 0.74621, 0.90707, 0.83756, 0.78601, 0.91275, 0.68132, 0.94941, 0.52742, 0.95997, 0.39724, 0.95705, 0.25269, 0.91303, 0.14187, 0.80645, 0.08264, 0.67561, 0.11618, 0.58487, 0.05608, 0.48808, 0.12737, 0.35903, 0.22101, 0.24525, 0.34401, 0.16661, 0.33242, 0.76439, 0.35758, 0.44176, 0.63572, 0.49217, 0.62874, 0.78052, 0.84119, 0.60711], "triangles": [17, 18, 21, 15, 16, 17, 15, 17, 21, 20, 15, 21, 14, 15, 20, 13, 14, 20, 12, 13, 20, 12, 20, 11, 21, 19, 0, 18, 19, 21, 22, 1, 2, 21, 0, 1, 21, 1, 22, 20, 21, 22, 23, 20, 22, 10, 11, 20, 23, 10, 20, 22, 2, 3, 24, 3, 4, 24, 4, 5, 22, 3, 24, 24, 5, 6, 23, 22, 24, 7, 24, 6, 23, 24, 7, 8, 23, 7, 9, 23, 8, 9, 10, 23], "vertices": [3, 3, -66.38, 72.41, 0.05914, 6, 17.88, -74.09, 0.85486, 7, -40.5, -76.46, 0.086, 3, 3, -32.1, 67.8, 0.2676, 6, -15.93, -66.81, 0.72346, 7, -74.62, -70.78, 0.00895, 2, 3, -2.77, 59.56, 0.60418, 6, -44.52, -56.3, 0.39582, 3, 3, 29.76, 47.07, 0.93228, 6, -75.98, -41.32, 0.06772, 7, -135.8, -48.15, 0, 3, 3, 55.07, 37.86, 0.99852, 6, -100.5, -30.15, 0.00148, 7, -160.81, -38.16, 0, 2, 3, 73.74, 30.46, 1, 7, -179.25, -30.19, 0, 1, 3, 77.17, -27.16, 1, 2, 3, 53.67, -45.3, 0.99636, 6, -92.6, 52.64, 0.00364, 3, 3, 22.58, -60.93, 0.88916, 6, -60.39, 65.8, 0.10972, 7, -125.27, 59.58, 0.00112, 3, 3, -4.61, -69.27, 0.64712, 6, -32.63, 71.99, 0.33209, 7, -97.84, 67.07, 0.02079, 3, 3, -45.05, -73.61, 0.20891, 6, 8.03, 73.15, 0.61705, 7, -57.28, 70.15, 0.17405, 3, 3, -79.39, -75.11, 0.03328, 6, 42.38, 71.97, 0.47871, 7, -22.91, 70.59, 0.48801, 2, 6, 80.39, 63.19, 0.12715, 7, 15.47, 63.61, 0.87285, 2, 6, 109.27, 43.14, 0.00162, 7, 45.26, 44.95, 0.99838, 1, 7, 61.57, 21.45, 1, 1, 7, 53.19, 4.6, 1, 1, 7, 69.55, -12.65, 1, 2, 6, 111.55, -38.79, 0.07568, 7, 51.41, -36.79, 0.92432, 2, 6, 86.44, -59.14, 0.35635, 7, 27.28, -58.3, 0.64365, 3, 3, -102, 68.45, 0.00281, 6, 53.7, -72.92, 0.69821, 7, -4.77, -73.61, 0.29898, 3, 3, -98.57, -40.93, 0.00361, 6, 58.83, 36.39, 0.33407, 7, -4.8, 35.83, 0.66231, 2, 6, 51.07, -22.51, 0.78869, 7, -9.77, -23.38, 0.21131, 2, 3, -21.59, 13.54, 0.47136, 6, -22.17, -11.9, 0.52864, 3, 3, -20.3, -39.24, 0.57436, 6, -19.33, 40.82, 0.40347, 7, -83.08, 36.57, 0.02216, 1, 3, 33.81, -4.24, 1], "hull": 20, "edges": [12, 14, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4, 4, 6, 6, 8, 12, 10, 8, 10, 14, 16, 16, 18], "width": 264, "height": 183}}, "wang02_bianyuan": {"wang02_bianyuan": {"type": "mesh", "uvs": [0.87072, 0.40802, 0.90274, 0.3525, 0.93046, 0.5345, 0.89671, 0.68265, 0.82139, 0.79835, 0.73146, 0.88549, 0.64154, 0.95718, 0.54255, 0.99999, 0.44008, 1, 0.34352, 0.97303, 0.25856, 0.90231, 0.17906, 0.82661, 0.10542, 0.73441, 0.04582, 0.61444, 0, 0.45424, 0.03213, 0.42446, 0.07708, 0.56412, 0.13872, 0.67914, 0.205, 0.78077, 0.3555, 0.90798, 0.42638, 0.93998, 0.52951, 0.94079, 0.62509, 0.89958, 0.79779, 0.75526, 0.85215, 0.65543, 0.87971, 0.53331, 0.71774, 0.83634, 0.27153, 0.8497], "triangles": [5, 26, 4, 26, 23, 4, 26, 18, 23, 4, 23, 3, 23, 18, 24, 23, 24, 3, 3, 24, 2, 24, 17, 25, 24, 25, 2, 0, 25, 17, 2, 0, 1, 2, 25, 0, 9, 20, 8, 8, 21, 7, 8, 20, 21, 7, 21, 6, 9, 19, 20, 9, 10, 19, 21, 22, 6, 6, 22, 5, 22, 21, 19, 21, 20, 19, 10, 27, 19, 19, 27, 22, 10, 11, 27, 22, 26, 5, 22, 27, 26, 11, 18, 27, 27, 18, 26, 11, 12, 18, 12, 17, 18, 24, 18, 17, 12, 13, 17, 13, 16, 17, 13, 14, 16, 14, 15, 16, 17, 16, 0, 16, 15, 0], "vertices": [3, 18, -108.23, -179.55, 0.05035, 16, 170.3, -192.52, 0.00567, 15, 24.11, 11.62, 0.94398, 2, 18, -91.74, -194.76, 0.047, 15, 40.6, -3.59, 0.953, 2, 16, 132.73, -220.9, 0.00377, 15, -13.45, -16.76, 0.99623, 2, 16, 88.73, -204.87, 0.09986, 15, -57.45, -0.73, 0.90014, 2, 16, 54.37, -169.09, 0.27389, 15, -91.81, 35.05, 0.72611, 2, 16, 28.49, -126.37, 0.50823, 15, -117.7, 77.77, 0.49177, 2, 16, 7.2, -83.66, 0.73673, 15, -138.99, 120.48, 0.26327, 2, 16, -5.52, -36.64, 0.92743, 15, -151.7, 167.5, 0.07257, 2, 17, -170.74, -201.62, 0.00552, 16, -5.52, 12.03, 0.99448, 2, 17, -162.72, -155.75, 0.119, 16, 2.49, 57.9, 0.881, 2, 17, -141.72, -115.4, 0.29117, 16, 23.49, 98.26, 0.70883, 2, 17, -119.24, -77.64, 0.49129, 16, 45.98, 136.02, 0.50871, 2, 17, -91.85, -42.66, 0.6832, 16, 73.36, 170.99, 0.3168, 2, 17, -56.22, -14.35, 0.85485, 16, 108.99, 199.3, 0.14515, 2, 17, -8.64, 7.41, 0.99804, 16, 156.57, 221.07, 0.00196, 1, 17, 0.2, -7.85, 1, 2, 17, -41.28, -29.2, 0.88864, 16, 123.94, 184.46, 0.11136, 2, 17, -75.44, -58.48, 0.67267, 16, 89.78, 155.18, 0.32733, 3, 17, -105.62, -89.96, 0.44019, 16, 59.59, 123.69, 0.55958, 15, -86.59, 327.83, 0.00023, 3, 17, -143.41, -161.45, 0.15204, 16, 21.81, 52.21, 0.84621, 15, -124.38, 256.35, 0.00175, 3, 17, -152.91, -195.12, 0.0263, 16, 12.31, 18.54, 0.9721, 15, -133.88, 222.68, 0.0016, 3, 17, -153.15, -244.1, 0.00162, 16, 12.07, -30.45, 0.91038, 15, -134.12, 173.69, 0.088, 3, 17, -140.91, -289.5, 0.00101, 16, 24.3, -75.85, 0.76609, 15, -121.88, 128.29, 0.2329, 2, 16, 67.17, -157.88, 0.24051, 15, -79.02, 46.26, 0.75949, 2, 16, 96.82, -183.7, 0.15623, 15, -49.37, 20.44, 0.84377, 3, 18, -145.44, -183.82, 0.004, 16, 133.09, -196.79, 0.02675, 15, -13.1, 7.35, 0.96925, 2, 16, 43.09, -119.86, 0.44483, 15, -103.1, 84.28, 0.55517, 3, 17, -126.1, -121.56, 0.28717, 16, 39.12, 92.09, 0.71232, 15, -107.07, 296.23, 0.00051], "hull": 16, "edges": [2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 34, 36, 38, 40, 40, 42, 42, 44, 46, 48, 48, 50, 50, 0, 44, 52, 52, 46, 8, 10, 10, 12, 18, 20, 20, 22, 36, 54, 54, 38, 30, 0, 2, 0, 30, 28], "width": 475, "height": 297}}, "wang02_bianyuan2": {"wang02_bianyuan": {"type": "mesh", "uvs": [0.59682, 0.10806, 0.70332, 0.18506, 0.80937, 0.2405, 0.90274, 0.3525, 0.87072, 0.40802, 0, 0.45424, 0, 0.26333, 0.04711, 0.1134, 0.14984, 0.08464, 0.26799, 0.05178, 0.3733, 0, 0.49177, 0, 0.0835, 0.16978, 0.03727, 0.26631, 0.03213, 0.42446, 0.80779, 0.30738, 0.69863, 0.25604, 0.59076, 0.17388, 0.4816, 0.07324, 0.38657, 0.06297, 0.28127, 0.11227, 0.15156, 0.14924], "triangles": [4, 16, 15, 4, 14, 16, 4, 15, 3, 15, 2, 3, 15, 16, 2, 16, 1, 2, 5, 14, 4, 5, 6, 14, 20, 17, 14, 14, 21, 20, 21, 13, 12, 21, 14, 13, 14, 6, 13, 13, 6, 12, 6, 7, 12, 21, 12, 8, 12, 7, 8, 21, 8, 20, 8, 9, 20, 14, 17, 16, 18, 17, 19, 19, 17, 20, 16, 17, 1, 17, 0, 1, 17, 18, 0, 20, 9, 19, 18, 11, 0, 18, 19, 11, 9, 10, 19, 19, 10, 11], "vertices": [3, 18, -19.14, -49.44, 0.79155, 16, 259.39, -62.42, 0.03824, 15, 113.2, 141.72, 0.17022, 3, 18, -42.01, -100.03, 0.45535, 16, 236.52, -113.01, 0.05647, 15, 90.33, 91.13, 0.48818, 3, 18, -58.48, -150.4, 0.206, 16, 220.05, -163.38, 0.02588, 15, 73.87, 40.76, 0.76812, 2, 18, -91.74, -194.76, 0.047, 15, 40.6, -3.59, 0.953, 3, 18, -108.23, -179.55, 0.05035, 16, 170.3, -192.52, 0.00567, 15, 24.11, 11.62, 0.94398, 2, 17, -8.64, 7.41, 0.99804, 16, 156.57, 221.07, 0.00196, 2, 18, -65.26, 234.04, 0.05132, 17, 48.06, 7.41, 0.94868, 2, 18, -20.73, 211.67, 0.13468, 17, 92.59, -14.96, 0.86532, 3, 18, -12.19, 162.87, 0.27542, 17, 101.13, -63.76, 0.71476, 16, 266.34, 149.89, 0.00982, 3, 18, -2.43, 106.75, 0.56631, 17, 110.89, -119.88, 0.41308, 16, 276.1, 93.77, 0.02061, 3, 18, 12.95, 56.73, 0.82884, 17, 126.26, -169.9, 0.16351, 16, 291.48, 43.75, 0.00765, 1, 18, 12.95, 0.45, 1, 3, 18, -37.47, 194.38, 0.14964, 17, 75.84, -32.25, 0.84753, 16, 241.06, 181.41, 0.00283, 3, 18, -66.14, 216.34, 0.06603, 17, 47.17, -10.29, 0.93077, 16, 212.39, 203.37, 0.0032, 1, 17, 0.2, -7.85, 1, 3, 18, -78.34, -149.66, 0.17327, 16, 200.19, -162.63, 0.02459, 15, 54, 41.51, 0.80214, 3, 18, -63.09, -97.81, 0.37503, 16, 215.44, -110.78, 0.05792, 15, 69.25, 93.36, 0.56705, 4, 18, -38.69, -46.57, 0.71838, 17, 74.62, -273.2, 0.0015, 16, 239.84, -59.54, 0.05306, 15, 93.65, 144.6, 0.22706, 3, 18, -8.8, 5.28, 0.9653, 17, 104.51, -221.35, 0.03052, 16, 269.73, -7.69, 0.00418, 4, 18, -5.75, 50.42, 0.84263, 17, 107.56, -176.21, 0.14289, 16, 272.78, 37.45, 0.0143, 15, 126.59, 241.59, 0.00019, 4, 18, -20.39, 100.44, 0.56094, 17, 92.92, -126.19, 0.40066, 16, 258.14, 87.47, 0.03811, 15, 111.95, 291.61, 0.00029, 3, 18, -31.37, 162.05, 0.22286, 17, 81.94, -64.58, 0.76912, 16, 247.16, 149.08, 0.00802], "hull": 12, "edges": [22, 0, 0, 2, 2, 4, 4, 6, 10, 12, 12, 14, 14, 16, 16, 18, 20, 22, 18, 20, 24, 26, 26, 28, 8, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 24, 14, 24, 30, 4, 28, 8, 6, 8, 8, 10], "width": 475, "height": 297}}, "wang02_neiceng01": {"wang02_neiceng01": {"type": "mesh", "uvs": [0.67257, 0.14345, 0.7804, 0.30916, 0.86182, 0.53094, 0.93334, 0.74254, 1, 0.90314, 1, 1, 0.93554, 1, 0.83573, 1, 0.74589, 0.85456, 0.64234, 0.69951, 0.51842, 0.57241, 0.40864, 0.49889, 0.30005, 0.51634, 0.19256, 0.57348, 0.08806, 0.66125, 0, 0.67784, 0, 0.56092, 0.04205, 0.43345, 0.11467, 0.30599, 0.2115, 0.15303, 0.30502, 0.04341, 0.42936, 0, 0.56804, 0], "triangles": [14, 16, 17, 15, 16, 14, 13, 14, 17, 18, 13, 17, 7, 8, 3, 6, 4, 5, 6, 7, 4, 4, 7, 3, 19, 20, 11, 8, 9, 2, 10, 0, 9, 19, 11, 12, 10, 11, 22, 12, 13, 18, 12, 18, 19, 2, 9, 1, 10, 22, 0, 21, 11, 20, 22, 11, 21, 1, 9, 0, 3, 8, 2], "vertices": [3, 19, -14.3, -52.58, 0.92546, 18, 50.58, -61.23, 0.0477, 15, 182.92, 129.93, 0.02685, 3, 19, -37.83, -88.06, 0.7411, 18, 27.05, -96.71, 0.15325, 15, 159.4, 94.46, 0.10565, 3, 19, -69.32, -114.84, 0.70042, 18, -4.44, -123.49, 0.13698, 15, 127.9, 67.67, 0.16259, 3, 19, -99.36, -138.37, 0.38754, 18, -34.49, -147.02, 0.17847, 15, 97.86, 44.14, 0.43399, 2, 18, -57.3, -168.96, 0.136, 15, 75.05, 22.21, 0.864, 2, 18, -71.05, -168.96, 0.136, 15, 61.29, 22.21, 0.864, 2, 18, -71.05, -147.75, 0.264, 15, 61.29, 43.42, 0.736, 2, 18, -71.05, -114.91, 0.336, 15, 61.29, 76.26, 0.664, 3, 19, -115.27, -76.7, 0.0022, 18, -50.4, -85.35, 0.7237, 15, 81.95, 105.81, 0.2741, 1, 18, -28.38, -51.29, 1, 1, 18, -10.33, -10.52, 1, 1, 18, 0.11, 25.6, 1, 2, 18, -2.37, 61.33, 0.832, 17, 110.95, -165.3, 0.168, 3, 19, -75.36, 105.34, 0.3951, 18, -10.48, 96.69, 0.2449, 17, 102.83, -129.94, 0.36, 3, 19, -87.82, 139.72, 0.2133, 18, -22.95, 131.07, 0.1227, 17, 90.37, -95.56, 0.664, 3, 19, -90.18, 168.69, 0.05608, 18, -25.3, 160.04, 0.03192, 17, 88.01, -66.59, 0.912, 3, 19, -73.57, 168.69, 0.05608, 18, -8.7, 160.04, 0.03192, 17, 104.62, -66.59, 0.912, 3, 19, -55.47, 154.86, 0.21393, 18, 9.4, 146.21, 0.12207, 17, 122.72, -80.42, 0.664, 3, 19, -37.38, 130.97, 0.40883, 18, 27.5, 122.32, 0.23117, 17, 140.81, -104.31, 0.36, 3, 19, -15.66, 99.11, 0.56087, 18, 49.22, 90.46, 0.27113, 17, 162.53, -136.17, 0.168, 2, 19, -0.09, 68.34, 0.77272, 18, 64.79, 59.69, 0.22728, 2, 19, 6.07, 27.44, 0.99839, 18, 70.95, 18.79, 0.00161, 2, 19, 6.07, -18.19, 0.98958, 18, 70.95, -26.84, 0.01042], "hull": 23, "edges": [30, 28, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 10, 12, 12, 14, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 44, 42, 44, 42, 40, 40, 38, 38, 36, 36, 34, 30, 32, 34, 32, 28, 26, 26, 24, 42, 22, 44, 20, 0, 18, 4, 16, 6, 14, 34, 28], "width": 329, "height": 142}}, "wang02_neiceng02": {"wang02_neiceng02": {"type": "mesh", "uvs": [0.71018, 0.08695, 0.80565, 0.18782, 0.88835, 0.30265, 0.9596, 0.42973, 1, 0.56446, 1, 0.69738, 0.9456, 0.79537, 0.84382, 0.88111, 0.73313, 0.94848, 0.61836, 1, 0.43887, 1, 0.32378, 0.97614, 0.23439, 0.91564, 0.14053, 0.82823, 0.06455, 0.72201, 0, 0.60503, 0, 0.48535, 0, 0.33039, 0.12874, 0.24618, 0.22544, 0.14973, 0.32849, 0.06858, 0.44045, 0, 0.59059, 0, 0.17923, 0.55245, 0.82731, 0.63829, 0.48022, 0.13983, 0.38868, 0.83406, 0.69522, 0.33826, 0.51942, 0.58548, 0.37134, 0.33532, 0.65509, 0.81456], "triangles": [28, 29, 27, 23, 29, 28, 28, 27, 24, 30, 28, 24, 26, 23, 28, 19, 20, 29, 25, 21, 22, 20, 21, 25, 29, 20, 25, 0, 25, 22, 27, 0, 1, 27, 25, 0, 29, 25, 27, 27, 1, 2, 23, 16, 17, 23, 18, 29, 23, 17, 18, 15, 16, 23, 14, 15, 23, 13, 14, 23, 13, 23, 26, 18, 19, 29, 26, 28, 30, 12, 13, 26, 8, 30, 7, 11, 12, 26, 10, 26, 30, 9, 10, 30, 11, 26, 10, 8, 9, 30, 3, 24, 27, 3, 27, 2, 24, 3, 4, 24, 4, 5, 6, 24, 5, 7, 30, 24, 6, 7, 24], "vertices": [3, 15, 176.51, 116.67, 0.04408, 19, -20.71, -65.84, 0.95296, 20, 140.2, -97.69, 0.00296, 3, 15, 140.81, 76, 0.10861, 19, -56.41, -106.51, 0.8867, 20, 104.5, -138.36, 0.00469, 3, 15, 100.16, 40.77, 0.44794, 19, -97.06, -141.74, 0.54406, 20, 63.85, -173.59, 0.008, 3, 15, 55.17, 10.42, 0.89741, 19, -142.05, -172.09, 0.09459, 20, 18.86, -203.94, 0.008, 2, 15, 7.48, -6.79, 0.99945, 19, -189.74, -189.31, 0.00055, 2, 15, -39.58, -6.79, 0.95618, 16, 106.61, -210.93, 0.04382, 2, 15, -74.27, 16.38, 0.85111, 16, 71.92, -187.76, 0.14889, 2, 15, -104.62, 59.74, 0.63003, 16, 41.57, -144.4, 0.36997, 2, 15, -128.47, 106.9, 0.34874, 16, 17.72, -97.24, 0.65126, 2, 15, -146.71, 155.79, 0.11379, 16, -0.52, -48.35, 0.88621, 2, 16, -0.52, 28.11, 0.99941, 17, -165.74, -185.54, 0.00059, 2, 16, 7.92, 77.14, 0.86292, 17, -157.29, -136.52, 0.13708, 2, 16, 29.34, 115.22, 0.67499, 17, -135.87, -98.44, 0.32501, 2, 16, 60.28, 155.2, 0.40968, 17, -104.93, -58.45, 0.59032, 2, 16, 97.89, 187.57, 0.17544, 17, -67.33, -26.08, 0.82456, 3, 16, 139.3, 215.07, 0.02834, 17, -25.91, 1.41, 0.94912, 20, -43.19, 204.84, 0.02253, 2, 17, 16.45, 1.41, 0.9993, 19, -161.74, 236.69, 0.0007, 3, 17, 71.31, 1.41, 0.8907, 19, -106.88, 236.69, 0.03319, 20, 54.03, 204.84, 0.07611, 3, 17, 101.12, -53.43, 0.78769, 19, -77.07, 181.85, 0.17231, 20, 83.84, 150, 0.04, 3, 17, 135.26, -94.62, 0.49004, 19, -42.93, 140.66, 0.46996, 20, 117.98, 108.81, 0.04, 3, 17, 163.99, -138.52, 0.13599, 19, -14.2, 96.76, 0.82401, 20, 146.71, 64.91, 0.04, 3, 17, 188.26, -186.22, 0.01116, 19, 10.07, 49.06, 0.94884, 20, 170.99, 17.21, 0.04, 2, 15, 207.29, 167.62, 0.00376, 19, 10.07, -14.89, 0.99624, 4, 16, 157.91, 138.72, 0.10629, 17, -7.3, -74.94, 0.64561, 19, -185.49, 160.34, 0.00737, 20, -24.58, 128.49, 0.24073, 4, 15, -18.66, 66.77, 0.65285, 16, 127.53, -137.37, 0.16072, 19, -215.88, -115.74, 0.01481, 20, -54.97, -147.59, 0.17162, 4, 15, 157.79, 214.63, 0.00519, 17, 138.76, -203.16, 0.04355, 19, -39.43, 32.12, 0.71508, 20, 121.48, 0.27, 0.23619, 4, 15, -87.96, 253.63, 0.00314, 16, 58.22, 49.49, 0.69594, 17, -106.99, -164.16, 0.09612, 20, -124.27, 39.27, 0.2048, 4, 15, 87.55, 123.04, 0.19844, 16, 233.74, -81.1, 0.00193, 19, -109.67, -59.47, 0.49001, 20, 51.24, -91.32, 0.30962, 5, 15, 0.04, 197.94, 0.10261, 16, 146.22, -6.2, 0.17968, 17, -18.99, -219.86, 0.00306, 19, -197.18, 15.42, 0.00678, 20, -36.27, -16.43, 0.70787, 4, 16, 234.78, 56.88, 0.0021, 17, 69.56, -156.77, 0.15814, 19, -108.63, 78.51, 0.20366, 20, 52.28, 46.66, 0.6361, 3, 15, -81.06, 140.14, 0.24155, 16, 65.13, -64, 0.59538, 20, -117.37, -74.22, 0.16307], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 0], "width": 426, "height": 354}}, "weiba": {"weiba": {"type": "mesh", "uvs": [0.1739, 0.21924, 0.37744, 0.13375, 0.57193, 0.05035, 0.80713, 0.06911, 0.93378, 0.21924, 1, 0.43192, 1, 0.6175, 0.92021, 0.81767, 0.78, 0.89273, 0.55384, 0.86771, 0.34352, 0.78639, 0.1445, 0.68631, 0, 0.5612, 0, 0.31933, 0.66761, 0.6525, 0.28509, 0.46185, 0.64786, 0.30851], "triangles": [7, 8, 14, 8, 9, 14, 9, 10, 14, 7, 14, 6, 15, 16, 14, 6, 14, 5, 14, 16, 5, 16, 4, 5, 1, 2, 16, 16, 3, 4, 16, 2, 3, 11, 15, 10, 10, 15, 14, 11, 12, 15, 12, 13, 15, 13, 0, 15, 15, 1, 16, 15, 0, 1], "vertices": [1, 4, 5.1, 32.41, 1, 2, 4, 31.15, 45.32, 0.62983, 5, -13.59, 46.09, 0.37017, 2, 4, 56.04, 57.89, 0.26636, 5, 11.93, 57.35, 0.73364, 2, 4, 86.69, 56.25, 0.07013, 5, 42.45, 54.09, 0.92987, 2, 4, 103.83, 35.63, 0.00737, 5, 58.48, 32.6, 0.99263, 1, 5, 66.49, 2.44, 1, 1, 5, 65.97, -23.72, 1, 2, 4, 104.82, -48.76, 0.01237, 5, 55.03, -51.73, 0.98763, 2, 4, 86.95, -59.94, 0.05906, 5, 36.6, -61.95, 0.94094, 2, 4, 57.45, -57.37, 0.27437, 5, 7.27, -57.83, 0.72563, 2, 4, 29.75, -46.8, 0.70124, 5, -19.83, -45.82, 0.29876, 1, 4, 3.43, -33.54, 1, 1, 4, -15.92, -16.52, 1, 1, 4, -17.03, 17.56, 1, 2, 4, 71.24, -26.56, 0.08117, 5, 22.67, -27.79, 0.91883, 1, 4, 20.66, -1.31, 1, 2, 4, 67.09, 21.83, 0.11251, 5, 21.07, 20.76, 0.88749], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 0], "width": 130, "height": 141}}, "yanjing": {"biyan": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-24.83, 21.33, 21.15, 22.63, 22.28, -17.35, -23.7, -18.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 46, "height": 40}, "yanjing": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-21.84, 21.41, 18.15, 22.55, 19.28, -17.44, -20.7, -18.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 40, "height": 40}}, "yanpi": {"yanpi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-24.24, 0.34, 21.74, 1.64, 22.33, -19.35, -23.65, -20.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 46, "height": 21}}, "yuqi1": {"yuqi1": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.56, -47.7, -37.88, 28.72, 5.66, 51.19, 45.11, -25.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 49}}, "yuqi2": {"yuqi2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [4.27, -44.51, -38.93, 11.84, 4.72, 45.3, 47.92, -11.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 71, "height": 55}}, "yuqi3": {"yuqi3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [51.74, 0.46, 12.85, -38.43, -22.51, -3.07, 16.38, 35.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 55, "height": 50}}, "yushou": {"yushou": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.13, -55.04, -31.69, -5.24, 26.29, 46.94, 71.11, -2.86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 78}}, "yuzui": {"yuzui": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-8.93, 4.5, 7.5, 21.99, 35.93, -4.71, 19.5, -22.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 39}}}}, "animations": {"idle": {"slots": {"shenti": {"color": [{"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "wang02_bianyuan": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "wang02_bianyuan"}, {"time": 1.9, "name": "wang02_bianyuan"}]}, "wang02_bianyuan2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "wang02_neiceng01": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "wang02_neiceng01"}]}, "wang02_neiceng02": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "wang02_neiceng02"}, {"time": 1.9, "name": "wang02_neiceng02"}]}, "weiba": {"color": [{"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "yanjing": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 1.8333, "name": "biyan"}]}, "yanpi": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 1.8333, "name": null}]}, "yuqi1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "yuqi2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "yuqi3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "yushou": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}, "yuzui": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}]}}, "bones": {"bone12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1}]}, "bone4": {"rotate": [{"time": 0, "angle": -3.68, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 0.2, "angle": 0}, {"time": 0.5, "angle": -5.54, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.6, "angle": -3.68, "curve": [0.328, 0.32, 0.669, 0.67]}, {"time": 0.9, "angle": -5.35, "curve": [0.355, 0.41, 0.698, 0.78]}, {"time": 1.1667, "angle": 50.51, "curve": [0.369, 0.63, 0.707, 1]}, {"time": 1.3333, "angle": -66.53}, {"time": 1.5667, "angle": 10}, {"time": 1.7333, "angle": 3.43}, {"time": 1.8, "angle": 26}, {"time": 2, "angle": -23.97}, {"time": 2.1, "angle": 31.62}, {"time": 2.2, "angle": -3.73}, {"time": 2.2333, "angle": -16.05}], "translate": [{"time": 0, "x": -7.71, "y": -0.75, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 0.2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -11.61, "y": -1.13, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.6, "x": -7.71, "y": -0.75}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 1.15, "curve": "stepped"}, {"time": 0.6, "angle": 1.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 24.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0.842, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 0.842, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone11": {"rotate": [{"time": 0, "angle": -3.61, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -7.23, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6, "angle": -91.14, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.9, "angle": 255.54, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.2, "angle": 19.08, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "angle": 266.1, "curve": [0.339, 0.35, 0.679, 0.7]}, {"time": 1.5333, "angle": -3.61, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 1.7333, "angle": 290.36, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.8667, "angle": 303.55, "curve": [0.339, 0.35, 0.679, 0.7]}, {"time": 2, "angle": 10.54, "curve": [0.382, 0.58, 0.731, 1]}, {"time": 2.2333, "angle": 297.09}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone18": {"rotate": [{"time": 0, "angle": -102.45}, {"time": 0.2, "angle": -100.92}, {"time": 0.4667, "angle": -150.6}, {"time": 1.2, "angle": -179.85, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4, "angle": 177.25, "curve": "stepped"}, {"time": 1.5667, "angle": 177.25, "curve": "stepped"}, {"time": 1.9, "angle": 177.25}, {"time": 2.2333, "angle": -158.44}], "translate": [{"time": 0, "x": 195.62, "y": -920.76}, {"time": 0.2, "x": 157.32, "y": -1006.44}, {"time": 0.4667, "x": -19.11, "y": -1069.68}, {"time": 0.6667, "x": -23.97, "y": -1038.89}, {"time": 0.9, "x": -18.28, "y": -942.27}, {"time": 1.2, "x": -12.6, "y": -944.66, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4, "x": -12.6, "y": -857.19}, {"time": 1.5667, "x": -14.03, "y": -805.1}, {"time": 1.7333, "x": -35.11, "y": -725.51}, {"time": 1.9, "x": -56.18, "y": -780.12}, {"time": 2.0667, "x": -22.94, "y": -765.95}, {"time": 2.2333, "x": 0.01, "y": -779.83}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.244, 0, 0.7, 0.79]}, {"time": 0.6, "angle": -4.29, "curve": [0.366, 0.63, 0.703, 1]}, {"time": 0.7333, "angle": -4.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -34.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 6.22, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4, "angle": 26.72, "curve": [0.25, 0, 0.883, 1]}, {"time": 1.5667, "angle": 24.75, "curve": [0.145, 0, 0.75, 1]}, {"time": 1.9, "angle": -36.36, "curve": [0.269, 0, 0.618, 0.42]}, {"time": 2.0667, "angle": -21.04, "curve": [0.328, 0.32, 0.664, 0.66]}, {"time": 2.2333, "angle": -7.31}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0, "y": -3.67, "curve": [0.242, 0, 0.667, 0.67]}, {"time": 0.6, "x": 9.29, "y": -10.22, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 0.7333, "x": 11.4, "y": -44.64, "curve": [0.244, 0, 0.641, 0.57]}, {"time": 0.9, "x": 29.83, "y": 80.5, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 1, "x": 39.15, "y": 112.45, "curve": [0.243, 0, 0.608, 0.43]}, {"time": 1.2, "x": -6.63, "y": 3.47, "curve": [0.095, 0, 0.576, 0.6]}, {"time": 1.3333, "x": -7.71, "y": 102.36, "curve": [0.394, 0.57, 0.762, 1]}, {"time": 1.4, "x": -8.06, "y": 111.84, "curve": [0.25, 0, 0.883, 1]}, {"time": 1.5667, "x": 2.6, "y": 60.18, "curve": [0.145, 0, 0.75, 1]}, {"time": 1.7333, "x": 15.17, "y": 139.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 27.74, "y": 178.24, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.0667, "x": 16.89, "y": 216.98, "curve": [0.261, 0.36, 0.633, 0.72]}, {"time": 2.2333, "x": -12.49, "y": 290.1}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone10": {"rotate": [{"time": 0, "angle": 4.17, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 0.2, "angle": 0}, {"time": 0.5, "angle": 6.27, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.6, "angle": -65.43, "curve": [0.371, 0.63, 0.71, 1]}, {"time": 0.9, "angle": -77.3, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 1.2, "angle": 26.86, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "angle": -67.17, "curve": [0.329, 0.32, 0.671, 0.68]}, {"time": 1.5333, "angle": 34.73, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 1.7333, "angle": -41.52, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 1.8667, "angle": -13.52, "curve": [0.329, 0.32, 0.671, 0.68]}, {"time": 2, "angle": -311.12, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 2.2333, "angle": -41.06}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 1.5333, "x": -0.36, "y": -10.02, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": [0.258, 0, 0.619, 0.45]}, {"time": 2, "x": -0.36, "y": -10.02, "curve": [0.363, 0.44, 0.755, 1]}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.252, 0, 0.622, 0.48]}, {"time": 0.3, "x": 0.865, "y": 1, "curve": [0.372, 0.48, 0.752, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone22": {"rotate": [{"time": 0.3333, "angle": 28.7, "curve": [0.117, 0.33, 0.75, 1]}, {"time": 0.5667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0}, {"time": 1.9333, "angle": 17.15}, {"time": 2.2333, "angle": 0}], "translate": [{"time": 0, "x": 58.89, "y": 52.55}, {"time": 0.3333, "x": -23.98, "y": 20.14, "curve": [0.117, 0.33, 0.75, 1]}, {"time": 0.5667, "x": -87.08, "y": 9.09}, {"time": 0.6667, "x": -48.24, "y": 15.38}, {"time": 0.9, "x": -100.62, "y": 6.88}, {"time": 1, "x": -145.3, "y": 25.45, "curve": [0.25, 0, 0.874, 1]}, {"time": 1.2, "x": -109.03, "y": 12.95, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": -94.02, "y": 6.26}, {"time": 1.6, "x": -67.91, "y": -4.42}, {"time": 1.9333, "x": -91.86, "y": -11.68}, {"time": 2.2333, "x": -100.62, "y": 6.88}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.2, "x": 1.134, "y": 1.134, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone9": {"rotate": [{"time": 0, "angle": -1.88, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.1667, "angle": 0}, {"time": 0.4667, "angle": -3.75, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.6, "angle": -1.88, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.7333, "angle": -3.75, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.9, "angle": -1.88, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "angle": 0, "curve": [0.25, 0, 0.874, 1]}, {"time": 1.2, "angle": -1.88, "curve": "stepped"}, {"time": 1.3333, "angle": -1.88, "curve": "stepped"}, {"time": 1.4333, "angle": -1.88, "curve": "stepped"}, {"time": 1.5667, "angle": -1.88, "curve": "stepped"}, {"time": 1.7333, "angle": -1.88, "curve": "stepped"}, {"time": 1.8667, "angle": -1.88, "curve": "stepped"}, {"time": 1.9333, "angle": -1.88, "curve": "stepped"}, {"time": 2.0667, "angle": -1.88, "curve": "stepped"}, {"time": 2.2333, "angle": -1.88}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0.911, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 0.911, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone19": {"rotate": [{"time": 0.3333, "angle": 72.2, "curve": [0.156, 0.3, 0.58, 0.71]}, {"time": 0.5, "angle": -15.89, "curve": [0.383, 0.59, 0.763, 1]}, {"time": 0.6667, "angle": -37.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -58.76, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "angle": -37.19, "curve": [0.379, 0.53, 0.746, 1]}, {"time": 1.6, "angle": -16.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": -110.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "angle": 4.55}, {"time": 2.0667, "angle": -41.64}, {"time": 2.2333, "angle": -111.45}], "translate": [{"time": 0, "x": 151.14, "y": 6.57}, {"time": 0.3333, "x": 253.19, "y": 92.13, "curve": [0.156, 0.3, 0.58, 0.71]}, {"time": 0.5, "x": -1.01, "y": 4.55, "curve": [0.383, 0.59, 0.763, 1]}, {"time": 0.6667, "x": -189.2, "y": 49.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -87.02, "y": 92.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": -151.61, "y": 53.41, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": 13.43, "y": 87.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 132.06, "y": 39.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": -47.03, "y": 183.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": -25.57, "y": 144.62}, {"time": 2.0667, "x": -18.62, "y": 99.01}, {"time": 2.2333, "x": -140.12, "y": 113.01}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.7667, "x": 1, "y": 0.927, "curve": [0.338, 0.35, 0.677, 0.7]}, {"time": 1.9333, "x": 1, "y": 0.383, "curve": [0.382, 0.58, 0.733, 1]}, {"time": 2.2333, "x": 1, "y": 1}], "shear": [{"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": 0}, {"time": 1.7667, "x": 39.64, "y": 0}, {"time": 1.9333, "x": 0, "y": -21}, {"time": 2.2333, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": -0.96, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 0.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -2.85, "curve": [0.244, 0, 0.641, 0.57]}, {"time": 0.6, "angle": -0.96, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 0.7333, "angle": -2.85, "curve": [0.244, 0, 0.641, 0.57]}, {"time": 0.9, "angle": -18.84, "curve": [0.353, 0.4, 0.693, 0.76]}, {"time": 1.0667, "angle": 15.46, "curve": [0.371, 0.62, 0.71, 1]}, {"time": 1.2, "angle": -0.96, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "angle": -34.38, "curve": [0.346, 0.38, 0.683, 0.72]}, {"time": 1.5667, "angle": 31.82, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 1.7333, "angle": 23.47, "curve": [0.346, 0.38, 0.683, 0.72]}, {"time": 1.8, "angle": 21.3, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 1.9333, "angle": -45.1, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 2.1, "angle": 10.81, "curve": [0.359, 0.46, 0.697, 0.82]}, {"time": 2.2, "angle": -31.46, "curve": [0.354, 0.65, 0.689, 1]}, {"time": 2.2333, "angle": -9.67}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.957, "y": 1, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0.873, "y": 1, "curve": [0.244, 0, 0.641, 0.57]}, {"time": 0.6, "x": 0.957, "y": 1, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 0.7333, "x": 0.873, "y": 1, "curve": [0.244, 0, 0.641, 0.57]}, {"time": 0.9, "x": 0.621, "y": 1, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 1.2, "x": 0.957, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 0.957, "y": 1, "curve": [0.346, 0.38, 0.683, 0.72]}, {"time": 1.5667, "x": 0.665, "y": 1, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 1.7333, "x": 0.851, "y": 1, "curve": [0.382, 0.56, 0.74, 1]}, {"time": 1.9333, "x": 0.708, "y": 1, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 2.1, "x": 1.145, "y": 1, "curve": [0.377, 0.61, 0.72, 1]}, {"time": 2.2333, "x": 0.957, "y": 1}]}, "bone20": {"rotate": [{"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -20.9, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.6, "angle": 21.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 10.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "angle": 0}], "translate": [{"time": 0, "x": 84.04, "y": -69.13}, {"time": 0.3333, "x": 59.9, "y": 0, "curve": [0.114, 0.28, 0.556, 0.7]}, {"time": 0.5, "x": 76.1, "y": -3.5, "curve": [0.384, 0.58, 0.769, 1]}, {"time": 0.6667, "x": -69.56, "y": 43.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -8.13, "y": 58.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 72.16, "y": 29.29, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": 97.2, "y": 21.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": 174.36, "y": -59.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 150.34, "y": 106.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "x": -38.2, "y": -48.89}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.129, "y": 1, "curve": [0.379, 0.53, 0.746, 1]}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 4.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1}]}, "bone24": {"rotate": [{"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5667, "x": -29.13, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": -29.13, "y": 0}, {"time": 1.2, "x": -10.59, "y": 30.36, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": 26.9, "y": -3.38}, {"time": 1.6, "x": -29.13, "y": 0}, {"time": 1.9333, "x": -42.7, "y": 17.93}, {"time": 2.2333, "x": -130.73, "y": -24.84}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone5": {"rotate": [{"time": 0, "angle": -1.79, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 0.1333, "angle": -3.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 0, "curve": [0.246, 0, 0.633, 0.54]}, {"time": 0.6, "angle": -14.9, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 0.7333, "angle": 0, "curve": [0.246, 0, 0.633, 0.54]}, {"time": 0.9, "angle": 48.77, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 1, "angle": -3.01, "curve": [0.25, 0, 0.874, 1]}, {"time": 1.2, "angle": -1.79, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "angle": 28.33, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 1.4333, "angle": -4.78}, {"time": 1.5667, "angle": -12.04}, {"time": 1.7333, "angle": -1.79, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 1.8667, "angle": 11.36, "curve": [0.348, 0.38, 0.688, 0.74]}, {"time": 1.9333, "angle": -4.78}, {"time": 2.0667, "angle": -12.04}, {"time": 2.2333, "angle": 3.27}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.051, "y": 1.026, "curve": "stepped"}, {"time": 0.6, "x": 1.051, "y": 1.026, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone21": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.165, 0.32, 0.586, 0.72]}, {"time": 0.5, "angle": 7.02, "curve": [0.383, 0.59, 0.761, 1]}, {"time": 0.6667, "angle": 24.03, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.9, "angle": 11.53, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.2, "angle": 18.23, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "angle": 31.32, "curve": [0.379, 0.53, 0.746, 1]}, {"time": 1.6, "angle": 83.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 51.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "angle": 37.48}, {"time": 2.2333, "angle": 88.54}], "translate": [{"time": 0, "x": -126.07, "y": -36.44}, {"time": 0.3333, "x": -88.89, "y": -28.84, "curve": [0.165, 0.32, 0.586, 0.72]}, {"time": 0.5, "x": -57.04, "y": 98.16, "curve": [0.383, 0.59, 0.761, 1]}, {"time": 0.6667, "x": -91.88, "y": 42.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -14.18, "y": -13.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": -6.12, "y": -0.06, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.4333, "x": -1.9, "y": -55.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "x": -26.3, "y": -70.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 43.96, "y": -8.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "x": -70.02, "y": -181.15}, {"time": 2.2333, "x": -192.77, "y": -221.79}], "scale": [{"time": 0.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.6, "x": 0.413, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.6, "x": -29.43, "y": -47.01}, {"time": 2.0667, "x": 29.23, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 8.57, "curve": [0.276, 0, 0.621, 0.4]}, {"time": 0.7333, "angle": 18.01, "curve": [0.333, 0.33, 0.758, 1]}, {"time": 0.9, "angle": -29.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 0, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "angle": -28.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 3.69}, {"time": 1.5667, "angle": 31.68}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -33.64, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "angle": 3.69}, {"time": 2.1, "angle": 2.54}, {"time": 2.2333, "angle": -23.19}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0.925, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 0.925, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone23": {"rotate": [{"time": 0.1667, "angle": 20.04}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0}, {"time": 0.9, "angle": -34.92}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0}], "translate": [{"time": 0, "x": -67.77, "y": -9.39}, {"time": 0.1667, "x": -33.13, "y": 90.61}, {"time": 0.3333, "x": 63.6, "y": 40.02, "curve": [0.199, 0.26, 0.56, 0.62]}, {"time": 0.4667, "x": 122.64, "y": 152.94, "curve": [0.295, 0.35, 0.661, 0.73]}, {"time": 0.6667, "x": -1.57, "y": -14.35}, {"time": 1, "x": 49.43, "y": -92.4, "curve": [0.25, 0, 0.874, 1]}, {"time": 1.2, "x": 51.61, "y": -42.68, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "x": 116.69, "y": -61.21}, {"time": 1.4333, "x": 106, "y": -68.76}, {"time": 1.5667, "x": 141.9, "y": -55.6}, {"time": 1.9333, "x": 124.97, "y": 63.96}, {"time": 2.2333, "x": 7.88, "y": -9.87}], "scale": [{"time": 0.3333, "x": 1, "y": 1}, {"time": 0.6667, "x": 1, "y": 1.634}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -4.77, "curve": "stepped"}, {"time": 0.6, "angle": -4.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 33.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 0, "curve": [0.093, 0, 0.75, 1]}, {"time": 1.3333, "angle": 5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 4.11}, {"time": 1.5667, "angle": -4.07}, {"time": 1.7333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": 58.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "angle": -4.07}, {"time": 2.2333, "angle": 0.08}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.9, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.0667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1}]}}, "deform": {"default": {"wang02_bianyuan": {"wang02_bianyuan": [{"time": 0, "curve": "stepped"}, {"time": 0.4667}]}, "wang02_bianyuan2": {"wang02_bianyuan": [{"time": 0, "curve": "stepped"}, {"time": 0.4667}]}, "wang02_neiceng01": {"wang02_neiceng01": [{"time": 0}, {"time": 0.3333, "offset": 24, "vertices": [-13.32269, 0.99091, -12.3101, 5.19011, -5.29828, -14.75691, -9.72998, -12.29488, 0, 0, 0, 0, 0, 0, 0, 0, 24.39655, 3.01972, 24.39673, 3.0197, 24.0851, -4.92255, 24.10464, 5.38068, 0, 0, 3.8374, 1.27407, 0, 0, 0, 0, -12.68185, 5.62195, -12.68178, 5.62191, -13.82318, -1.15897, 16.93179, 11.6844, 16.93188, 11.68438, 9.24002, 18.38052, 7.12885, 39.23698, 7.1292, 39.23689, -12.5904, 37.84012, 12.91464, 24.77106, 12.91487, 24.771, -0.56834, 27.93011, 11.73047, 21.4283, 11.73064, 21.42825, -0.00172, 24.4292, 4.52707, 2.15848, 4.52715, 2.15846, 2.93432, 4.0676, 4.13718, 5.30649, 4.1375, 5.30642, 1.08091, 6.64175]}, {"time": 0.4667}]}, "wang02_neiceng02": {"wang02_neiceng02": [{"time": 0}, {"time": 0.3333, "vertices": [10.10376, -7.49498, 11.96704, -3.87936, 12.35983, 2.34459, 18.29445, -9.16895, 20.2637, -2.8524, 19.14389, 7.22987, 17.24539, -10.50598, 19.69598, -4.45428, 19.4154, 5.55222, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12.38327, 19.21951, 20.09171, 10.9108, 12.38324, 19.21938, 4.80982, 12.82938, 10.37991, 8.94285, 4.80984, 12.82909]}, {"time": 0.4667}]}}}}}}