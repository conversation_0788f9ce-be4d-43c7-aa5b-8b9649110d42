import UBaseView from "../../../../framwork/widget/UBaseView";
import OKClub<PERSON>anger from "./OKClubManger";

const { ccclass, property } = cc._decorator;

const TAG = "[ItemClubAvator]";

/**
 * 俱乐部头像
 * @description 兼容系统的头像和自定义头像、圆形和非圆形
 */
@ccclass
export default class ItemClubAvator extends UBaseView {
    @property(cc.Sprite)
    clubAvatorImg: cc.Sprite = null;

    @property(cc.Sprite)
    clubSelectedStateImg: cc.Sprite = null;

    @property(cc.Button)
    btnChoice: cc.Button = null;

    @property(cc.Sprite)
    normalMask: cc.Sprite = null;

    @property
    avatorImgWidth: number = 200;

    @property
    avatorImgHeight: number = 200;

    @property([cc.SpriteFrame])
    protected iconList: cc.SpriteFrame[] = [];

    @property(cc.SpriteFrame)
    defaultAvator: cc.SpriteFrame = null;

    @property
    protected defaultSelected: boolean = false;

    private maskNode: cc.Node = null;
    /**
     * 俱乐部的图片地址：有可能是1、2、3，有可能是http开头的网址
     */
    private clubIconUrl: string = "";
    /**
     * 点击回调
     */
    private clickCallback: Function = null;
    /**
     * 是否可以点选
     */
    private isClickEnable: boolean = true;

    onUILoad() {
        this.clubAvatorImg.node.width = this.avatorImgWidth;
        this.clubAvatorImg.node.height = this.avatorImgHeight;
        this.clubSelectedStateImg.node.active = this.defaultSelected;

        this.onRegisterEvent(this.btnChoice.node, this.clickImageCallback.bind(this));
    }

    /**
     * 设置头像
     * @param id 头像ID
     * @param _clickCallback 点击回调
     */
    public setAvatorId(iconUrl: string, _clickCallback: Function) {
        this.clubIconUrl = iconUrl;
        this.clickCallback = _clickCallback;
        this.setCircleAvator(iconUrl, false, _clickCallback);
    }

    /**
     * 设置头像
     * @param id 可以是数字ID或图片URL
     * @param isCircle 是否设置为圆形
     * @param _clickCallback 点击回调
     *
     */
    public setCircleAvator(iconUrl: string | number, isCircle: boolean, _clickCallback: Function) {
        // 使用OKClubManager处理头像URL
        this.clubIconUrl = OKClubManger.getInstance().processAvatarUrl(iconUrl);
        this.clickCallback = _clickCallback;

        // 根据 isCircle 参数决定是否使用圆形遮罩
        if (isCircle) {
            if (!this.maskNode) {
                this.createCircleMask();
            }
        } else {
            // 如果不是圆形，移除遮罩
            if (this.maskNode) {
                this.removeCircleMask();
            }
        }

        // 检查是否为数字ID,如果是，则认为是系统的图标
        if (!isNaN(parseInt(this.clubIconUrl))) {
            // console.log(TAG, "准备加载的系统图标:", this.clubIconUrl);
            this.clubAvatorImg.spriteFrame = this.iconList[parseInt(this.clubIconUrl)];
        } else {
            // console.log(TAG, "准备加载的网络图片:", this.clubIconUrl);
            // 先显示默认头像作为加载状态
            if (this.clubAvatorImg && this.clubAvatorImg.isValid) {
                this.clubAvatorImg.spriteFrame = this.defaultAvator;
            }
            // 如果是图片URL，则加载网络图片
            let ext = this.getRemoteUrlExt(this.clubIconUrl);
            cc.assetManager.loadRemote(this.clubIconUrl, { ext }, (err, texture: cc.Texture2D) => {
                if (err) {
                    cc.log(TAG, "加载网络图片失败:", err);
                    if (this.clubAvatorImg && this.clubAvatorImg.isValid) {
                        this.clubAvatorImg.spriteFrame = this.defaultAvator;
                    }
                    return;
                }
                if (!this.clubAvatorImg || !this.clubAvatorImg.isValid) {
                    cc.log(TAG, "clubAvatorImg 组件无效或已被销毁");
                    return;
                }
                const spriteFrame = new cc.SpriteFrame();
                spriteFrame.setTexture(texture);
                this.clubAvatorImg.spriteFrame = spriteFrame;
            });
        }
    }

    private createCircleMask() {
        // 创建遮罩节点
        this.maskNode = new cc.Node("Mask");
        const mask = this.maskNode.addComponent(cc.Mask);
        mask.type = cc.Mask.Type.ELLIPSE;

        // 设置遮罩大小
        this.maskNode.width = this.avatorImgWidth;
        this.maskNode.height = this.avatorImgHeight;

        // 将遮罩节点添加到头像节点下
        this.clubAvatorImg.node.parent.addChild(this.maskNode);
        this.maskNode.setPosition(this.clubAvatorImg.node.position);

        // 将头像节点设置为遮罩的子节点
        this.clubAvatorImg.node.parent = this.maskNode;
        this.clubAvatorImg.node.setPosition(0, 0);
    }

    private removeCircleMask() {
        if (this.maskNode) {
            // 将头像节点移回原来的父节点
            const originalParent = this.maskNode.parent;
            this.clubAvatorImg.node.parent = originalParent;
            this.clubAvatorImg.node.setPosition(this.maskNode.position);

            // 移除遮罩节点
            this.maskNode.removeFromParent();
            this.maskNode = null;
        }
    }

    /**
     * 点选头像
     */
    clickImageCallback() {
        if (!this.isClickEnable) {
            cc.log(TAG, "头像不能点选");
            return;
        }
        if (this.clubSelectedStateImg.node.active) {
            // 重复选中nothing to do.
        } else {
            this.clubSelectedStateImg.node.active = true;
            this.clickCallback && this.clickCallback(this.clubIconUrl);
            this.setSelectedState(true);
        }
    }

    public setSelectedState(isSelected: boolean) {
        this.clubSelectedStateImg.node.active = isSelected;
    }

    public setClickEnable(isEnable: boolean) {
        this.isClickEnable = isEnable;
        this.clubSelectedStateImg.node.active = false;
    }

    public getClubUrl(): string {
        return this.clubIconUrl;
    }

    /**
     * 获取远程URL的文件扩展名
     */
    private getRemoteUrlExt(url: string): string {
        let pos = url.lastIndexOf(".");
        if (pos < 0) {
            return ".png";
        }
        let ext = url.substring(pos);
        let imageTypes = [
            ".png",
            ".jpg",
            ".jpeg",
            ".bmp",
            ".gif",
            ".webp",
            ".psd",
            ".svg",
            ".tiff",
        ];
        for (let i = 0; i < imageTypes.length; i++) {
            if (ext.startsWith(imageTypes[i])) {
                return imageTypes[i];
            }
        }
        return ".png";
    }

    /**
     * 是否显示正方形的边框
     * @param isShow 是否显示
     */
    public showNormalMask(isShow: boolean) {
        this.normalMask.node.active = isShow;
    }
}
