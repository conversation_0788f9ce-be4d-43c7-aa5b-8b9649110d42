import UIconSprite from "../../../../framwork/widget/UIconSprite";
const { ccclass, property } = cc._decorator;
@ccclass
export default class UIClubAvatar extends UIconSprite {

	@property(cc.Node)
	avatarBox: cc.Node = null;

	@property([cc.SpriteFrame])
	protected avatarSourceList: cc.SpriteFrame[] = [];
	onUILoad(): void {
	}

	setIcon(url: string) {
		if (!url || url.length == 0) return;
		if (parseInt(url) >= 0) {
			this.setIconByIndex(parseInt(url));
		} else {
			const match = url.match(/\/\/([^\/]+)\/(\d+)\?x-oss-process=image\/resize,w_120/);
			if (match) {
				this.setIconByIndex(parseInt(match[2]));
			} else {
				super.setIcon(url);
			}
		}
	}

	private setIconByIndex(index: number) {
		index = Math.max(0, Math.min(index, this.avatarSourceList.length - 1));
		this.sprite.spriteFrame = this.avatarSourceList[index];
	}

	setAvatarBoxVisibility(visibility: boolean) {
		this.avatarBox.active = visibility;
	}
}